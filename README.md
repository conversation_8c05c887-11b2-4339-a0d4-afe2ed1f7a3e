# UI-TARS 1.5-7B 本地部署套件

🤖 **UI-TARS 1.5-7B** 是ByteDance开发的多模态大语言模型，专门用于UI界面理解和交互。本套件提供完整的本地部署解决方案。

## ✨ 特性

- 🚀 **一键部署** - 自动安装所有依赖
- 🌐 **Web界面** - 友好的图形用户界面
- 💻 **命令行** - 适合开发者的CLI工具
- 🖼️ **图像分析** - 支持UI截图分析
- ⚡ **GPU加速** - 自动检测并使用GPU
- 🔧 **灵活配置** - 支持多种部署选项

## 📋 系统要求

### 硬件要求
- **GPU**: NVIDIA GPU，16GB+ 显存（推荐24GB+）
- **内存**: 32GB+ RAM
- **存储**: 50GB+ 可用空间

### 软件要求
- **Python**: 3.8 或更高版本
- **CUDA**: 11.8+ (GPU用户)
- **操作系统**: Windows 10/11, Linux, macOS

## 🚀 快速开始

### 方法一：一键启动（推荐）

**Windows用户：**
```bash
# 双击运行
启动UI-TARS.bat
```

**所有平台：**
```bash
python start_ui_tars.py
```

### 方法二：手动安装

1. **克隆或下载文件**
   ```bash
   # 确保以下文件在同一目录：
   # - start_ui_tars.py
   # - install_ui_tars.py
   # - ui_tars_web_interface.py
   # - ui_tars_local_deploy.py
   ```

2. **安装依赖**
   ```bash
   python install_ui_tars.py
   ```

3. **启动服务**
   ```bash
   # Web界面
   python ui_tars_web_interface.py
   
   # 命令行界面
   python ui_tars_local_deploy.py
   ```

## 📁 文件说明

| 文件名 | 描述 |
|--------|------|
| `start_ui_tars.py` | 主启动脚本，提供交互菜单 |
| `install_ui_tars.py` | 自动安装脚本 |
| `ui_tars_web_interface.py` | Web界面服务器 |
| `ui_tars_local_deploy.py` | 命令行部署脚本 |
| `启动UI-TARS.bat` | Windows一键启动脚本 |
| `UI-TARS_部署指南.md` | 详细部署指南 |

## 🌐 Web界面使用

1. 启动Web界面后访问：`http://localhost:7860`
2. 点击"加载模型"按钮
3. 输入问题或上传UI截图
4. 开始与AI对话

### 界面功能
- 📝 **文本对话** - 直接与AI交流
- 🖼️ **图像上传** - 分析UI截图
- ⚙️ **参数调节** - 自定义生成参数
- 📋 **对话历史** - 查看聊天记录
- 💾 **一键复制** - 复制AI回复

## 💻 命令行使用

```bash
python ui_tars_local_deploy.py

# 示例对话
请输入指令: 请分析这个界面的布局
请输入指令: 这个按钮的位置合适吗？ --image screenshot.png
```

## 🔧 高级配置

### 自定义启动参数
```bash
# 指定端口和主机
python ui_tars_web_interface.py --host 0.0.0.0 --port 8080

# 使用CPU运行
python ui_tars_local_deploy.py --device cpu

# 使用本地模型
python ui_tars_local_deploy.py --model_path ./local_models/UI-TARS-1.5-7B
```

### 环境变量
```bash
# 使用镜像源加速下载
export HF_ENDPOINT=https://hf-mirror.com

# GPU内存优化
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
```

## 🎯 使用示例

### 基本UI分析
```
用户: 请分析这个登录界面的设计
AI: 这个登录界面采用了简洁的设计风格，主要包含以下元素：
1. 用户名和密码输入框布局合理
2. 登录按钮位置突出
3. 建议增加忘记密码链接...
```

### 可访问性检查
```
用户: 这个界面有什么可访问性问题？
AI: 经过分析，发现以下可访问性问题：
1. 按钮缺少alt文本
2. 颜色对比度不足
3. 缺少键盘导航支持...
```

## 🔍 故障排除

### 常见问题

**Q: 模型下载失败？**
```bash
# 设置镜像源
export HF_ENDPOINT=https://hf-mirror.com
```

**Q: GPU内存不足？**
```bash
# 使用CPU模式
python start_ui_tars.py --device cpu
```

**Q: 依赖安装失败？**
```bash
# 重新创建虚拟环境
conda create -n ui-tars-new python=3.10
conda activate ui-tars-new
python install_ui_tars.py
```

## 📚 更多资源

- 📖 [详细部署指南](UI-TARS_部署指南.md)
- 🔗 [官方模型页面](https://huggingface.co/ByteDance-Seed/UI-TARS-1.5-7B)
- 💬 [GitHub Issues](https://github.com/bytedance/UI-TARS/issues)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个部署套件！

## 📄 许可证

本项目遵循MIT许可证。模型本身请参考官方许可证。

## 🙏 致谢

感谢ByteDance团队开发了优秀的UI-TARS模型！

---

**开始您的UI分析之旅吧！** 🚀
