{"version": 3, "file": "Index27-DbvnhMnQ.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index27.js"], "sourcesContent": ["import { create_ssr_component, validate_component, escape, add_attribute, globals } from \"svelte/internal\";\nimport { n as Block, S as Static, I as IconButtonWrapper, F as FullscreenButton, r as BlockTitle, k as Empty, v as <PERSON><PERSON><PERSON> } from \"./client.js\";\nimport { onMount } from \"svelte\";\nconst { Object: Object_1 } = globals;\nconst css = {\n  code: \"div.svelte-19qacdz{width:100%;height:100%}#vg-tooltip-element{font-family:var(--font) !important;font-size:var(--text-xs) !important;box-shadow:none !important;background-color:var(--block-background-fill) !important;border:1px solid var(--border-color-primary) !important;color:var(--body-text-color) !important}#vg-tooltip-element .key{color:var(--body-text-color-subdued) !important}.caption.svelte-19qacdz{padding:0 4px;margin:0;text-align:center}\",\n  map: '{\"version\":3,\"file\":\"Index.svelte\",\"sources\":[\"Index.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { BlockTitle } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Block } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { FullscreenButton, IconButtonWrapper } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { StatusTracker } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport { onMount } from \\\\\"svelte\\\\\";\\\\nimport { LineChart as LabelIcon } from \\\\\"@gradio/icons\\\\\";\\\\nimport { Empty } from \\\\\"@gradio/atoms\\\\\";\\\\nexport let value;\\\\nexport let x;\\\\nexport let y;\\\\nexport let color = null;\\\\nexport let root;\\\\n$: unique_colors = color && value && value.datatypes[color] === \\\\\"nominal\\\\\" ? Array.from(new Set(_data.map((d) => d[color]))) : [];\\\\nexport let title = null;\\\\nexport let x_title = null;\\\\nexport let y_title = null;\\\\nexport let color_title = null;\\\\nexport let x_bin = null;\\\\nexport let y_aggregate = void 0;\\\\nexport let color_map = null;\\\\nexport let x_lim = null;\\\\nexport let y_lim = null;\\\\n$: x_lim = x_lim || null;\\\\n$: y_lim = y_lim || null;\\\\n$: [x_start, x_end] = x_lim === null ? [void 0, void 0] : x_lim;\\\\n$: [y_start, y_end] = y_lim || [void 0, void 0];\\\\nexport let x_label_angle = null;\\\\nexport let y_label_angle = null;\\\\nexport let x_axis_labels_visible = true;\\\\nexport let caption = null;\\\\nexport let sort = null;\\\\nexport let tooltip = \\\\\"axis\\\\\";\\\\nexport let show_fullscreen_button = false;\\\\nlet fullscreen = false;\\\\nfunction reformat_sort(_sort2) {\\\\n    if (_sort2 === \\\\\"x\\\\\") {\\\\n        return \\\\\"ascending\\\\\";\\\\n    }\\\\n    else if (_sort2 === \\\\\"-x\\\\\") {\\\\n        return \\\\\"descending\\\\\";\\\\n    }\\\\n    else if (_sort2 === \\\\\"y\\\\\") {\\\\n        return { field: y, order: \\\\\"ascending\\\\\" };\\\\n    }\\\\n    else if (_sort2 === \\\\\"-y\\\\\") {\\\\n        return { field: y, order: \\\\\"descending\\\\\" };\\\\n    }\\\\n    else if (_sort2 === null) {\\\\n        return null;\\\\n    }\\\\n    else if (Array.isArray(_sort2)) {\\\\n        return _sort2;\\\\n    }\\\\n}\\\\n$: _sort = reformat_sort(sort);\\\\nexport let _selectable = false;\\\\nlet _data;\\\\nexport let gradio;\\\\n$: x_temporal = value && value.datatypes[x] === \\\\\"temporal\\\\\";\\\\n$: _x_lim = x_lim && x_temporal ? [x_lim[0] * 1e3, x_lim[1] * 1e3] : x_lim;\\\\nlet _x_bin;\\\\nlet mouse_down_on_chart = false;\\\\nconst SUFFIX_DURATION = {\\\\n    s: 1,\\\\n    m: 60,\\\\n    h: 60 * 60,\\\\n    d: 24 * 60 * 60\\\\n};\\\\n$: _x_bin = x_bin ? typeof x_bin === \\\\\"string\\\\\" ? 1e3 * parseInt(x_bin.substring(0, x_bin.length - 1)) * SUFFIX_DURATION[x_bin[x_bin.length - 1]] : x_bin : void 0;\\\\nlet _y_aggregate;\\\\nlet aggregating;\\\\n$: {\\\\n    if (value) {\\\\n        if (value.mark === \\\\\"point\\\\\") {\\\\n            aggregating = _x_bin !== void 0;\\\\n            _y_aggregate = y_aggregate || aggregating ? \\\\\"sum\\\\\" : void 0;\\\\n        }\\\\n        else {\\\\n            aggregating = _x_bin !== void 0 || value.datatypes[x] === \\\\\"nominal\\\\\";\\\\n            _y_aggregate = y_aggregate ? y_aggregate : \\\\\"sum\\\\\";\\\\n        }\\\\n    }\\\\n}\\\\nfunction reformat_data(data) {\\\\n    if (tooltip == \\\\\"all\\\\\" || Array.isArray(tooltip)) {\\\\n        return data.data.map((row) => {\\\\n            const obj = {};\\\\n            data.columns.forEach((col, i) => {\\\\n                obj[col] = row[i];\\\\n            });\\\\n            return obj;\\\\n        });\\\\n    }\\\\n    let x_index = data.columns.indexOf(x);\\\\n    let y_index = data.columns.indexOf(y);\\\\n    let color_index = color ? data.columns.indexOf(color) : null;\\\\n    return data.data.map((row) => {\\\\n        const obj = {\\\\n            [x]: row[x_index],\\\\n            [y]: row[y_index]\\\\n        };\\\\n        if (color && color_index !== null) {\\\\n            obj[color] = row[color_index];\\\\n        }\\\\n        return obj;\\\\n    });\\\\n}\\\\n$: _data = value ? reformat_data(value) : [];\\\\nlet old_value = value;\\\\n$: if (old_value !== value && view) {\\\\n    old_value = value;\\\\n    view.data(\\\\\"data\\\\\", _data).runAsync();\\\\n}\\\\nconst is_browser = typeof window !== \\\\\"undefined\\\\\";\\\\nlet chart_element;\\\\n$: computed_style = chart_element ? window.getComputedStyle(chart_element) : null;\\\\nlet view;\\\\nlet mounted = false;\\\\nlet old_width;\\\\nlet resizeObserver;\\\\nlet vegaEmbed;\\\\nasync function load_chart() {\\\\n    if (mouse_down_on_chart) {\\\\n        refresh_pending = true;\\\\n        return;\\\\n    }\\\\n    if (view) {\\\\n        view.finalize();\\\\n    }\\\\n    if (!value || !chart_element)\\\\n        return;\\\\n    old_width = chart_element.offsetWidth;\\\\n    const spec = create_vega_lite_spec();\\\\n    if (!spec)\\\\n        return;\\\\n    resizeObserver = new ResizeObserver((el) => {\\\\n        if (!el[0].target || !(el[0].target instanceof HTMLElement))\\\\n            return;\\\\n        if (old_width === 0 && chart_element.offsetWidth !== 0 && value.datatypes[x] === \\\\\"nominal\\\\\") {\\\\n            load_chart();\\\\n        }\\\\n        else {\\\\n            view.signal(\\\\\"width\\\\\", el[0].target.offsetWidth).run();\\\\n        }\\\\n    });\\\\n    if (!vegaEmbed) {\\\\n        vegaEmbed = (await import(\\\\\"vega-embed\\\\\")).default;\\\\n    }\\\\n    vegaEmbed(chart_element, spec, { actions: false }).then(function (result) {\\\\n        view = result.view;\\\\n        resizeObserver.observe(chart_element);\\\\n        var debounceTimeout;\\\\n        view.addEventListener(\\\\\"dblclick\\\\\", () => {\\\\n            gradio.dispatch(\\\\\"double_click\\\\\");\\\\n        });\\\\n        chart_element.addEventListener(\\\\\"mousedown\\\\\", function (e) {\\\\n            if (e.detail > 1) {\\\\n                e.preventDefault();\\\\n            }\\\\n        }, false);\\\\n        if (_selectable) {\\\\n            view.addSignalListener(\\\\\"brush\\\\\", function (_, value2) {\\\\n                mouse_down_on_chart = true;\\\\n                if (Object.keys(value2).length === 0)\\\\n                    return;\\\\n                clearTimeout(debounceTimeout);\\\\n                let range = value2[Object.keys(value2)[0]];\\\\n                if (x_temporal) {\\\\n                    range = [range[0] / 1e3, range[1] / 1e3];\\\\n                }\\\\n                debounceTimeout = setTimeout(function () {\\\\n                    mouse_down_on_chart = false;\\\\n                    gradio.dispatch(\\\\\"select\\\\\", {\\\\n                        value: range,\\\\n                        index: range,\\\\n                        selected: true\\\\n                    });\\\\n                    if (refresh_pending) {\\\\n                        refresh_pending = false;\\\\n                        load_chart();\\\\n                    }\\\\n                }, 250);\\\\n            });\\\\n        }\\\\n    });\\\\n}\\\\nlet refresh_pending = false;\\\\nonMount(() => {\\\\n    mounted = true;\\\\n    return () => {\\\\n        mounted = false;\\\\n        if (view) {\\\\n            view.finalize();\\\\n        }\\\\n        if (resizeObserver) {\\\\n            resizeObserver.disconnect();\\\\n        }\\\\n    };\\\\n});\\\\n$: _color_map = JSON.stringify(color_map);\\\\n$: title, x_title, y_title, color_title, x, y, color, x_bin, _y_aggregate, _color_map, x_start, x_end, y_start, y_end, caption, sort, mounted, chart_element, computed_style && requestAnimationFrame(load_chart);\\\\nfunction create_vega_lite_spec() {\\\\n    if (!value || !computed_style)\\\\n        return null;\\\\n    let accent_color = computed_style.getPropertyValue(\\\\\"--color-accent\\\\\");\\\\n    let body_text_color = computed_style.getPropertyValue(\\\\\"--body-text-color\\\\\");\\\\n    let borderColorPrimary = computed_style.getPropertyValue(\\\\\"--border-color-primary\\\\\");\\\\n    let font_family = computed_style.fontFamily;\\\\n    let title_weight = computed_style.getPropertyValue(\\\\\"--block-title-text-weight\\\\\");\\\\n    const font_to_px_val = (font) => {\\\\n        return font.endsWith(\\\\\"px\\\\\") ? parseFloat(font.slice(0, -2)) : 12;\\\\n    };\\\\n    let text_size_md = font_to_px_val(computed_style.getPropertyValue(\\\\\"--text-md\\\\\"));\\\\n    let text_size_sm = font_to_px_val(computed_style.getPropertyValue(\\\\\"--text-sm\\\\\"));\\\\n    return {\\\\n        $schema: \\\\\"https://vega.github.io/schema/vega-lite/v5.17.0.json\\\\\",\\\\n        background: \\\\\"transparent\\\\\",\\\\n        config: {\\\\n            autosize: { type: \\\\\"fit\\\\\", contains: \\\\\"padding\\\\\" },\\\\n            axis: {\\\\n                labelFont: font_family,\\\\n                labelColor: body_text_color,\\\\n                titleFont: font_family,\\\\n                titleColor: body_text_color,\\\\n                titlePadding: 8,\\\\n                tickColor: borderColorPrimary,\\\\n                labelFontSize: text_size_sm,\\\\n                gridColor: borderColorPrimary,\\\\n                titleFontWeight: \\\\\"normal\\\\\",\\\\n                titleFontSize: text_size_sm,\\\\n                labelFontWeight: \\\\\"normal\\\\\",\\\\n                domain: false,\\\\n                labelAngle: 0\\\\n            },\\\\n            legend: {\\\\n                labelColor: body_text_color,\\\\n                labelFont: font_family,\\\\n                titleColor: body_text_color,\\\\n                titleFont: font_family,\\\\n                titleFontWeight: \\\\\"normal\\\\\",\\\\n                titleFontSize: text_size_sm,\\\\n                labelFontWeight: \\\\\"normal\\\\\",\\\\n                offset: 2\\\\n            },\\\\n            title: {\\\\n                color: body_text_color,\\\\n                font: font_family,\\\\n                fontSize: text_size_md,\\\\n                fontWeight: title_weight,\\\\n                anchor: \\\\\"middle\\\\\"\\\\n            },\\\\n            view: { stroke: borderColorPrimary },\\\\n            mark: {\\\\n                stroke: value.mark !== \\\\\"bar\\\\\" ? accent_color : void 0,\\\\n                fill: value.mark === \\\\\"bar\\\\\" ? accent_color : void 0,\\\\n                cursor: \\\\\"crosshair\\\\\"\\\\n            }\\\\n        },\\\\n        data: { name: \\\\\"data\\\\\" },\\\\n        datasets: {\\\\n            data: _data\\\\n        },\\\\n        layer: [\\\\\"plot\\\\\", ...value.mark === \\\\\"line\\\\\" ? [\\\\\"hover\\\\\"] : []].map((mode) => {\\\\n            return {\\\\n                encoding: {\\\\n                    size: value.mark === \\\\\"line\\\\\" ? mode == \\\\\"plot\\\\\" ? {\\\\n                        condition: {\\\\n                            empty: false,\\\\n                            param: \\\\\"hoverPlot\\\\\",\\\\n                            value: 3\\\\n                        },\\\\n                        value: 2\\\\n                    } : {\\\\n                        condition: { empty: false, param: \\\\\"hover\\\\\", value: 100 },\\\\n                        value: 0\\\\n                    } : void 0,\\\\n                    opacity: mode === \\\\\"plot\\\\\" ? void 0 : {\\\\n                        condition: { empty: false, param: \\\\\"hover\\\\\", value: 1 },\\\\n                        value: 0\\\\n                    },\\\\n                    x: {\\\\n                        axis: {\\\\n                            ...x_label_angle !== null && { labelAngle: x_label_angle },\\\\n                            labels: x_axis_labels_visible,\\\\n                            ticks: x_axis_labels_visible\\\\n                        },\\\\n                        field: x,\\\\n                        title: x_title || x,\\\\n                        type: value.datatypes[x],\\\\n                        scale: _x_lim ? { domain: _x_lim } : void 0,\\\\n                        bin: _x_bin ? { step: _x_bin } : void 0,\\\\n                        sort: _sort\\\\n                    },\\\\n                    y: {\\\\n                        axis: y_label_angle ? { labelAngle: y_label_angle } : {},\\\\n                        field: y,\\\\n                        title: y_title || y,\\\\n                        type: value.datatypes[y],\\\\n                        scale: y_lim ? { domain: y_lim } : void 0,\\\\n                        aggregate: aggregating ? _y_aggregate : void 0\\\\n                    },\\\\n                    color: color ? {\\\\n                        field: color,\\\\n                        legend: { orient: \\\\\"bottom\\\\\", title: color_title },\\\\n                        scale: value.datatypes[color] === \\\\\"nominal\\\\\" ? {\\\\n                            domain: unique_colors,\\\\n                            range: color_map ? unique_colors.map((c) => color_map[c]) : void 0\\\\n                        } : {\\\\n                            range: [\\\\n                                100,\\\\n                                200,\\\\n                                300,\\\\n                                400,\\\\n                                500,\\\\n                                600,\\\\n                                700,\\\\n                                800,\\\\n                                900\\\\n                            ].map((n) => computed_style.getPropertyValue(\\\\\"--primary-\\\\\" + n)),\\\\n                            interpolate: \\\\\"hsl\\\\\"\\\\n                        },\\\\n                        type: value.datatypes[color]\\\\n                    } : void 0,\\\\n                    tooltip: tooltip == \\\\\"none\\\\\" ? void 0 : [\\\\n                        {\\\\n                            field: y,\\\\n                            type: value.datatypes[y],\\\\n                            aggregate: aggregating ? _y_aggregate : void 0,\\\\n                            title: y_title || y\\\\n                        },\\\\n                        {\\\\n                            field: x,\\\\n                            type: value.datatypes[x],\\\\n                            title: x_title || x,\\\\n                            format: x_temporal ? \\\\\"%Y-%m-%d %H:%M:%S\\\\\" : void 0,\\\\n                            bin: _x_bin ? { step: _x_bin } : void 0\\\\n                        },\\\\n                        ...color ? [\\\\n                            {\\\\n                                field: color,\\\\n                                type: value.datatypes[color]\\\\n                            }\\\\n                        ] : [],\\\\n                        ...tooltip === \\\\\"axis\\\\\" ? [] : value?.columns.filter((col) => col !== x && col !== y && col !== color && (tooltip === \\\\\"all\\\\\" || tooltip.includes(col))).map((column) => ({\\\\n                            field: column,\\\\n                            type: value.datatypes[column]\\\\n                        }))\\\\n                    ]\\\\n                },\\\\n                strokeDash: {},\\\\n                mark: { clip: true, type: mode === \\\\\"hover\\\\\" ? \\\\\"point\\\\\" : value.mark },\\\\n                name: mode\\\\n            };\\\\n        }),\\\\n        // @ts-ignore\\\\n        params: [\\\\n            ...value.mark === \\\\\"line\\\\\" ? [\\\\n                {\\\\n                    name: \\\\\"hoverPlot\\\\\",\\\\n                    select: {\\\\n                        clear: \\\\\"mouseout\\\\\",\\\\n                        fields: color ? [color] : [],\\\\n                        nearest: true,\\\\n                        on: \\\\\"mouseover\\\\\",\\\\n                        type: \\\\\"point\\\\\"\\\\n                    },\\\\n                    views: [\\\\\"hover\\\\\"]\\\\n                },\\\\n                {\\\\n                    name: \\\\\"hover\\\\\",\\\\n                    select: {\\\\n                        clear: \\\\\"mouseout\\\\\",\\\\n                        nearest: true,\\\\n                        on: \\\\\"mouseover\\\\\",\\\\n                        type: \\\\\"point\\\\\"\\\\n                    },\\\\n                    views: [\\\\\"hover\\\\\"]\\\\n                }\\\\n            ] : [],\\\\n            ..._selectable ? [\\\\n                {\\\\n                    name: \\\\\"brush\\\\\",\\\\n                    select: {\\\\n                        encodings: [\\\\\"x\\\\\"],\\\\n                        mark: { fill: \\\\\"gray\\\\\", fillOpacity: 0.3, stroke: \\\\\"none\\\\\" },\\\\n                        type: \\\\\"interval\\\\\"\\\\n                    },\\\\n                    views: [\\\\\"plot\\\\\"]\\\\n                }\\\\n            ] : []\\\\n        ],\\\\n        width: chart_element.offsetWidth,\\\\n        height: height ? \\\\\"container\\\\\" : void 0,\\\\n        title: title || void 0\\\\n    };\\\\n}\\\\nexport let label = \\\\\"Textbox\\\\\";\\\\nexport let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let visible = true;\\\\nexport let show_label;\\\\nexport let scale = null;\\\\nexport let min_width = void 0;\\\\nexport let loading_status = void 0;\\\\nexport let height = void 0;\\\\n<\\/script>\\\\n\\\\n<Block\\\\n\\\\t{visible}\\\\n\\\\t{elem_id}\\\\n\\\\t{elem_classes}\\\\n\\\\t{scale}\\\\n\\\\t{min_width}\\\\n\\\\tallow_overflow={false}\\\\n\\\\tpadding={true}\\\\n\\\\t{height}\\\\n\\\\tbind:fullscreen\\\\n>\\\\n\\\\t{#if loading_status}\\\\n\\\\t\\\\t<StatusTracker\\\\n\\\\t\\\\t\\\\tautoscroll={gradio.autoscroll}\\\\n\\\\t\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t\\\\t{...loading_status}\\\\n\\\\t\\\\t\\\\ton:clear_status={() => gradio.dispatch(\\\\\"clear_status\\\\\", loading_status)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t{#if show_fullscreen_button}\\\\n\\\\t\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t\\\\t<FullscreenButton\\\\n\\\\t\\\\t\\\\t\\\\t{fullscreen}\\\\n\\\\t\\\\t\\\\t\\\\ton:fullscreen={({ detail }) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tfullscreen = detail;\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\t{/if}\\\\n\\\\t<BlockTitle {root} {show_label} info={undefined}>{label}</BlockTitle>\\\\n\\\\n\\\\t{#if value && is_browser}\\\\n\\\\t\\\\t<div bind:this={chart_element}></div>\\\\n\\\\n\\\\t\\\\t{#if caption}\\\\n\\\\t\\\\t\\\\t<p class=\\\\\"caption\\\\\">{caption}</p>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<Empty unpadded_box={true}><LabelIcon /></Empty>\\\\n\\\\t{/if}\\\\n</Block>\\\\n\\\\n<style>\\\\n\\\\tdiv {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\t:global(#vg-tooltip-element) {\\\\n\\\\t\\\\tfont-family: var(--font) !important;\\\\n\\\\t\\\\tfont-size: var(--text-xs) !important;\\\\n\\\\t\\\\tbox-shadow: none !important;\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill) !important;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary) !important;\\\\n\\\\t\\\\tcolor: var(--body-text-color) !important;\\\\n\\\\t}\\\\n\\\\t:global(#vg-tooltip-element .key) {\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued) !important;\\\\n\\\\t}\\\\n\\\\t.caption {\\\\n\\\\t\\\\tpadding: 0 4px;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAkcC,kBAAI,CACH,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CACQ,mBAAqB,CAC5B,WAAW,CAAE,IAAI,MAAM,CAAC,CAAC,UAAU,CACnC,SAAS,CAAE,IAAI,SAAS,CAAC,CAAC,UAAU,CACpC,UAAU,CAAE,IAAI,CAAC,UAAU,CAC3B,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAAC,UAAU,CACzD,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAAC,UAAU,CACxD,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAAC,UAC/B,CACQ,wBAA0B,CACjC,KAAK,CAAE,IAAI,yBAAyB,CAAC,CAAC,UACvC,CACA,uBAAS,CACR,OAAO,CAAE,CAAC,CAAC,GAAG,CACd,MAAM,CAAE,CAAC,CACT,UAAU,CAAE,MACb\"}'\n};\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let unique_colors;\n  let _sort;\n  let x_temporal;\n  let _x_lim;\n  let computed_style;\n  let { value } = $$props;\n  let { x } = $$props;\n  let { y } = $$props;\n  let { color = null } = $$props;\n  let { root } = $$props;\n  let { title = null } = $$props;\n  let { x_title = null } = $$props;\n  let { y_title = null } = $$props;\n  let { color_title = null } = $$props;\n  let { x_bin = null } = $$props;\n  let { y_aggregate = void 0 } = $$props;\n  let { color_map = null } = $$props;\n  let { x_lim = null } = $$props;\n  let { y_lim = null } = $$props;\n  let { x_label_angle = null } = $$props;\n  let { y_label_angle = null } = $$props;\n  let { x_axis_labels_visible = true } = $$props;\n  let { caption = null } = $$props;\n  let { sort = null } = $$props;\n  let { tooltip = \"axis\" } = $$props;\n  let { show_fullscreen_button = false } = $$props;\n  let fullscreen = false;\n  function reformat_sort(_sort2) {\n    if (_sort2 === \"x\") {\n      return \"ascending\";\n    } else if (_sort2 === \"-x\") {\n      return \"descending\";\n    } else if (_sort2 === \"y\") {\n      return { field: y, order: \"ascending\" };\n    } else if (_sort2 === \"-y\") {\n      return { field: y, order: \"descending\" };\n    } else if (_sort2 === null) {\n      return null;\n    } else if (Array.isArray(_sort2)) {\n      return _sort2;\n    }\n  }\n  let { _selectable = false } = $$props;\n  let _data;\n  let { gradio } = $$props;\n  let _x_bin;\n  let mouse_down_on_chart = false;\n  const SUFFIX_DURATION = { s: 1, m: 60, h: 60 * 60, d: 24 * 60 * 60 };\n  let _y_aggregate;\n  let aggregating;\n  function reformat_data(data) {\n    if (tooltip == \"all\" || Array.isArray(tooltip)) {\n      return data.data.map((row) => {\n        const obj = {};\n        data.columns.forEach((col, i) => {\n          obj[col] = row[i];\n        });\n        return obj;\n      });\n    }\n    let x_index = data.columns.indexOf(x);\n    let y_index = data.columns.indexOf(y);\n    let color_index = color ? data.columns.indexOf(color) : null;\n    return data.data.map((row) => {\n      const obj = { [x]: row[x_index], [y]: row[y_index] };\n      if (color && color_index !== null) {\n        obj[color] = row[color_index];\n      }\n      return obj;\n    });\n  }\n  let old_value = value;\n  const is_browser = typeof window !== \"undefined\";\n  let chart_element;\n  let view;\n  let old_width;\n  let resizeObserver;\n  let vegaEmbed;\n  async function load_chart() {\n    if (mouse_down_on_chart) {\n      refresh_pending = true;\n      return;\n    }\n    if (view) {\n      view.finalize();\n    }\n    if (!value || !chart_element)\n      return;\n    old_width = chart_element.offsetWidth;\n    const spec = create_vega_lite_spec();\n    if (!spec)\n      return;\n    resizeObserver = new ResizeObserver((el) => {\n      if (!el[0].target || !(el[0].target instanceof HTMLElement))\n        return;\n      if (old_width === 0 && chart_element.offsetWidth !== 0 && value.datatypes[x] === \"nominal\") {\n        load_chart();\n      } else {\n        view.signal(\"width\", el[0].target.offsetWidth).run();\n      }\n    });\n    if (!vegaEmbed) {\n      vegaEmbed = (await import(\"./vega-embed.module.js\")).default;\n    }\n    vegaEmbed(chart_element, spec, { actions: false }).then(function(result) {\n      view = result.view;\n      resizeObserver.observe(chart_element);\n      var debounceTimeout;\n      view.addEventListener(\"dblclick\", () => {\n        gradio.dispatch(\"double_click\");\n      });\n      chart_element.addEventListener(\n        \"mousedown\",\n        function(e) {\n          if (e.detail > 1) {\n            e.preventDefault();\n          }\n        },\n        false\n      );\n      if (_selectable) {\n        view.addSignalListener(\"brush\", function(_, value2) {\n          mouse_down_on_chart = true;\n          if (Object.keys(value2).length === 0)\n            return;\n          clearTimeout(debounceTimeout);\n          let range = value2[Object.keys(value2)[0]];\n          if (x_temporal) {\n            range = [range[0] / 1e3, range[1] / 1e3];\n          }\n          debounceTimeout = setTimeout(\n            function() {\n              mouse_down_on_chart = false;\n              gradio.dispatch(\"select\", {\n                value: range,\n                index: range,\n                selected: true\n              });\n              if (refresh_pending) {\n                refresh_pending = false;\n                load_chart();\n              }\n            },\n            250\n          );\n        });\n      }\n    });\n  }\n  let refresh_pending = false;\n  onMount(() => {\n    return () => {\n      if (view) {\n        view.finalize();\n      }\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n    };\n  });\n  function create_vega_lite_spec() {\n    if (!value || !computed_style)\n      return null;\n    let accent_color = computed_style.getPropertyValue(\"--color-accent\");\n    let body_text_color = computed_style.getPropertyValue(\"--body-text-color\");\n    let borderColorPrimary = computed_style.getPropertyValue(\"--border-color-primary\");\n    let font_family = computed_style.fontFamily;\n    let title_weight = computed_style.getPropertyValue(\"--block-title-text-weight\");\n    const font_to_px_val = (font) => {\n      return font.endsWith(\"px\") ? parseFloat(font.slice(0, -2)) : 12;\n    };\n    let text_size_md = font_to_px_val(computed_style.getPropertyValue(\"--text-md\"));\n    let text_size_sm = font_to_px_val(computed_style.getPropertyValue(\"--text-sm\"));\n    return {\n      $schema: \"https://vega.github.io/schema/vega-lite/v5.17.0.json\",\n      background: \"transparent\",\n      config: {\n        autosize: { type: \"fit\", contains: \"padding\" },\n        axis: {\n          labelFont: font_family,\n          labelColor: body_text_color,\n          titleFont: font_family,\n          titleColor: body_text_color,\n          titlePadding: 8,\n          tickColor: borderColorPrimary,\n          labelFontSize: text_size_sm,\n          gridColor: borderColorPrimary,\n          titleFontWeight: \"normal\",\n          titleFontSize: text_size_sm,\n          labelFontWeight: \"normal\",\n          domain: false,\n          labelAngle: 0\n        },\n        legend: {\n          labelColor: body_text_color,\n          labelFont: font_family,\n          titleColor: body_text_color,\n          titleFont: font_family,\n          titleFontWeight: \"normal\",\n          titleFontSize: text_size_sm,\n          labelFontWeight: \"normal\",\n          offset: 2\n        },\n        title: {\n          color: body_text_color,\n          font: font_family,\n          fontSize: text_size_md,\n          fontWeight: title_weight,\n          anchor: \"middle\"\n        },\n        view: { stroke: borderColorPrimary },\n        mark: {\n          stroke: value.mark !== \"bar\" ? accent_color : void 0,\n          fill: value.mark === \"bar\" ? accent_color : void 0,\n          cursor: \"crosshair\"\n        }\n      },\n      data: { name: \"data\" },\n      datasets: { data: _data },\n      layer: [\"plot\", ...value.mark === \"line\" ? [\"hover\"] : []].map((mode) => {\n        return {\n          encoding: {\n            size: value.mark === \"line\" ? mode == \"plot\" ? {\n              condition: {\n                empty: false,\n                param: \"hoverPlot\",\n                value: 3\n              },\n              value: 2\n            } : {\n              condition: { empty: false, param: \"hover\", value: 100 },\n              value: 0\n            } : void 0,\n            opacity: mode === \"plot\" ? void 0 : {\n              condition: { empty: false, param: \"hover\", value: 1 },\n              value: 0\n            },\n            x: {\n              axis: {\n                ...x_label_angle !== null && { labelAngle: x_label_angle },\n                labels: x_axis_labels_visible,\n                ticks: x_axis_labels_visible\n              },\n              field: x,\n              title: x_title || x,\n              type: value.datatypes[x],\n              scale: _x_lim ? { domain: _x_lim } : void 0,\n              bin: _x_bin ? { step: _x_bin } : void 0,\n              sort: _sort\n            },\n            y: {\n              axis: y_label_angle ? { labelAngle: y_label_angle } : {},\n              field: y,\n              title: y_title || y,\n              type: value.datatypes[y],\n              scale: y_lim ? { domain: y_lim } : void 0,\n              aggregate: aggregating ? _y_aggregate : void 0\n            },\n            color: color ? {\n              field: color,\n              legend: { orient: \"bottom\", title: color_title },\n              scale: value.datatypes[color] === \"nominal\" ? {\n                domain: unique_colors,\n                range: color_map ? unique_colors.map((c) => color_map[c]) : void 0\n              } : {\n                range: [100, 200, 300, 400, 500, 600, 700, 800, 900].map((n) => computed_style.getPropertyValue(\"--primary-\" + n)),\n                interpolate: \"hsl\"\n              },\n              type: value.datatypes[color]\n            } : void 0,\n            tooltip: tooltip == \"none\" ? void 0 : [\n              {\n                field: y,\n                type: value.datatypes[y],\n                aggregate: aggregating ? _y_aggregate : void 0,\n                title: y_title || y\n              },\n              {\n                field: x,\n                type: value.datatypes[x],\n                title: x_title || x,\n                format: x_temporal ? \"%Y-%m-%d %H:%M:%S\" : void 0,\n                bin: _x_bin ? { step: _x_bin } : void 0\n              },\n              ...color ? [\n                {\n                  field: color,\n                  type: value.datatypes[color]\n                }\n              ] : [],\n              ...tooltip === \"axis\" ? [] : value?.columns.filter((col) => col !== x && col !== y && col !== color && (tooltip === \"all\" || tooltip.includes(col))).map((column) => ({\n                field: column,\n                type: value.datatypes[column]\n              }))\n            ]\n          },\n          strokeDash: {},\n          mark: {\n            clip: true,\n            type: mode === \"hover\" ? \"point\" : value.mark\n          },\n          name: mode\n        };\n      }),\n      // @ts-ignore\n      params: [\n        ...value.mark === \"line\" ? [\n          {\n            name: \"hoverPlot\",\n            select: {\n              clear: \"mouseout\",\n              fields: color ? [color] : [],\n              nearest: true,\n              on: \"mouseover\",\n              type: \"point\"\n            },\n            views: [\"hover\"]\n          },\n          {\n            name: \"hover\",\n            select: {\n              clear: \"mouseout\",\n              nearest: true,\n              on: \"mouseover\",\n              type: \"point\"\n            },\n            views: [\"hover\"]\n          }\n        ] : [],\n        ..._selectable ? [\n          {\n            name: \"brush\",\n            select: {\n              encodings: [\"x\"],\n              mark: {\n                fill: \"gray\",\n                fillOpacity: 0.3,\n                stroke: \"none\"\n              },\n              type: \"interval\"\n            },\n            views: [\"plot\"]\n          }\n        ] : []\n      ],\n      width: chart_element.offsetWidth,\n      height: height ? \"container\" : void 0,\n      title: title || void 0\n    };\n  }\n  let { label = \"Textbox\" } = $$props;\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { show_label } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { loading_status = void 0 } = $$props;\n  let { height = void 0 } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.x === void 0 && $$bindings.x && x !== void 0)\n    $$bindings.x(x);\n  if ($$props.y === void 0 && $$bindings.y && y !== void 0)\n    $$bindings.y(y);\n  if ($$props.color === void 0 && $$bindings.color && color !== void 0)\n    $$bindings.color(color);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.title === void 0 && $$bindings.title && title !== void 0)\n    $$bindings.title(title);\n  if ($$props.x_title === void 0 && $$bindings.x_title && x_title !== void 0)\n    $$bindings.x_title(x_title);\n  if ($$props.y_title === void 0 && $$bindings.y_title && y_title !== void 0)\n    $$bindings.y_title(y_title);\n  if ($$props.color_title === void 0 && $$bindings.color_title && color_title !== void 0)\n    $$bindings.color_title(color_title);\n  if ($$props.x_bin === void 0 && $$bindings.x_bin && x_bin !== void 0)\n    $$bindings.x_bin(x_bin);\n  if ($$props.y_aggregate === void 0 && $$bindings.y_aggregate && y_aggregate !== void 0)\n    $$bindings.y_aggregate(y_aggregate);\n  if ($$props.color_map === void 0 && $$bindings.color_map && color_map !== void 0)\n    $$bindings.color_map(color_map);\n  if ($$props.x_lim === void 0 && $$bindings.x_lim && x_lim !== void 0)\n    $$bindings.x_lim(x_lim);\n  if ($$props.y_lim === void 0 && $$bindings.y_lim && y_lim !== void 0)\n    $$bindings.y_lim(y_lim);\n  if ($$props.x_label_angle === void 0 && $$bindings.x_label_angle && x_label_angle !== void 0)\n    $$bindings.x_label_angle(x_label_angle);\n  if ($$props.y_label_angle === void 0 && $$bindings.y_label_angle && y_label_angle !== void 0)\n    $$bindings.y_label_angle(y_label_angle);\n  if ($$props.x_axis_labels_visible === void 0 && $$bindings.x_axis_labels_visible && x_axis_labels_visible !== void 0)\n    $$bindings.x_axis_labels_visible(x_axis_labels_visible);\n  if ($$props.caption === void 0 && $$bindings.caption && caption !== void 0)\n    $$bindings.caption(caption);\n  if ($$props.sort === void 0 && $$bindings.sort && sort !== void 0)\n    $$bindings.sort(sort);\n  if ($$props.tooltip === void 0 && $$bindings.tooltip && tooltip !== void 0)\n    $$bindings.tooltip(tooltip);\n  if ($$props.show_fullscreen_button === void 0 && $$bindings.show_fullscreen_button && show_fullscreen_button !== void 0)\n    $$bindings.show_fullscreen_button(show_fullscreen_button);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    _data = value ? reformat_data(value) : [];\n    unique_colors = color && value && value.datatypes[color] === \"nominal\" ? Array.from(new Set(_data.map((d) => d[color]))) : [];\n    x_lim = x_lim || null;\n    y_lim = y_lim || null;\n    _sort = reformat_sort(sort);\n    x_temporal = value && value.datatypes[x] === \"temporal\";\n    _x_lim = x_lim && x_temporal ? [x_lim[0] * 1e3, x_lim[1] * 1e3] : x_lim;\n    _x_bin = x_bin ? typeof x_bin === \"string\" ? 1e3 * parseInt(x_bin.substring(0, x_bin.length - 1)) * SUFFIX_DURATION[x_bin[x_bin.length - 1]] : x_bin : void 0;\n    {\n      {\n        if (value) {\n          if (value.mark === \"point\") {\n            aggregating = _x_bin !== void 0;\n            _y_aggregate = y_aggregate || aggregating ? \"sum\" : void 0;\n          } else {\n            aggregating = _x_bin !== void 0 || value.datatypes[x] === \"nominal\";\n            _y_aggregate = y_aggregate ? y_aggregate : \"sum\";\n          }\n        }\n      }\n    }\n    {\n      if (old_value !== value && view) {\n        old_value = value;\n        view.data(\"data\", _data).runAsync();\n      }\n    }\n    computed_style = null;\n    JSON.stringify(color_map);\n    {\n      computed_style && requestAnimationFrame(load_chart);\n    }\n    $$rendered = `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        elem_id,\n        elem_classes,\n        scale,\n        min_width,\n        allow_overflow: false,\n        padding: true,\n        height,\n        fullscreen\n      },\n      {\n        fullscreen: ($$value) => {\n          fullscreen = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${loading_status ? `${validate_component(Static, \"StatusTracker\").$$render($$result, Object_1.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})}` : ``} ${show_fullscreen_button ? `${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, {}, {}, {\n            default: () => {\n              return `${validate_component(FullscreenButton, \"FullscreenButton\").$$render($$result, { fullscreen }, {}, {})}`;\n            }\n          })}` : ``} ${validate_component(BlockTitle, \"BlockTitle\").$$render($$result, { root, show_label, info: void 0 }, {}, {\n            default: () => {\n              return `${escape(label)}`;\n            }\n          })} ${value && is_browser ? `<div class=\"svelte-19qacdz\"${add_attribute(\"this\", chart_element, 0)}></div> ${caption ? `<p class=\"caption svelte-19qacdz\">${escape(caption)}</p>` : ``}` : `${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true }, {}, {\n            default: () => {\n              return `${validate_component(LineChart, \"LabelIcon\").$$render($$result, {}, {}, {})}`;\n            }\n          })}`}`;\n        }\n      }\n    )}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  Index as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AACrC,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,qcAAqc;AAC7c,EAAE,GAAG,EAAE,wxjBAAwxjB;AAC/xjB,CAAC,CAAC;AACG,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;AACtB,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;AACtB,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,qBAAqB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,OAAO,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,sBAAsB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACnD,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC;AACzB,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;AACjC,IAAI,IAAI,MAAM,KAAK,GAAG,EAAE;AACxB,MAAM,OAAO,WAAW,CAAC;AACzB,KAAK,MAAM,IAAI,MAAM,KAAK,IAAI,EAAE;AAChC,MAAM,OAAO,YAAY,CAAC;AAC1B,KAAK,MAAM,IAAI,MAAM,KAAK,GAAG,EAAE;AAC/B,MAAM,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC9C,KAAK,MAAM,IAAI,MAAM,KAAK,IAAI,EAAE;AAChC,MAAM,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;AAC/C,KAAK,MAAM,IAAI,MAAM,KAAK,IAAI,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACtC,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAClC,EAAE,MAAM,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;AACvE,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,SAAS,aAAa,CAAC,IAAI,EAAE;AAC/B,IAAI,IAAI,OAAO,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACpD,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACpC,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC;AACvB,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;AACzC,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5B,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,GAAG,CAAC;AACnB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACjE,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AAClC,MAAM,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;AAC3D,MAAM,IAAI,KAAK,IAAI,WAAW,KAAK,IAAI,EAAE;AACzC,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;AACtC,OAAO;AACP,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,eAAe,UAAU,GAAG;AAC9B,IAAI,IAAI,mBAAmB,EAAE;AAC7B,MAAM,eAAe,GAAG,IAAI,CAAC;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa;AAChC,MAAM,OAAO;AACb,IAAI,SAAS,GAAG,aAAa,CAAC,WAAW,CAAC;AAC1C,IAAI,MAAM,IAAI,GAAG,qBAAqB,EAAE,CAAC;AACzC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO;AACb,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,KAAK;AAChD,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,YAAY,WAAW,CAAC;AACjE,QAAQ,OAAO;AACf,MAAM,IAAI,SAAS,KAAK,CAAC,IAAI,aAAa,CAAC,WAAW,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AAClG,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;AAC7D,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,SAAS,GAAG,CAAC,MAAM,OAAO,iCAAwB,CAAC,EAAE,OAAO,CAAC;AACnE,KAAK;AACL,IAAI,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,MAAM,EAAE;AAC7E,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AACzB,MAAM,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC5C,MAAM,IAAI,eAAe,CAAC;AAC1B,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM;AAC9C,QAAQ,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AACxC,OAAO,CAAC,CAAC;AACT,MAAM,aAAa,CAAC,gBAAgB;AACpC,QAAQ,WAAW;AACnB,QAAQ,SAAS,CAAC,EAAE;AACpB,UAAU,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,YAAY,CAAC,CAAC,cAAc,EAAE,CAAC;AAC/B,WAAW;AACX,SAAS;AACT,QAAQ,KAAK;AACb,OAAO,CAAC;AACR,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE;AAC5D,UAAU,mBAAmB,GAAG,IAAI,CAAC;AACrC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;AAC9C,YAAY,OAAO;AACnB,UAAU,YAAY,CAAC,eAAe,CAAC,CAAC;AACxC,UAAU,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,UAAU,IAAI,UAAU,EAAE;AAC1B,YAAY,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACrD,WAAW;AACX,UAAU,eAAe,GAAG,UAAU;AACtC,YAAY,WAAW;AACvB,cAAc,mBAAmB,GAAG,KAAK,CAAC;AAC1C,cAAc,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACxC,gBAAgB,KAAK,EAAE,KAAK;AAC5B,gBAAgB,KAAK,EAAE,KAAK;AAC5B,gBAAgB,QAAQ,EAAE,IAAI;AAC9B,eAAe,CAAC,CAAC;AACjB,cAAc,IAAI,eAAe,EAAE;AACnC,gBAAgB,eAAe,GAAG,KAAK,CAAC;AACxC,gBAAgB,UAAU,EAAE,CAAC;AAC7B,eAAe;AACf,aAAa;AACb,YAAY,GAAG;AACf,WAAW,CAAC;AACZ,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAW9B,EAAE,SAAS,qBAAqB,GAAG;AACnC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc;AACjC,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,YAAY,GAAG,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AACzE,IAAI,IAAI,eAAe,GAAG,cAAc,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;AAC/E,IAAI,IAAI,kBAAkB,GAAG,cAAc,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AACvF,IAAI,IAAI,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC;AAChD,IAAI,IAAI,YAAY,GAAG,cAAc,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AACpF,IAAI,MAAM,cAAc,GAAG,CAAC,IAAI,KAAK;AACrC,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACtE,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;AACpF,IAAI,IAAI,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;AACpF,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,sDAAsD;AACrE,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE;AACtD,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,WAAW;AAChC,UAAU,UAAU,EAAE,eAAe;AACrC,UAAU,SAAS,EAAE,WAAW;AAChC,UAAU,UAAU,EAAE,eAAe;AACrC,UAAU,YAAY,EAAE,CAAC;AACzB,UAAU,SAAS,EAAE,kBAAkB;AACvC,UAAU,aAAa,EAAE,YAAY;AACrC,UAAU,SAAS,EAAE,kBAAkB;AACvC,UAAU,eAAe,EAAE,QAAQ;AACnC,UAAU,aAAa,EAAE,YAAY;AACrC,UAAU,eAAe,EAAE,QAAQ;AACnC,UAAU,MAAM,EAAE,KAAK;AACvB,UAAU,UAAU,EAAE,CAAC;AACvB,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,UAAU,UAAU,EAAE,eAAe;AACrC,UAAU,SAAS,EAAE,WAAW;AAChC,UAAU,UAAU,EAAE,eAAe;AACrC,UAAU,SAAS,EAAE,WAAW;AAChC,UAAU,eAAe,EAAE,QAAQ;AACnC,UAAU,aAAa,EAAE,YAAY;AACrC,UAAU,eAAe,EAAE,QAAQ;AACnC,UAAU,MAAM,EAAE,CAAC;AACnB,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,UAAU,KAAK,EAAE,eAAe;AAChC,UAAU,IAAI,EAAE,WAAW;AAC3B,UAAU,QAAQ,EAAE,YAAY;AAChC,UAAU,UAAU,EAAE,YAAY;AAClC,UAAU,MAAM,EAAE,QAAQ;AAC1B,SAAS;AACT,QAAQ,IAAI,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;AAC5C,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC;AAC9D,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG,KAAK,CAAC;AAC5D,UAAU,MAAM,EAAE,WAAW;AAC7B,SAAS;AACT,OAAO;AACP,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;AAC5B,MAAM,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC/B,MAAM,KAAK,EAAE,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAC/E,QAAQ,OAAO;AACf,UAAU,QAAQ,EAAE;AACpB,YAAY,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG;AAC3D,cAAc,SAAS,EAAE;AACzB,gBAAgB,KAAK,EAAE,KAAK;AAC5B,gBAAgB,KAAK,EAAE,WAAW;AAClC,gBAAgB,KAAK,EAAE,CAAC;AACxB,eAAe;AACf,cAAc,KAAK,EAAE,CAAC;AACtB,aAAa,GAAG;AAChB,cAAc,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE;AACrE,cAAc,KAAK,EAAE,CAAC;AACtB,aAAa,GAAG,KAAK,CAAC;AACtB,YAAY,OAAO,EAAE,IAAI,KAAK,MAAM,GAAG,KAAK,CAAC,GAAG;AAChD,cAAc,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE;AACnE,cAAc,KAAK,EAAE,CAAC;AACtB,aAAa;AACb,YAAY,CAAC,EAAE;AACf,cAAc,IAAI,EAAE;AACpB,gBAAgB,GAAG,aAAa,KAAK,IAAI,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE;AAC1E,gBAAgB,MAAM,EAAE,qBAAqB;AAC7C,gBAAgB,KAAK,EAAE,qBAAqB;AAC5C,eAAe;AACf,cAAc,KAAK,EAAE,CAAC;AACtB,cAAc,KAAK,EAAE,OAAO,IAAI,CAAC;AACjC,cAAc,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACtC,cAAc,KAAK,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACzD,cAAc,GAAG,EAAE,MAAM,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACrD,cAAc,IAAI,EAAE,KAAK;AACzB,aAAa;AACb,YAAY,CAAC,EAAE;AACf,cAAc,IAAI,EAAE,aAAa,GAAG,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,EAAE;AACtE,cAAc,KAAK,EAAE,CAAC;AACtB,cAAc,KAAK,EAAE,OAAO,IAAI,CAAC;AACjC,cAAc,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACtC,cAAc,KAAK,EAAE,KAAK,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AACvD,cAAc,SAAS,EAAE,WAAW,GAAG,YAAY,GAAG,KAAK,CAAC;AAC5D,aAAa;AACb,YAAY,KAAK,EAAE,KAAK,GAAG;AAC3B,cAAc,KAAK,EAAE,KAAK;AAC1B,cAAc,MAAM,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;AAC9D,cAAc,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,GAAG;AAC5D,gBAAgB,MAAM,EAAE,aAAa;AACrC,gBAAgB,KAAK,EAAE,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAClF,eAAe,GAAG;AAClB,gBAAgB,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,gBAAgB,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;AAClI,gBAAgB,WAAW,EAAE,KAAK;AAClC,eAAe;AACf,cAAc,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;AAC1C,aAAa,GAAG,KAAK,CAAC;AACtB,YAAY,OAAO,EAAE,OAAO,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG;AAClD,cAAc;AACd,gBAAgB,KAAK,EAAE,CAAC;AACxB,gBAAgB,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACxC,gBAAgB,SAAS,EAAE,WAAW,GAAG,YAAY,GAAG,KAAK,CAAC;AAC9D,gBAAgB,KAAK,EAAE,OAAO,IAAI,CAAC;AACnC,eAAe;AACf,cAAc;AACd,gBAAgB,KAAK,EAAE,CAAC;AACxB,gBAAgB,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACxC,gBAAgB,KAAK,EAAE,OAAO,IAAI,CAAC;AACnC,gBAAgB,MAAM,EAAE,UAAU,GAAG,mBAAmB,GAAG,KAAK,CAAC;AACjE,gBAAgB,GAAG,EAAE,MAAM,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACvD,eAAe;AACf,cAAc,GAAG,KAAK,GAAG;AACzB,gBAAgB;AAChB,kBAAkB,KAAK,EAAE,KAAK;AAC9B,kBAAkB,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;AAC9C,iBAAiB;AACjB,eAAe,GAAG,EAAE;AACpB,cAAc,GAAG,OAAO,KAAK,MAAM,GAAG,EAAE,GAAG,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACpL,gBAAgB,KAAK,EAAE,MAAM;AAC7B,gBAAgB,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;AAC7C,eAAe,CAAC,CAAC;AACjB,aAAa;AACb,WAAW;AACX,UAAU,UAAU,EAAE,EAAE;AACxB,UAAU,IAAI,EAAE;AAChB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,IAAI,EAAE,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,KAAK,CAAC,IAAI;AACzD,WAAW;AACX,UAAU,IAAI,EAAE,IAAI;AACpB,SAAS,CAAC;AACV,OAAO,CAAC;AACR;AACA,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,KAAK,CAAC,IAAI,KAAK,MAAM,GAAG;AACnC,UAAU;AACV,YAAY,IAAI,EAAE,WAAW;AAC7B,YAAY,MAAM,EAAE;AACpB,cAAc,KAAK,EAAE,UAAU;AAC/B,cAAc,MAAM,EAAE,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE;AAC1C,cAAc,OAAO,EAAE,IAAI;AAC3B,cAAc,EAAE,EAAE,WAAW;AAC7B,cAAc,IAAI,EAAE,OAAO;AAC3B,aAAa;AACb,YAAY,KAAK,EAAE,CAAC,OAAO,CAAC;AAC5B,WAAW;AACX,UAAU;AACV,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,MAAM,EAAE;AACpB,cAAc,KAAK,EAAE,UAAU;AAC/B,cAAc,OAAO,EAAE,IAAI;AAC3B,cAAc,EAAE,EAAE,WAAW;AAC7B,cAAc,IAAI,EAAE,OAAO;AAC3B,aAAa;AACb,YAAY,KAAK,EAAE,CAAC,OAAO,CAAC;AAC5B,WAAW;AACX,SAAS,GAAG,EAAE;AACd,QAAQ,GAAG,WAAW,GAAG;AACzB,UAAU;AACV,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,MAAM,EAAE;AACpB,cAAc,SAAS,EAAE,CAAC,GAAG,CAAC;AAC9B,cAAc,IAAI,EAAE;AACpB,gBAAgB,IAAI,EAAE,MAAM;AAC5B,gBAAgB,WAAW,EAAE,GAAG;AAChC,gBAAgB,MAAM,EAAE,MAAM;AAC9B,eAAe;AACf,cAAc,IAAI,EAAE,UAAU;AAC9B,aAAa;AACb,YAAY,KAAK,EAAE,CAAC,MAAM,CAAC;AAC3B,WAAW;AACX,SAAS,GAAG,EAAE;AACd,OAAO;AACP,MAAM,KAAK,EAAE,aAAa,CAAC,WAAW;AACtC,MAAM,MAAM,EAAE,MAAM,GAAG,WAAW,GAAG,KAAK,CAAC;AAC3C,MAAM,KAAK,EAAE,KAAK,IAAI,KAAK,CAAC;AAC5B,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,SAAS,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,cAAc,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;AAC1D,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;AAC1D,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,qBAAqB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,qBAAqB,IAAI,qBAAqB,KAAK,KAAK,CAAC;AACtH,IAAI,UAAU,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC;AAC5D,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,sBAAsB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sBAAsB,IAAI,sBAAsB,KAAK,KAAK,CAAC;AACzH,IAAI,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;AAC9D,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC9C,IAAI,aAAa,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAClI,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC;AAC1B,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC;AAC1B,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AAChC,IAAI,UAAU,GAAG,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC;AAC5D,IAAI,MAAM,GAAG,KAAK,IAAI,UAAU,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;AAC5E,IAAI,MAAM,GAAG,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AAClK,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,YAAY,WAAW,GAAG,MAAM,KAAK,KAAK,CAAC,CAAC;AAC5C,YAAY,YAAY,GAAG,WAAW,IAAI,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AACvE,WAAW,MAAM;AACjB,YAAY,WAAW,GAAG,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC;AAChF,YAAY,YAAY,GAAG,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC;AAC7D,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,SAAS,KAAK,KAAK,IAAI,IAAI,EAAE;AACvC,QAAQ,SAAS,GAAG,KAAK,CAAC;AAC1B,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC5C,OAAO;AACP,KAAK;AACL,IAAI,cAAc,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC9B,IAAI;AACJ,MAAM,cAAc,IAAI,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC/D,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,MAAM;AACd,QAAQ,UAAU;AAClB,OAAO;AACP,MAAM;AACN,QAAQ,UAAU,EAAE,CAAC,OAAO,KAAK;AACjC,UAAU,UAAU,GAAG,OAAO,CAAC;AAC/B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,cAAc,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,sBAAsB,GAAG,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1U,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9H,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE;AAC/H,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACxC,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,UAAU,GAAG,CAAC,2BAA2B,EAAE,aAAa,CAAC,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,kCAAkC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;AACzR,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACpG,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}