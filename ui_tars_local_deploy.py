#!/usr/bin/env python3
"""
UI-TARS 1.5-7B 本地部署脚本
支持直接使用transformers库加载和运行模型
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoProcessor
from PIL import Image
import requests
import json
import argparse
import logging
from typing import Optional, Dict, Any
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UITarsLocalServer:
    def __init__(self, model_path: str = "ByteDance-Seed/UI-TARS-1.5-7B", device: str = "auto"):
        """
        初始化UI-TARS本地服务器
        
        Args:
            model_path: 模型路径，可以是HuggingFace模型ID或本地路径
            device: 设备类型，auto/cuda/cpu
        """
        self.model_path = model_path
        self.device = self._get_device(device)
        self.model = None
        self.tokenizer = None
        self.processor = None
        
    def _get_device(self, device: str) -> str:
        """获取可用设备"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        return device
    
    def load_model(self):
        """加载模型和tokenizer"""
        logger.info(f"正在加载模型: {self.model_path}")
        logger.info(f"使用设备: {self.device}")
        
        try:
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # 加载processor（用于处理图像）
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # 加载模型
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
                
            logger.info("模型加载完成!")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def generate_response(self, 
                         text: str, 
                         image_path: Optional[str] = None,
                         max_new_tokens: int = 512,
                         temperature: float = 0.7,
                         do_sample: bool = True) -> str:
        """
        生成响应
        
        Args:
            text: 输入文本
            image_path: 图像路径（可选）
            max_new_tokens: 最大生成token数
            temperature: 温度参数
            do_sample: 是否采样
            
        Returns:
            生成的响应文本
        """
        if self.model is None:
            raise RuntimeError("模型未加载，请先调用load_model()")
        
        try:
            # 处理输入
            if image_path:
                image = Image.open(image_path).convert('RGB')
                inputs = self.processor(
                    text=text,
                    images=image,
                    return_tensors="pt"
                ).to(self.device)
            else:
                inputs = self.tokenizer(
                    text,
                    return_tensors="pt"
                ).to(self.device)
            
            # 生成响应
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    do_sample=do_sample,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码响应
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            logger.error(f"生成响应失败: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="UI-TARS 1.5-7B 本地部署")
    parser.add_argument("--model_path", default="ByteDance-Seed/UI-TARS-1.5-7B", 
                       help="模型路径")
    parser.add_argument("--device", default="auto", choices=["auto", "cuda", "cpu"],
                       help="设备类型")
    parser.add_argument("--port", type=int, default=8000, help="服务端口")
    parser.add_argument("--host", default="127.0.0.1", help="服务主机")
    
    args = parser.parse_args()
    
    # 初始化服务器
    server = UITarsLocalServer(args.model_path, args.device)
    
    # 加载模型
    server.load_model()
    
    # 简单的交互式测试
    print("UI-TARS 1.5-7B 本地部署成功!")
    print("输入 'quit' 退出程序")
    print("=" * 50)
    
    while True:
        try:
            user_input = input("\n请输入指令: ")
            if user_input.lower() == 'quit':
                break
                
            # 检查是否包含图像路径
            image_path = None
            if " --image " in user_input:
                parts = user_input.split(" --image ")
                user_input = parts[0]
                image_path = parts[1].strip()
                
            response = server.generate_response(user_input, image_path)
            print(f"\nUI-TARS: {response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")
    
    print("\n再见!")

if __name__ == "__main__":
    main()
