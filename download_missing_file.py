#!/usr/bin/env python3
"""
下载缺失的model-00004-of-00007.safetensors文件
"""

import os
from huggingface_hub import hf_hub_download
import time

def download_missing_file():
    """下载缺失的模型文件"""
    print("🔄 开始下载缺失的model-00004-of-00007.safetensors文件...")
    
    repo_id = "ByteDance-Seed/UI-TARS-1.5-7B"
    filename = "model-00004-of-00007.safetensors"
    
    # 设置本地目录
    local_dir = "models"
    
    try:
        print(f"📥 正在下载 {filename}...")
        start_time = time.time()
        
        # 下载文件
        downloaded_path = hf_hub_download(
            repo_id=repo_id,
            filename=filename,
            local_dir=local_dir,
            local_dir_use_symlinks=False,
            resume_download=True
        )
        
        download_time = time.time() - start_time
        print(f"✅ 文件下载成功！")
        print(f"📁 保存路径: {downloaded_path}")
        print(f"⏱️ 下载耗时: {download_time:.2f}秒")
        
        # 检查文件大小
        if os.path.exists(downloaded_path):
            file_size = os.path.getsize(downloaded_path)
            print(f"📊 文件大小: {file_size / (1024**3):.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = download_missing_file()
    if success:
        print("\n✅ 缺失文件下载完成！现在可以测试完整模型了。")
    else:
        print("\n❌ 文件下载失败，请检查网络连接或重试。")
