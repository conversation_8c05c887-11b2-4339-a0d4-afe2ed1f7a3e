{"version": 3, "file": "WebGLRenderer-CZrk4nK-.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/WebGLRenderer.js"], "sourcesContent": ["import { S as State, E as ExtensionType, B as BufferUsage, w as warn, D as DOMAdapter, N as getAttributeInfoFromFormat, a6 as Geometry, x as GlProgram, t as Shader, u as Texture, k as TextureSource, b as STENCIL_MODES, V as Rectangle, i as CLEAR, j as CanvasSource, l as UniformGroup, q as getMaxTexturesPerBatch, M as Matrix, a7 as compileHighShaderGlProgram, a8 as colorBitGl, a9 as generateTextureBatchBitGl, aa as roundPixelsBitGl, ab as getBatchSamplersUniformGroup, e as extensions, A as AbstractRenderer, R as RendererType } from \"./Index3.js\";\nimport { b as localUniformBitGl } from \"./colorToUniform.js\";\nimport { e as ensureAttributes, G as GpuStencilModesToPixi, c as createUboSyncFunction, b as uboSyncFunctionsSTD40, U as UboSystem, R as RenderTargetSystem, B as BufferResource, d as uniformParsers, f as textureBitGl, S as SharedSystems, a as SharedRenderPipes } from \"./SharedSystems.js\";\nclass GlBatchAdaptor {\n  constructor() {\n    this._tempState = State.for2d();\n    this._didUploadHash = {};\n  }\n  init(batcherPipe) {\n    batcherPipe.renderer.runners.contextChange.add(this);\n  }\n  contextChange() {\n    this._didUploadHash = {};\n  }\n  start(batchPipe, geometry, shader) {\n    const renderer = batchPipe.renderer;\n    const didUpload = this._didUploadHash[shader.uid];\n    renderer.shader.bind(shader, didUpload);\n    if (!didUpload) {\n      this._didUploadHash[shader.uid] = true;\n    }\n    renderer.shader.updateUniformGroup(renderer.globalUniforms.uniformGroup);\n    renderer.geometry.bind(geometry, shader.glProgram);\n  }\n  execute(batchPipe, batch) {\n    const renderer = batchPipe.renderer;\n    this._tempState.blendMode = batch.blendMode;\n    renderer.state.set(this._tempState);\n    const textures = batch.textures.textures;\n    for (let i = 0; i < batch.textures.count; i++) {\n      renderer.texture.bind(textures[i], i);\n    }\n    renderer.geometry.draw(batch.topology, batch.size, batch.start);\n  }\n}\nGlBatchAdaptor.extension = {\n  type: [\n    ExtensionType.WebGLPipesAdaptor\n  ],\n  name: \"batch\"\n};\nvar BUFFER_TYPE = /* @__PURE__ */ ((BUFFER_TYPE2) => {\n  BUFFER_TYPE2[BUFFER_TYPE2[\"ELEMENT_ARRAY_BUFFER\"] = 34963] = \"ELEMENT_ARRAY_BUFFER\";\n  BUFFER_TYPE2[BUFFER_TYPE2[\"ARRAY_BUFFER\"] = 34962] = \"ARRAY_BUFFER\";\n  BUFFER_TYPE2[BUFFER_TYPE2[\"UNIFORM_BUFFER\"] = 35345] = \"UNIFORM_BUFFER\";\n  return BUFFER_TYPE2;\n})(BUFFER_TYPE || {});\nclass GlBuffer {\n  constructor(buffer, type) {\n    this._lastBindBaseLocation = -1;\n    this._lastBindCallId = -1;\n    this.buffer = buffer || null;\n    this.updateID = -1;\n    this.byteLength = -1;\n    this.type = type;\n  }\n}\nclass GlBufferSystem {\n  /**\n   * @param {Renderer} renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n    this._boundBufferBases = /* @__PURE__ */ Object.create(null);\n    this._minBaseLocation = 0;\n    this._nextBindBaseIndex = this._minBaseLocation;\n    this._bindCallId = 0;\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuBuffers\");\n  }\n  /**\n   * @ignore\n   */\n  destroy() {\n    this._renderer = null;\n    this._gl = null;\n    this._gpuBuffers = null;\n    this._boundBufferBases = null;\n  }\n  /** Sets up the renderer context and necessary buffers. */\n  contextChange() {\n    const gl = this._gl = this._renderer.gl;\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n    this._maxBindings = gl.MAX_UNIFORM_BUFFER_BINDINGS ? gl.getParameter(gl.MAX_UNIFORM_BUFFER_BINDINGS) : 0;\n  }\n  getGlBuffer(buffer) {\n    return this._gpuBuffers[buffer.uid] || this.createGLBuffer(buffer);\n  }\n  /**\n   * This binds specified buffer. On first run, it will create the webGL buffers for the context too\n   * @param buffer - the buffer to bind to the renderer\n   */\n  bind(buffer) {\n    const { _gl: gl } = this;\n    const glBuffer = this.getGlBuffer(buffer);\n    gl.bindBuffer(glBuffer.type, glBuffer.buffer);\n  }\n  /**\n   * Binds an uniform buffer to at the given index.\n   *\n   * A cache is used so a buffer will not be bound again if already bound.\n   * @param glBuffer - the buffer to bind\n   * @param index - the base index to bind it to.\n   */\n  bindBufferBase(glBuffer, index) {\n    const { _gl: gl } = this;\n    if (this._boundBufferBases[index] !== glBuffer) {\n      this._boundBufferBases[index] = glBuffer;\n      glBuffer._lastBindBaseLocation = index;\n      gl.bindBufferBase(gl.UNIFORM_BUFFER, index, glBuffer.buffer);\n    }\n  }\n  nextBindBase(hasTransformFeedback) {\n    this._bindCallId++;\n    this._minBaseLocation = 0;\n    if (hasTransformFeedback) {\n      this._boundBufferBases[0] = null;\n      this._minBaseLocation = 1;\n      if (this._nextBindBaseIndex < 1) {\n        this._nextBindBaseIndex = 1;\n      }\n    }\n  }\n  freeLocationForBufferBase(glBuffer) {\n    let freeIndex = this.getLastBindBaseLocation(glBuffer);\n    if (freeIndex >= this._minBaseLocation) {\n      glBuffer._lastBindCallId = this._bindCallId;\n      return freeIndex;\n    }\n    let loop = 0;\n    let nextIndex = this._nextBindBaseIndex;\n    while (loop < 2) {\n      if (nextIndex >= this._maxBindings) {\n        nextIndex = this._minBaseLocation;\n        loop++;\n      }\n      const curBuf = this._boundBufferBases[nextIndex];\n      if (curBuf && curBuf._lastBindCallId === this._bindCallId) {\n        nextIndex++;\n        continue;\n      }\n      break;\n    }\n    freeIndex = nextIndex;\n    this._nextBindBaseIndex = nextIndex + 1;\n    if (loop >= 2) {\n      return -1;\n    }\n    glBuffer._lastBindCallId = this._bindCallId;\n    this._boundBufferBases[freeIndex] = null;\n    return freeIndex;\n  }\n  getLastBindBaseLocation(glBuffer) {\n    const index = glBuffer._lastBindBaseLocation;\n    if (this._boundBufferBases[index] === glBuffer) {\n      return index;\n    }\n    return -1;\n  }\n  /**\n   * Binds a buffer whilst also binding its range.\n   * This will make the buffer start from the offset supplied rather than 0 when it is read.\n   * @param glBuffer - the buffer to bind\n   * @param index - the base index to bind at, defaults to 0\n   * @param offset - the offset to bind at (this is blocks of 256). 0 = 0, 1 = 256, 2 = 512 etc\n   * @param size - the size to bind at (this is blocks of 256).\n   */\n  bindBufferRange(glBuffer, index, offset, size) {\n    const { _gl: gl } = this;\n    offset || (offset = 0);\n    index || (index = 0);\n    this._boundBufferBases[index] = null;\n    gl.bindBufferRange(gl.UNIFORM_BUFFER, index || 0, glBuffer.buffer, offset * 256, size || 256);\n  }\n  /**\n   * Will ensure the data in the buffer is uploaded to the GPU.\n   * @param {Buffer} buffer - the buffer to update\n   */\n  updateBuffer(buffer) {\n    const { _gl: gl } = this;\n    const glBuffer = this.getGlBuffer(buffer);\n    if (buffer._updateID === glBuffer.updateID) {\n      return glBuffer;\n    }\n    glBuffer.updateID = buffer._updateID;\n    gl.bindBuffer(glBuffer.type, glBuffer.buffer);\n    const data = buffer.data;\n    const drawType = buffer.descriptor.usage & BufferUsage.STATIC ? gl.STATIC_DRAW : gl.DYNAMIC_DRAW;\n    if (data) {\n      if (glBuffer.byteLength >= data.byteLength) {\n        gl.bufferSubData(glBuffer.type, 0, data, 0, buffer._updateSize / data.BYTES_PER_ELEMENT);\n      } else {\n        glBuffer.byteLength = data.byteLength;\n        gl.bufferData(glBuffer.type, data, drawType);\n      }\n    } else {\n      glBuffer.byteLength = buffer.descriptor.size;\n      gl.bufferData(glBuffer.type, glBuffer.byteLength, drawType);\n    }\n    return glBuffer;\n  }\n  /** dispose all WebGL resources of all managed buffers */\n  destroyAll() {\n    const gl = this._gl;\n    for (const id in this._gpuBuffers) {\n      gl.deleteBuffer(this._gpuBuffers[id].buffer);\n    }\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n  }\n  /**\n   * Disposes buffer\n   * @param {Buffer} buffer - buffer with data\n   * @param {boolean} [contextLost=false] - If context was lost, we suppress deleteVertexArray\n   */\n  onBufferDestroy(buffer, contextLost) {\n    const glBuffer = this._gpuBuffers[buffer.uid];\n    const gl = this._gl;\n    if (!contextLost) {\n      gl.deleteBuffer(glBuffer.buffer);\n    }\n    this._gpuBuffers[buffer.uid] = null;\n  }\n  /**\n   * creates and attaches a GLBuffer object tied to the current context.\n   * @param buffer\n   * @protected\n   */\n  createGLBuffer(buffer) {\n    const { _gl: gl } = this;\n    let type = BUFFER_TYPE.ARRAY_BUFFER;\n    if (buffer.descriptor.usage & BufferUsage.INDEX) {\n      type = BUFFER_TYPE.ELEMENT_ARRAY_BUFFER;\n    } else if (buffer.descriptor.usage & BufferUsage.UNIFORM) {\n      type = BUFFER_TYPE.UNIFORM_BUFFER;\n    }\n    const glBuffer = new GlBuffer(gl.createBuffer(), type);\n    this._gpuBuffers[buffer.uid] = glBuffer;\n    buffer.on(\"destroy\", this.onBufferDestroy, this);\n    return glBuffer;\n  }\n  resetState() {\n    this._boundBufferBases = /* @__PURE__ */ Object.create(null);\n  }\n}\nGlBufferSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"buffer\"\n};\nconst _GlContextSystem = class _GlContextSystem2 {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this.supports = {\n      /** Support for 32-bit indices buffer. */\n      uint32Indices: true,\n      /** Support for UniformBufferObjects */\n      uniformBufferObject: true,\n      /** Support for VertexArrayObjects */\n      vertexArrayObject: true,\n      /** Support for SRGB texture format */\n      srgbTextures: true,\n      /** Support for wrapping modes if a texture is non-power of two */\n      nonPowOf2wrapping: true,\n      /** Support for MSAA (antialiasing of dynamic textures) */\n      msaa: true,\n      /** Support for mipmaps if a texture is non-power of two */\n      nonPowOf2mipmaps: true\n    };\n    this._renderer = renderer;\n    this.extensions = /* @__PURE__ */ Object.create(null);\n    this.handleContextLost = this.handleContextLost.bind(this);\n    this.handleContextRestored = this.handleContextRestored.bind(this);\n  }\n  /**\n   * `true` if the context is lost\n   * @readonly\n   */\n  get isLost() {\n    return !this.gl || this.gl.isContextLost();\n  }\n  /**\n   * Handles the context change event.\n   * @param {WebGLRenderingContext} gl - New WebGL context.\n   */\n  contextChange(gl) {\n    this.gl = gl;\n    this._renderer.gl = gl;\n  }\n  init(options) {\n    options = { ..._GlContextSystem2.defaultOptions, ...options };\n    let multiView = this.multiView = options.multiView;\n    if (options.context && multiView) {\n      warn(\"Renderer created with both a context and multiview enabled. Disabling multiView as both cannot work together.\");\n      multiView = false;\n    }\n    if (multiView) {\n      this.canvas = DOMAdapter.get().createCanvas(this._renderer.canvas.width, this._renderer.canvas.height);\n    } else {\n      this.canvas = this._renderer.view.canvas;\n    }\n    if (options.context) {\n      this.initFromContext(options.context);\n    } else {\n      const alpha = this._renderer.background.alpha < 1;\n      const premultipliedAlpha = options.premultipliedAlpha ?? true;\n      const antialias = options.antialias && !this._renderer.backBuffer.useBackBuffer;\n      this.createContext(options.preferWebGLVersion, {\n        alpha,\n        premultipliedAlpha,\n        antialias,\n        stencil: true,\n        preserveDrawingBuffer: options.preserveDrawingBuffer,\n        powerPreference: options.powerPreference ?? \"default\"\n      });\n    }\n  }\n  ensureCanvasSize(targetCanvas) {\n    if (!this.multiView) {\n      if (targetCanvas !== this.canvas) {\n        warn(\"multiView is disabled, but targetCanvas is not the main canvas\");\n      }\n      return;\n    }\n    const { canvas } = this;\n    if (canvas.width < targetCanvas.width || canvas.height < targetCanvas.height) {\n      canvas.width = Math.max(targetCanvas.width, targetCanvas.width);\n      canvas.height = Math.max(targetCanvas.height, targetCanvas.height);\n    }\n  }\n  /**\n   * Initializes the context.\n   * @protected\n   * @param {WebGLRenderingContext} gl - WebGL context\n   */\n  initFromContext(gl) {\n    this.gl = gl;\n    this.webGLVersion = gl instanceof DOMAdapter.get().getWebGLRenderingContext() ? 1 : 2;\n    this.getExtensions();\n    this.validateContext(gl);\n    this._renderer.runners.contextChange.emit(gl);\n    const element = this._renderer.view.canvas;\n    element.addEventListener(\"webglcontextlost\", this.handleContextLost, false);\n    element.addEventListener(\"webglcontextrestored\", this.handleContextRestored, false);\n  }\n  /**\n   * Initialize from context options\n   * @protected\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/getContext\n   * @param preferWebGLVersion\n   * @param {object} options - context attributes\n   */\n  createContext(preferWebGLVersion, options) {\n    let gl;\n    const canvas = this.canvas;\n    if (preferWebGLVersion === 2) {\n      gl = canvas.getContext(\"webgl2\", options);\n    }\n    if (!gl) {\n      gl = canvas.getContext(\"webgl\", options);\n      if (!gl) {\n        throw new Error(\"This browser does not support WebGL. Try using the canvas renderer\");\n      }\n    }\n    this.gl = gl;\n    this.initFromContext(this.gl);\n  }\n  /** Auto-populate the {@link GlContextSystem.extensions extensions}. */\n  getExtensions() {\n    const { gl } = this;\n    const common = {\n      anisotropicFiltering: gl.getExtension(\"EXT_texture_filter_anisotropic\"),\n      floatTextureLinear: gl.getExtension(\"OES_texture_float_linear\"),\n      s3tc: gl.getExtension(\"WEBGL_compressed_texture_s3tc\"),\n      s3tc_sRGB: gl.getExtension(\"WEBGL_compressed_texture_s3tc_srgb\"),\n      // eslint-disable-line camelcase\n      etc: gl.getExtension(\"WEBGL_compressed_texture_etc\"),\n      etc1: gl.getExtension(\"WEBGL_compressed_texture_etc1\"),\n      pvrtc: gl.getExtension(\"WEBGL_compressed_texture_pvrtc\") || gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_pvrtc\"),\n      atc: gl.getExtension(\"WEBGL_compressed_texture_atc\"),\n      astc: gl.getExtension(\"WEBGL_compressed_texture_astc\"),\n      bptc: gl.getExtension(\"EXT_texture_compression_bptc\"),\n      rgtc: gl.getExtension(\"EXT_texture_compression_rgtc\"),\n      loseContext: gl.getExtension(\"WEBGL_lose_context\")\n    };\n    if (this.webGLVersion === 1) {\n      this.extensions = {\n        ...common,\n        drawBuffers: gl.getExtension(\"WEBGL_draw_buffers\"),\n        depthTexture: gl.getExtension(\"WEBGL_depth_texture\"),\n        vertexArrayObject: gl.getExtension(\"OES_vertex_array_object\") || gl.getExtension(\"MOZ_OES_vertex_array_object\") || gl.getExtension(\"WEBKIT_OES_vertex_array_object\"),\n        uint32ElementIndex: gl.getExtension(\"OES_element_index_uint\"),\n        // Floats and half-floats\n        floatTexture: gl.getExtension(\"OES_texture_float\"),\n        floatTextureLinear: gl.getExtension(\"OES_texture_float_linear\"),\n        textureHalfFloat: gl.getExtension(\"OES_texture_half_float\"),\n        textureHalfFloatLinear: gl.getExtension(\"OES_texture_half_float_linear\"),\n        vertexAttribDivisorANGLE: gl.getExtension(\"ANGLE_instanced_arrays\"),\n        srgb: gl.getExtension(\"EXT_sRGB\")\n      };\n    } else {\n      this.extensions = {\n        ...common,\n        colorBufferFloat: gl.getExtension(\"EXT_color_buffer_float\")\n      };\n      const provokeExt = gl.getExtension(\"WEBGL_provoking_vertex\");\n      if (provokeExt) {\n        provokeExt.provokingVertexWEBGL(provokeExt.FIRST_VERTEX_CONVENTION_WEBGL);\n      }\n    }\n  }\n  /**\n   * Handles a lost webgl context\n   * @param {WebGLContextEvent} event - The context lost event.\n   */\n  handleContextLost(event) {\n    event.preventDefault();\n    if (this._contextLossForced) {\n      this._contextLossForced = false;\n      setTimeout(() => {\n        if (this.gl.isContextLost()) {\n          this.extensions.loseContext?.restoreContext();\n        }\n      }, 0);\n    }\n  }\n  /** Handles a restored webgl context. */\n  handleContextRestored() {\n    this._renderer.runners.contextChange.emit(this.gl);\n  }\n  destroy() {\n    const element = this._renderer.view.canvas;\n    this._renderer = null;\n    element.removeEventListener(\"webglcontextlost\", this.handleContextLost);\n    element.removeEventListener(\"webglcontextrestored\", this.handleContextRestored);\n    this.gl.useProgram(null);\n    this.extensions.loseContext?.loseContext();\n  }\n  /**\n   * this function can be called to force a webGL context loss\n   * this will release all resources on the GPU.\n   * Useful if you need to put Pixi to sleep, and save some GPU memory\n   *\n   * As soon as render is called - all resources will be created again.\n   */\n  forceContextLoss() {\n    this.extensions.loseContext?.loseContext();\n    this._contextLossForced = true;\n  }\n  /**\n   * Validate context.\n   * @param {WebGLRenderingContext} gl - Render context.\n   */\n  validateContext(gl) {\n    const attributes = gl.getContextAttributes();\n    if (attributes && !attributes.stencil) {\n      warn(\"Provided WebGL context does not have a stencil buffer, masks may not render correctly\");\n    }\n    const supports = this.supports;\n    const isWebGl2 = this.webGLVersion === 2;\n    const extensions2 = this.extensions;\n    supports.uint32Indices = isWebGl2 || !!extensions2.uint32ElementIndex;\n    supports.uniformBufferObject = isWebGl2;\n    supports.vertexArrayObject = isWebGl2 || !!extensions2.vertexArrayObject;\n    supports.srgbTextures = isWebGl2 || !!extensions2.srgb;\n    supports.nonPowOf2wrapping = isWebGl2;\n    supports.nonPowOf2mipmaps = isWebGl2;\n    supports.msaa = isWebGl2;\n    if (!supports.uint32Indices) {\n      warn(\"Provided WebGL context does not support 32 index buffer, large scenes may not render correctly\");\n    }\n  }\n};\n_GlContextSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"context\"\n};\n_GlContextSystem.defaultOptions = {\n  /**\n   * {@link WebGLOptions.context}\n   * @default null\n   */\n  context: null,\n  /**\n   * {@link WebGLOptions.premultipliedAlpha}\n   * @default true\n   */\n  premultipliedAlpha: true,\n  /**\n   * {@link WebGLOptions.preserveDrawingBuffer}\n   * @default false\n   */\n  preserveDrawingBuffer: false,\n  /**\n   * {@link WebGLOptions.powerPreference}\n   * @default default\n   */\n  powerPreference: void 0,\n  /**\n   * {@link WebGLOptions.webGLVersion}\n   * @default 2\n   */\n  preferWebGLVersion: 2,\n  /**\n   * {@link WebGLOptions.multiView}\n   * @default false\n   */\n  multiView: false\n};\nlet GlContextSystem = _GlContextSystem;\nvar GL_FORMATS = /* @__PURE__ */ ((GL_FORMATS2) => {\n  GL_FORMATS2[GL_FORMATS2[\"RGBA\"] = 6408] = \"RGBA\";\n  GL_FORMATS2[GL_FORMATS2[\"RGB\"] = 6407] = \"RGB\";\n  GL_FORMATS2[GL_FORMATS2[\"RG\"] = 33319] = \"RG\";\n  GL_FORMATS2[GL_FORMATS2[\"RED\"] = 6403] = \"RED\";\n  GL_FORMATS2[GL_FORMATS2[\"RGBA_INTEGER\"] = 36249] = \"RGBA_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"RGB_INTEGER\"] = 36248] = \"RGB_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"RG_INTEGER\"] = 33320] = \"RG_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"RED_INTEGER\"] = 36244] = \"RED_INTEGER\";\n  GL_FORMATS2[GL_FORMATS2[\"ALPHA\"] = 6406] = \"ALPHA\";\n  GL_FORMATS2[GL_FORMATS2[\"LUMINANCE\"] = 6409] = \"LUMINANCE\";\n  GL_FORMATS2[GL_FORMATS2[\"LUMINANCE_ALPHA\"] = 6410] = \"LUMINANCE_ALPHA\";\n  GL_FORMATS2[GL_FORMATS2[\"DEPTH_COMPONENT\"] = 6402] = \"DEPTH_COMPONENT\";\n  GL_FORMATS2[GL_FORMATS2[\"DEPTH_STENCIL\"] = 34041] = \"DEPTH_STENCIL\";\n  return GL_FORMATS2;\n})(GL_FORMATS || {});\nvar GL_TARGETS = /* @__PURE__ */ ((GL_TARGETS2) => {\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_2D\"] = 3553] = \"TEXTURE_2D\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP\"] = 34067] = \"TEXTURE_CUBE_MAP\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_2D_ARRAY\"] = 35866] = \"TEXTURE_2D_ARRAY\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_POSITIVE_X\"] = 34069] = \"TEXTURE_CUBE_MAP_POSITIVE_X\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_NEGATIVE_X\"] = 34070] = \"TEXTURE_CUBE_MAP_NEGATIVE_X\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_POSITIVE_Y\"] = 34071] = \"TEXTURE_CUBE_MAP_POSITIVE_Y\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_NEGATIVE_Y\"] = 34072] = \"TEXTURE_CUBE_MAP_NEGATIVE_Y\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_POSITIVE_Z\"] = 34073] = \"TEXTURE_CUBE_MAP_POSITIVE_Z\";\n  GL_TARGETS2[GL_TARGETS2[\"TEXTURE_CUBE_MAP_NEGATIVE_Z\"] = 34074] = \"TEXTURE_CUBE_MAP_NEGATIVE_Z\";\n  return GL_TARGETS2;\n})(GL_TARGETS || {});\nvar GL_TYPES = /* @__PURE__ */ ((GL_TYPES2) => {\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_BYTE\"] = 5121] = \"UNSIGNED_BYTE\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT\"] = 5123] = \"UNSIGNED_SHORT\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT_5_6_5\"] = 33635] = \"UNSIGNED_SHORT_5_6_5\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT_4_4_4_4\"] = 32819] = \"UNSIGNED_SHORT_4_4_4_4\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_SHORT_5_5_5_1\"] = 32820] = \"UNSIGNED_SHORT_5_5_5_1\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT\"] = 5125] = \"UNSIGNED_INT\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_10F_11F_11F_REV\"] = 35899] = \"UNSIGNED_INT_10F_11F_11F_REV\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_2_10_10_10_REV\"] = 33640] = \"UNSIGNED_INT_2_10_10_10_REV\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_24_8\"] = 34042] = \"UNSIGNED_INT_24_8\";\n  GL_TYPES2[GL_TYPES2[\"UNSIGNED_INT_5_9_9_9_REV\"] = 35902] = \"UNSIGNED_INT_5_9_9_9_REV\";\n  GL_TYPES2[GL_TYPES2[\"BYTE\"] = 5120] = \"BYTE\";\n  GL_TYPES2[GL_TYPES2[\"SHORT\"] = 5122] = \"SHORT\";\n  GL_TYPES2[GL_TYPES2[\"INT\"] = 5124] = \"INT\";\n  GL_TYPES2[GL_TYPES2[\"FLOAT\"] = 5126] = \"FLOAT\";\n  GL_TYPES2[GL_TYPES2[\"FLOAT_32_UNSIGNED_INT_24_8_REV\"] = 36269] = \"FLOAT_32_UNSIGNED_INT_24_8_REV\";\n  GL_TYPES2[GL_TYPES2[\"HALF_FLOAT\"] = 36193] = \"HALF_FLOAT\";\n  return GL_TYPES2;\n})(GL_TYPES || {});\nconst infoMap = {\n  uint8x2: GL_TYPES.UNSIGNED_BYTE,\n  uint8x4: GL_TYPES.UNSIGNED_BYTE,\n  sint8x2: GL_TYPES.BYTE,\n  sint8x4: GL_TYPES.BYTE,\n  unorm8x2: GL_TYPES.UNSIGNED_BYTE,\n  unorm8x4: GL_TYPES.UNSIGNED_BYTE,\n  snorm8x2: GL_TYPES.BYTE,\n  snorm8x4: GL_TYPES.BYTE,\n  uint16x2: GL_TYPES.UNSIGNED_SHORT,\n  uint16x4: GL_TYPES.UNSIGNED_SHORT,\n  sint16x2: GL_TYPES.SHORT,\n  sint16x4: GL_TYPES.SHORT,\n  unorm16x2: GL_TYPES.UNSIGNED_SHORT,\n  unorm16x4: GL_TYPES.UNSIGNED_SHORT,\n  snorm16x2: GL_TYPES.SHORT,\n  snorm16x4: GL_TYPES.SHORT,\n  float16x2: GL_TYPES.HALF_FLOAT,\n  float16x4: GL_TYPES.HALF_FLOAT,\n  float32: GL_TYPES.FLOAT,\n  float32x2: GL_TYPES.FLOAT,\n  float32x3: GL_TYPES.FLOAT,\n  float32x4: GL_TYPES.FLOAT,\n  uint32: GL_TYPES.UNSIGNED_INT,\n  uint32x2: GL_TYPES.UNSIGNED_INT,\n  uint32x3: GL_TYPES.UNSIGNED_INT,\n  uint32x4: GL_TYPES.UNSIGNED_INT,\n  sint32: GL_TYPES.INT,\n  sint32x2: GL_TYPES.INT,\n  sint32x3: GL_TYPES.INT,\n  sint32x4: GL_TYPES.INT\n};\nfunction getGlTypeFromFormat(format) {\n  return infoMap[format] ?? infoMap.float32;\n}\nconst topologyToGlMap = {\n  \"point-list\": 0,\n  \"line-list\": 1,\n  \"line-strip\": 3,\n  \"triangle-list\": 4,\n  \"triangle-strip\": 5\n};\nclass GlGeometrySystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this._geometryVaoHash = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._activeGeometry = null;\n    this._activeVao = null;\n    this.hasVao = true;\n    this.hasInstance = true;\n    this._renderer.renderableGC.addManagedHash(this, \"_geometryVaoHash\");\n  }\n  /** Sets up the renderer context and necessary buffers. */\n  contextChange() {\n    const gl = this.gl = this._renderer.gl;\n    if (!this._renderer.context.supports.vertexArrayObject) {\n      throw new Error(\"[PixiJS] Vertex Array Objects are not supported on this device\");\n    }\n    const nativeVaoExtension = this._renderer.context.extensions.vertexArrayObject;\n    if (nativeVaoExtension) {\n      gl.createVertexArray = () => nativeVaoExtension.createVertexArrayOES();\n      gl.bindVertexArray = (vao) => nativeVaoExtension.bindVertexArrayOES(vao);\n      gl.deleteVertexArray = (vao) => nativeVaoExtension.deleteVertexArrayOES(vao);\n    }\n    const nativeInstancedExtension = this._renderer.context.extensions.vertexAttribDivisorANGLE;\n    if (nativeInstancedExtension) {\n      gl.drawArraysInstanced = (a, b, c, d) => {\n        nativeInstancedExtension.drawArraysInstancedANGLE(a, b, c, d);\n      };\n      gl.drawElementsInstanced = (a, b, c, d, e) => {\n        nativeInstancedExtension.drawElementsInstancedANGLE(a, b, c, d, e);\n      };\n      gl.vertexAttribDivisor = (a, b) => nativeInstancedExtension.vertexAttribDivisorANGLE(a, b);\n    }\n    this._activeGeometry = null;\n    this._activeVao = null;\n    this._geometryVaoHash = /* @__PURE__ */ Object.create(null);\n  }\n  /**\n   * Binds geometry so that is can be drawn. Creating a Vao if required\n   * @param geometry - Instance of geometry to bind.\n   * @param program - Instance of program to use vao for.\n   */\n  bind(geometry, program) {\n    const gl = this.gl;\n    this._activeGeometry = geometry;\n    const vao = this.getVao(geometry, program);\n    if (this._activeVao !== vao) {\n      this._activeVao = vao;\n      gl.bindVertexArray(vao);\n    }\n    this.updateBuffers();\n  }\n  /** Reset and unbind any active VAO and geometry. */\n  resetState() {\n    this.unbind();\n  }\n  /** Update buffers of the currently bound geometry. */\n  updateBuffers() {\n    const geometry = this._activeGeometry;\n    const bufferSystem = this._renderer.buffer;\n    for (let i = 0; i < geometry.buffers.length; i++) {\n      const buffer = geometry.buffers[i];\n      bufferSystem.updateBuffer(buffer);\n    }\n  }\n  /**\n   * Check compatibility between a geometry and a program\n   * @param geometry - Geometry instance.\n   * @param program - Program instance.\n   */\n  checkCompatibility(geometry, program) {\n    const geometryAttributes = geometry.attributes;\n    const shaderAttributes = program._attributeData;\n    for (const j in shaderAttributes) {\n      if (!geometryAttributes[j]) {\n        throw new Error(`shader and geometry incompatible, geometry missing the \"${j}\" attribute`);\n      }\n    }\n  }\n  /**\n   * Takes a geometry and program and generates a unique signature for them.\n   * @param geometry - To get signature from.\n   * @param program - To test geometry against.\n   * @returns - Unique signature of the geometry and program\n   */\n  getSignature(geometry, program) {\n    const attribs = geometry.attributes;\n    const shaderAttributes = program._attributeData;\n    const strings = [\"g\", geometry.uid];\n    for (const i in attribs) {\n      if (shaderAttributes[i]) {\n        strings.push(i, shaderAttributes[i].location);\n      }\n    }\n    return strings.join(\"-\");\n  }\n  getVao(geometry, program) {\n    return this._geometryVaoHash[geometry.uid]?.[program._key] || this.initGeometryVao(geometry, program);\n  }\n  /**\n   * Creates or gets Vao with the same structure as the geometry and stores it on the geometry.\n   * If vao is created, it is bound automatically. We use a shader to infer what and how to set up the\n   * attribute locations.\n   * @param geometry - Instance of geometry to to generate Vao for.\n   * @param program\n   * @param _incRefCount - Increment refCount of all geometry buffers.\n   */\n  initGeometryVao(geometry, program, _incRefCount = true) {\n    const gl = this._renderer.gl;\n    const bufferSystem = this._renderer.buffer;\n    this._renderer.shader._getProgramData(program);\n    this.checkCompatibility(geometry, program);\n    const signature = this.getSignature(geometry, program);\n    if (!this._geometryVaoHash[geometry.uid]) {\n      this._geometryVaoHash[geometry.uid] = /* @__PURE__ */ Object.create(null);\n      geometry.on(\"destroy\", this.onGeometryDestroy, this);\n    }\n    const vaoObjectHash = this._geometryVaoHash[geometry.uid];\n    let vao = vaoObjectHash[signature];\n    if (vao) {\n      vaoObjectHash[program._key] = vao;\n      return vao;\n    }\n    ensureAttributes(geometry, program._attributeData);\n    const buffers = geometry.buffers;\n    vao = gl.createVertexArray();\n    gl.bindVertexArray(vao);\n    for (let i = 0; i < buffers.length; i++) {\n      const buffer = buffers[i];\n      bufferSystem.bind(buffer);\n    }\n    this.activateVao(geometry, program);\n    vaoObjectHash[program._key] = vao;\n    vaoObjectHash[signature] = vao;\n    gl.bindVertexArray(null);\n    return vao;\n  }\n  /**\n   * Disposes geometry.\n   * @param geometry - Geometry with buffers. Only VAO will be disposed\n   * @param [contextLost=false] - If context was lost, we suppress deleteVertexArray\n   */\n  onGeometryDestroy(geometry, contextLost) {\n    const vaoObjectHash = this._geometryVaoHash[geometry.uid];\n    const gl = this.gl;\n    if (vaoObjectHash) {\n      if (contextLost) {\n        for (const i in vaoObjectHash) {\n          if (this._activeVao !== vaoObjectHash[i]) {\n            this.unbind();\n          }\n          gl.deleteVertexArray(vaoObjectHash[i]);\n        }\n      }\n      this._geometryVaoHash[geometry.uid] = null;\n    }\n  }\n  /**\n   * Dispose all WebGL resources of all managed geometries.\n   * @param [contextLost=false] - If context was lost, we suppress `gl.delete` calls\n   */\n  destroyAll(contextLost = false) {\n    const gl = this.gl;\n    for (const i in this._geometryVaoHash) {\n      if (contextLost) {\n        for (const j in this._geometryVaoHash[i]) {\n          const vaoObjectHash = this._geometryVaoHash[i];\n          if (this._activeVao !== vaoObjectHash) {\n            this.unbind();\n          }\n          gl.deleteVertexArray(vaoObjectHash[j]);\n        }\n      }\n      this._geometryVaoHash[i] = null;\n    }\n  }\n  /**\n   * Activate vertex array object.\n   * @param geometry - Geometry instance.\n   * @param program - Shader program instance.\n   */\n  activateVao(geometry, program) {\n    const gl = this._renderer.gl;\n    const bufferSystem = this._renderer.buffer;\n    const attributes = geometry.attributes;\n    if (geometry.indexBuffer) {\n      bufferSystem.bind(geometry.indexBuffer);\n    }\n    let lastBuffer = null;\n    for (const j in attributes) {\n      const attribute = attributes[j];\n      const buffer = attribute.buffer;\n      const glBuffer = bufferSystem.getGlBuffer(buffer);\n      const programAttrib = program._attributeData[j];\n      if (programAttrib) {\n        if (lastBuffer !== glBuffer) {\n          bufferSystem.bind(buffer);\n          lastBuffer = glBuffer;\n        }\n        const location = programAttrib.location;\n        gl.enableVertexAttribArray(location);\n        const attributeInfo = getAttributeInfoFromFormat(attribute.format);\n        const type = getGlTypeFromFormat(attribute.format);\n        if (programAttrib.format?.substring(1, 4) === \"int\") {\n          gl.vertexAttribIPointer(\n            location,\n            attributeInfo.size,\n            type,\n            attribute.stride,\n            attribute.offset\n          );\n        } else {\n          gl.vertexAttribPointer(\n            location,\n            attributeInfo.size,\n            type,\n            attributeInfo.normalised,\n            attribute.stride,\n            attribute.offset\n          );\n        }\n        if (attribute.instance) {\n          if (this.hasInstance) {\n            const divisor = attribute.divisor ?? 1;\n            gl.vertexAttribDivisor(location, divisor);\n          } else {\n            throw new Error(\"geometry error, GPU Instancing is not supported on this device\");\n          }\n        }\n      }\n    }\n  }\n  /**\n   * Draws the currently bound geometry.\n   * @param topology - The type primitive to render.\n   * @param size - The number of elements to be rendered. If not specified, all vertices after the\n   *  starting vertex will be drawn.\n   * @param start - The starting vertex in the geometry to start drawing from. If not specified,\n   *  drawing will start from the first vertex.\n   * @param instanceCount - The number of instances of the set of elements to execute. If not specified,\n   *  all instances will be drawn.\n   */\n  draw(topology, size, start, instanceCount) {\n    const { gl } = this._renderer;\n    const geometry = this._activeGeometry;\n    const glTopology = topologyToGlMap[topology || geometry.topology];\n    instanceCount ?? (instanceCount = geometry.instanceCount);\n    if (geometry.indexBuffer) {\n      const byteSize = geometry.indexBuffer.data.BYTES_PER_ELEMENT;\n      const glType = byteSize === 2 ? gl.UNSIGNED_SHORT : gl.UNSIGNED_INT;\n      if (instanceCount > 1) {\n        gl.drawElementsInstanced(glTopology, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize, instanceCount);\n      } else {\n        gl.drawElements(glTopology, size || geometry.indexBuffer.data.length, glType, (start || 0) * byteSize);\n      }\n    } else if (instanceCount > 1) {\n      gl.drawArraysInstanced(glTopology, start || 0, size || geometry.getSize(), instanceCount);\n    } else {\n      gl.drawArrays(glTopology, start || 0, size || geometry.getSize());\n    }\n    return this;\n  }\n  /** Unbind/reset everything. */\n  unbind() {\n    this.gl.bindVertexArray(null);\n    this._activeVao = null;\n    this._activeGeometry = null;\n  }\n  destroy() {\n    this._renderer = null;\n    this.gl = null;\n    this._activeVao = null;\n    this._activeGeometry = null;\n  }\n}\nGlGeometrySystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"geometry\"\n};\nconst bigTriangleGeometry = new Geometry({\n  attributes: {\n    aPosition: [\n      -1,\n      -1,\n      // Bottom left corner\n      3,\n      -1,\n      // Bottom right corner, extending beyond right edge\n      -1,\n      3\n      // Top left corner, extending beyond top edge\n    ]\n  }\n});\nconst _GlBackBufferSystem = class _GlBackBufferSystem2 {\n  constructor(renderer) {\n    this.useBackBuffer = false;\n    this._useBackBufferThisRender = false;\n    this._renderer = renderer;\n  }\n  init(options = {}) {\n    const { useBackBuffer, antialias } = { ..._GlBackBufferSystem2.defaultOptions, ...options };\n    this.useBackBuffer = useBackBuffer;\n    this._antialias = antialias;\n    if (!this._renderer.context.supports.msaa) {\n      warn(\"antialiasing, is not supported on when using the back buffer\");\n      this._antialias = false;\n    }\n    this._state = State.for2d();\n    const bigTriangleProgram = new GlProgram({\n      vertex: `\n                attribute vec2 aPosition;\n                out vec2 vUv;\n\n                void main() {\n                    gl_Position = vec4(aPosition, 0.0, 1.0);\n\n                    vUv = (aPosition + 1.0) / 2.0;\n\n                    // flip dem UVs\n                    vUv.y = 1.0 - vUv.y;\n                }`,\n      fragment: `\n                in vec2 vUv;\n                out vec4 finalColor;\n\n                uniform sampler2D uTexture;\n\n                void main() {\n                    finalColor = texture(uTexture, vUv);\n                }`,\n      name: \"big-triangle\"\n    });\n    this._bigTriangleShader = new Shader({\n      glProgram: bigTriangleProgram,\n      resources: {\n        uTexture: Texture.WHITE.source\n      }\n    });\n  }\n  /**\n   * This is called before the RenderTargetSystem is started. This is where\n   * we replace the target with the back buffer if required.\n   * @param options - The options for this render.\n   */\n  renderStart(options) {\n    const renderTarget = this._renderer.renderTarget.getRenderTarget(options.target);\n    this._useBackBufferThisRender = this.useBackBuffer && !!renderTarget.isRoot;\n    if (this._useBackBufferThisRender) {\n      const renderTarget2 = this._renderer.renderTarget.getRenderTarget(options.target);\n      this._targetTexture = renderTarget2.colorTexture;\n      options.target = this._getBackBufferTexture(renderTarget2.colorTexture);\n    }\n  }\n  renderEnd() {\n    this._presentBackBuffer();\n  }\n  _presentBackBuffer() {\n    const renderer = this._renderer;\n    renderer.renderTarget.finishRenderPass();\n    if (!this._useBackBufferThisRender)\n      return;\n    renderer.renderTarget.bind(this._targetTexture, false);\n    this._bigTriangleShader.resources.uTexture = this._backBufferTexture.source;\n    renderer.encoder.draw({\n      geometry: bigTriangleGeometry,\n      shader: this._bigTriangleShader,\n      state: this._state\n    });\n  }\n  _getBackBufferTexture(targetSourceTexture) {\n    this._backBufferTexture = this._backBufferTexture || new Texture({\n      source: new TextureSource({\n        width: targetSourceTexture.width,\n        height: targetSourceTexture.height,\n        resolution: targetSourceTexture._resolution,\n        antialias: this._antialias\n      })\n    });\n    this._backBufferTexture.source.resize(\n      targetSourceTexture.width,\n      targetSourceTexture.height,\n      targetSourceTexture._resolution\n    );\n    return this._backBufferTexture;\n  }\n  /** destroys the back buffer */\n  destroy() {\n    if (this._backBufferTexture) {\n      this._backBufferTexture.destroy();\n      this._backBufferTexture = null;\n    }\n  }\n};\n_GlBackBufferSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"backBuffer\",\n  priority: 1\n};\n_GlBackBufferSystem.defaultOptions = {\n  /** if true will use the back buffer where required */\n  useBackBuffer: false\n};\nlet GlBackBufferSystem = _GlBackBufferSystem;\nclass GlColorMaskSystem {\n  constructor(renderer) {\n    this._colorMaskCache = 15;\n    this._renderer = renderer;\n  }\n  setMask(colorMask) {\n    if (this._colorMaskCache === colorMask)\n      return;\n    this._colorMaskCache = colorMask;\n    this._renderer.gl.colorMask(\n      !!(colorMask & 8),\n      !!(colorMask & 4),\n      !!(colorMask & 2),\n      !!(colorMask & 1)\n    );\n  }\n}\nGlColorMaskSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"colorMask\"\n};\nclass GlEncoderSystem {\n  constructor(renderer) {\n    this.commandFinished = Promise.resolve();\n    this._renderer = renderer;\n  }\n  setGeometry(geometry, shader) {\n    this._renderer.geometry.bind(geometry, shader.glProgram);\n  }\n  finishRenderPass() {\n  }\n  draw(options) {\n    const renderer = this._renderer;\n    const { geometry, shader, state, skipSync, topology: type, size, start, instanceCount } = options;\n    renderer.shader.bind(shader, skipSync);\n    renderer.geometry.bind(geometry, renderer.shader._activeProgram);\n    if (state) {\n      renderer.state.set(state);\n    }\n    renderer.geometry.draw(type, size, start, instanceCount ?? geometry.instanceCount);\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\nGlEncoderSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"encoder\"\n};\nclass GlRenderTarget {\n  constructor() {\n    this.width = -1;\n    this.height = -1;\n    this.msaa = false;\n    this.msaaRenderBuffer = [];\n  }\n}\nclass GlStencilSystem {\n  constructor(renderer) {\n    this._stencilCache = {\n      enabled: false,\n      stencilReference: 0,\n      stencilMode: STENCIL_MODES.NONE\n    };\n    this._renderTargetStencilState = /* @__PURE__ */ Object.create(null);\n    renderer.renderTarget.onRenderTargetChange.add(this);\n  }\n  contextChange(gl) {\n    this._gl = gl;\n    this._comparisonFuncMapping = {\n      always: gl.ALWAYS,\n      never: gl.NEVER,\n      equal: gl.EQUAL,\n      \"not-equal\": gl.NOTEQUAL,\n      less: gl.LESS,\n      \"less-equal\": gl.LEQUAL,\n      greater: gl.GREATER,\n      \"greater-equal\": gl.GEQUAL\n    };\n    this._stencilOpsMapping = {\n      keep: gl.KEEP,\n      zero: gl.ZERO,\n      replace: gl.REPLACE,\n      invert: gl.INVERT,\n      \"increment-clamp\": gl.INCR,\n      \"decrement-clamp\": gl.DECR,\n      \"increment-wrap\": gl.INCR_WRAP,\n      \"decrement-wrap\": gl.DECR_WRAP\n    };\n    this._stencilCache.enabled = false;\n    this._stencilCache.stencilMode = STENCIL_MODES.NONE;\n    this._stencilCache.stencilReference = 0;\n  }\n  onRenderTargetChange(renderTarget) {\n    if (this._activeRenderTarget === renderTarget)\n      return;\n    this._activeRenderTarget = renderTarget;\n    let stencilState = this._renderTargetStencilState[renderTarget.uid];\n    if (!stencilState) {\n      stencilState = this._renderTargetStencilState[renderTarget.uid] = {\n        stencilMode: STENCIL_MODES.DISABLED,\n        stencilReference: 0\n      };\n    }\n    this.setStencilMode(stencilState.stencilMode, stencilState.stencilReference);\n  }\n  setStencilMode(stencilMode, stencilReference) {\n    const stencilState = this._renderTargetStencilState[this._activeRenderTarget.uid];\n    const gl = this._gl;\n    const mode = GpuStencilModesToPixi[stencilMode];\n    const _stencilCache = this._stencilCache;\n    stencilState.stencilMode = stencilMode;\n    stencilState.stencilReference = stencilReference;\n    if (stencilMode === STENCIL_MODES.DISABLED) {\n      if (this._stencilCache.enabled) {\n        this._stencilCache.enabled = false;\n        gl.disable(gl.STENCIL_TEST);\n      }\n      return;\n    }\n    if (!this._stencilCache.enabled) {\n      this._stencilCache.enabled = true;\n      gl.enable(gl.STENCIL_TEST);\n    }\n    if (stencilMode !== _stencilCache.stencilMode || _stencilCache.stencilReference !== stencilReference) {\n      _stencilCache.stencilMode = stencilMode;\n      _stencilCache.stencilReference = stencilReference;\n      gl.stencilFunc(this._comparisonFuncMapping[mode.stencilBack.compare], stencilReference, 255);\n      gl.stencilOp(gl.KEEP, gl.KEEP, this._stencilOpsMapping[mode.stencilBack.passOp]);\n    }\n  }\n}\nGlStencilSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"stencil\"\n};\nconst WGSL_TO_STD40_SIZE = {\n  f32: 4,\n  i32: 4,\n  \"vec2<f32>\": 8,\n  \"vec3<f32>\": 12,\n  \"vec4<f32>\": 16,\n  \"vec2<i32>\": 8,\n  \"vec3<i32>\": 12,\n  \"vec4<i32>\": 16,\n  \"mat2x2<f32>\": 16 * 2,\n  \"mat3x3<f32>\": 16 * 3,\n  \"mat4x4<f32>\": 16 * 4\n  // TODO - not essential for now but support these in the future\n  // int:      4,\n  // ivec2:    8,\n  // ivec3:    12,\n  // ivec4:    16,\n  // uint:     4,\n  // uvec2:    8,\n  // uvec3:    12,\n  // uvec4:    16,\n  // bool:     4,\n  // bvec2:    8,\n  // bvec3:    12,\n  // bvec4:    16,\n  // mat2:     16 * 2,\n  // mat3:     16 * 3,\n  // mat4:     16 * 4,\n};\nfunction createUboElementsSTD40(uniformData) {\n  const uboElements = uniformData.map((data) => ({\n    data,\n    offset: 0,\n    size: 0\n  }));\n  const chunkSize = 16;\n  let size = 0;\n  let offset = 0;\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i];\n    size = WGSL_TO_STD40_SIZE[uboElement.data.type];\n    if (!size) {\n      throw new Error(`Unknown type ${uboElement.data.type}`);\n    }\n    if (uboElement.data.size > 1) {\n      size = Math.max(size, chunkSize) * uboElement.data.size;\n    }\n    const boundary = size === 12 ? 16 : size;\n    uboElement.size = size;\n    const curOffset = offset % chunkSize;\n    if (curOffset > 0 && chunkSize - curOffset < boundary) {\n      offset += (chunkSize - curOffset) % 16;\n    } else {\n      offset += (size - curOffset % size) % size;\n    }\n    uboElement.offset = offset;\n    offset += size;\n  }\n  offset = Math.ceil(offset / 16) * 16;\n  return { uboElements, size: offset };\n}\nfunction generateArraySyncSTD40(uboElement, offsetToAdd) {\n  const rowSize = Math.max(WGSL_TO_STD40_SIZE[uboElement.data.type] / 16, 1);\n  const elementSize = uboElement.data.value.length / uboElement.data.size;\n  const remainder = (4 - elementSize % 4) % 4;\n  const data = uboElement.data.type.indexOf(\"i32\") >= 0 ? \"dataInt32\" : \"data\";\n  return `\n        v = uv.${uboElement.data.name};\n        offset += ${offsetToAdd};\n\n        arrayOffset = offset;\n\n        t = 0;\n\n        for(var i=0; i < ${uboElement.data.size * rowSize}; i++)\n        {\n            for(var j = 0; j < ${elementSize}; j++)\n            {\n                ${data}[arrayOffset++] = v[t++];\n            }\n            ${remainder !== 0 ? `arrayOffset += ${remainder};` : \"\"}\n        }\n    `;\n}\nfunction createUboSyncFunctionSTD40(uboElements) {\n  return createUboSyncFunction(\n    uboElements,\n    \"uboStd40\",\n    generateArraySyncSTD40,\n    uboSyncFunctionsSTD40\n  );\n}\nclass GlUboSystem extends UboSystem {\n  constructor() {\n    super({\n      createUboElements: createUboElementsSTD40,\n      generateUboSync: createUboSyncFunctionSTD40\n    });\n  }\n}\nGlUboSystem.extension = {\n  type: [ExtensionType.WebGLSystem],\n  name: \"ubo\"\n};\nclass GlRenderTargetAdaptor {\n  constructor() {\n    this._clearColorCache = [0, 0, 0, 0];\n    this._viewPortCache = new Rectangle();\n  }\n  init(renderer, renderTargetSystem) {\n    this._renderer = renderer;\n    this._renderTargetSystem = renderTargetSystem;\n    renderer.runners.contextChange.add(this);\n  }\n  contextChange() {\n    this._clearColorCache = [0, 0, 0, 0];\n    this._viewPortCache = new Rectangle();\n  }\n  copyToTexture(sourceRenderSurfaceTexture, destinationTexture, originSrc, size, originDest) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const renderer = this._renderer;\n    const glRenderTarget = renderTargetSystem.getGpuRenderTarget(sourceRenderSurfaceTexture);\n    const gl = renderer.gl;\n    this.finishRenderPass(sourceRenderSurfaceTexture);\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.resolveTargetFramebuffer);\n    renderer.texture.bind(destinationTexture, 0);\n    gl.copyTexSubImage2D(\n      gl.TEXTURE_2D,\n      0,\n      originDest.x,\n      originDest.y,\n      originSrc.x,\n      originSrc.y,\n      size.width,\n      size.height\n    );\n    return destinationTexture;\n  }\n  startRenderPass(renderTarget, clear = true, clearColor, viewport) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const source = renderTarget.colorTexture;\n    const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    let viewPortY = viewport.y;\n    if (renderTarget.isRoot) {\n      viewPortY = source.pixelHeight - viewport.height;\n    }\n    renderTarget.colorTextures.forEach((texture) => {\n      this._renderer.texture.unbind(texture);\n    });\n    const gl = this._renderer.gl;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, gpuRenderTarget.framebuffer);\n    const viewPortCache = this._viewPortCache;\n    if (viewPortCache.x !== viewport.x || viewPortCache.y !== viewPortY || viewPortCache.width !== viewport.width || viewPortCache.height !== viewport.height) {\n      viewPortCache.x = viewport.x;\n      viewPortCache.y = viewPortY;\n      viewPortCache.width = viewport.width;\n      viewPortCache.height = viewport.height;\n      gl.viewport(\n        viewport.x,\n        viewPortY,\n        viewport.width,\n        viewport.height\n      );\n    }\n    if (!gpuRenderTarget.depthStencilRenderBuffer && (renderTarget.stencil || renderTarget.depth)) {\n      this._initStencil(gpuRenderTarget);\n    }\n    this.clear(renderTarget, clear, clearColor);\n  }\n  finishRenderPass(renderTarget) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const glRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    if (!glRenderTarget.msaa)\n      return;\n    const gl = this._renderer.gl;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.resolveTargetFramebuffer);\n    gl.bindFramebuffer(gl.READ_FRAMEBUFFER, glRenderTarget.framebuffer);\n    gl.blitFramebuffer(\n      0,\n      0,\n      glRenderTarget.width,\n      glRenderTarget.height,\n      0,\n      0,\n      glRenderTarget.width,\n      glRenderTarget.height,\n      gl.COLOR_BUFFER_BIT,\n      gl.NEAREST\n    );\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenderTarget.framebuffer);\n  }\n  initGpuRenderTarget(renderTarget) {\n    const renderer = this._renderer;\n    const gl = renderer.gl;\n    const glRenderTarget = new GlRenderTarget();\n    const colorTexture = renderTarget.colorTexture;\n    if (colorTexture.resource === renderer.canvas) {\n      this._renderer.context.ensureCanvasSize(renderTarget.colorTexture.resource);\n      glRenderTarget.framebuffer = null;\n      return glRenderTarget;\n    }\n    this._initColor(renderTarget, glRenderTarget);\n    gl.bindFramebuffer(gl.FRAMEBUFFER, null);\n    return glRenderTarget;\n  }\n  destroyGpuRenderTarget(gpuRenderTarget) {\n    const gl = this._renderer.gl;\n    if (gpuRenderTarget.framebuffer) {\n      gl.deleteFramebuffer(gpuRenderTarget.framebuffer);\n      gpuRenderTarget.framebuffer = null;\n    }\n    if (gpuRenderTarget.resolveTargetFramebuffer) {\n      gl.deleteFramebuffer(gpuRenderTarget.resolveTargetFramebuffer);\n      gpuRenderTarget.resolveTargetFramebuffer = null;\n    }\n    if (gpuRenderTarget.depthStencilRenderBuffer) {\n      gl.deleteRenderbuffer(gpuRenderTarget.depthStencilRenderBuffer);\n      gpuRenderTarget.depthStencilRenderBuffer = null;\n    }\n    gpuRenderTarget.msaaRenderBuffer.forEach((renderBuffer) => {\n      gl.deleteRenderbuffer(renderBuffer);\n    });\n    gpuRenderTarget.msaaRenderBuffer = null;\n  }\n  clear(_renderTarget, clear, clearColor) {\n    if (!clear)\n      return;\n    const renderTargetSystem = this._renderTargetSystem;\n    if (typeof clear === \"boolean\") {\n      clear = clear ? CLEAR.ALL : CLEAR.NONE;\n    }\n    const gl = this._renderer.gl;\n    if (clear & CLEAR.COLOR) {\n      clearColor ?? (clearColor = renderTargetSystem.defaultClearColor);\n      const clearColorCache = this._clearColorCache;\n      const clearColorArray = clearColor;\n      if (clearColorCache[0] !== clearColorArray[0] || clearColorCache[1] !== clearColorArray[1] || clearColorCache[2] !== clearColorArray[2] || clearColorCache[3] !== clearColorArray[3]) {\n        clearColorCache[0] = clearColorArray[0];\n        clearColorCache[1] = clearColorArray[1];\n        clearColorCache[2] = clearColorArray[2];\n        clearColorCache[3] = clearColorArray[3];\n        gl.clearColor(clearColorArray[0], clearColorArray[1], clearColorArray[2], clearColorArray[3]);\n      }\n    }\n    gl.clear(clear);\n  }\n  resizeGpuRenderTarget(renderTarget) {\n    if (renderTarget.isRoot)\n      return;\n    const renderTargetSystem = this._renderTargetSystem;\n    const glRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    this._resizeColor(renderTarget, glRenderTarget);\n    if (renderTarget.stencil || renderTarget.depth) {\n      this._resizeStencil(glRenderTarget);\n    }\n  }\n  _initColor(renderTarget, glRenderTarget) {\n    const renderer = this._renderer;\n    const gl = renderer.gl;\n    const resolveTargetFramebuffer = gl.createFramebuffer();\n    glRenderTarget.resolveTargetFramebuffer = resolveTargetFramebuffer;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, resolveTargetFramebuffer);\n    glRenderTarget.width = renderTarget.colorTexture.source.pixelWidth;\n    glRenderTarget.height = renderTarget.colorTexture.source.pixelHeight;\n    renderTarget.colorTextures.forEach((colorTexture, i) => {\n      const source = colorTexture.source;\n      if (source.antialias) {\n        if (renderer.context.supports.msaa) {\n          glRenderTarget.msaa = true;\n        } else {\n          warn(\"[RenderTexture] Antialiasing on textures is not supported in WebGL1\");\n        }\n      }\n      renderer.texture.bindSource(source, 0);\n      const glSource = renderer.texture.getGlSource(source);\n      const glTexture = glSource.texture;\n      gl.framebufferTexture2D(\n        gl.FRAMEBUFFER,\n        gl.COLOR_ATTACHMENT0 + i,\n        3553,\n        // texture.target,\n        glTexture,\n        0\n      );\n    });\n    if (glRenderTarget.msaa) {\n      const viewFramebuffer = gl.createFramebuffer();\n      glRenderTarget.framebuffer = viewFramebuffer;\n      gl.bindFramebuffer(gl.FRAMEBUFFER, viewFramebuffer);\n      renderTarget.colorTextures.forEach((_, i) => {\n        const msaaRenderBuffer = gl.createRenderbuffer();\n        glRenderTarget.msaaRenderBuffer[i] = msaaRenderBuffer;\n      });\n    } else {\n      glRenderTarget.framebuffer = resolveTargetFramebuffer;\n    }\n    this._resizeColor(renderTarget, glRenderTarget);\n  }\n  _resizeColor(renderTarget, glRenderTarget) {\n    const source = renderTarget.colorTexture.source;\n    glRenderTarget.width = source.pixelWidth;\n    glRenderTarget.height = source.pixelHeight;\n    renderTarget.colorTextures.forEach((colorTexture, i) => {\n      if (i === 0)\n        return;\n      colorTexture.source.resize(source.width, source.height, source._resolution);\n    });\n    if (glRenderTarget.msaa) {\n      const renderer = this._renderer;\n      const gl = renderer.gl;\n      const viewFramebuffer = glRenderTarget.framebuffer;\n      gl.bindFramebuffer(gl.FRAMEBUFFER, viewFramebuffer);\n      renderTarget.colorTextures.forEach((colorTexture, i) => {\n        const source2 = colorTexture.source;\n        renderer.texture.bindSource(source2, 0);\n        const glSource = renderer.texture.getGlSource(source2);\n        const glInternalFormat = glSource.internalFormat;\n        const msaaRenderBuffer = glRenderTarget.msaaRenderBuffer[i];\n        gl.bindRenderbuffer(\n          gl.RENDERBUFFER,\n          msaaRenderBuffer\n        );\n        gl.renderbufferStorageMultisample(\n          gl.RENDERBUFFER,\n          4,\n          glInternalFormat,\n          source2.pixelWidth,\n          source2.pixelHeight\n        );\n        gl.framebufferRenderbuffer(\n          gl.FRAMEBUFFER,\n          gl.COLOR_ATTACHMENT0 + i,\n          gl.RENDERBUFFER,\n          msaaRenderBuffer\n        );\n      });\n    }\n  }\n  _initStencil(glRenderTarget) {\n    if (glRenderTarget.framebuffer === null)\n      return;\n    const gl = this._renderer.gl;\n    const depthStencilRenderBuffer = gl.createRenderbuffer();\n    glRenderTarget.depthStencilRenderBuffer = depthStencilRenderBuffer;\n    gl.bindRenderbuffer(\n      gl.RENDERBUFFER,\n      depthStencilRenderBuffer\n    );\n    gl.framebufferRenderbuffer(\n      gl.FRAMEBUFFER,\n      gl.DEPTH_STENCIL_ATTACHMENT,\n      gl.RENDERBUFFER,\n      depthStencilRenderBuffer\n    );\n    this._resizeStencil(glRenderTarget);\n  }\n  _resizeStencil(glRenderTarget) {\n    const gl = this._renderer.gl;\n    gl.bindRenderbuffer(\n      gl.RENDERBUFFER,\n      glRenderTarget.depthStencilRenderBuffer\n    );\n    if (glRenderTarget.msaa) {\n      gl.renderbufferStorageMultisample(\n        gl.RENDERBUFFER,\n        4,\n        gl.DEPTH24_STENCIL8,\n        glRenderTarget.width,\n        glRenderTarget.height\n      );\n    } else {\n      gl.renderbufferStorage(\n        gl.RENDERBUFFER,\n        this._renderer.context.webGLVersion === 2 ? gl.DEPTH24_STENCIL8 : gl.DEPTH_STENCIL,\n        glRenderTarget.width,\n        glRenderTarget.height\n      );\n    }\n  }\n  prerender(renderTarget) {\n    const resource = renderTarget.colorTexture.resource;\n    if (this._renderer.context.multiView && CanvasSource.test(resource)) {\n      this._renderer.context.ensureCanvasSize(resource);\n    }\n  }\n  postrender(renderTarget) {\n    if (!this._renderer.context.multiView)\n      return;\n    if (CanvasSource.test(renderTarget.colorTexture.resource)) {\n      const contextCanvas = this._renderer.context.canvas;\n      const canvasSource = renderTarget.colorTexture;\n      canvasSource.context2D.drawImage(\n        contextCanvas,\n        0,\n        canvasSource.pixelHeight - contextCanvas.height\n      );\n    }\n  }\n}\nclass GlRenderTargetSystem extends RenderTargetSystem {\n  constructor(renderer) {\n    super(renderer);\n    this.adaptor = new GlRenderTargetAdaptor();\n    this.adaptor.init(renderer, this);\n  }\n}\nGlRenderTargetSystem.extension = {\n  type: [ExtensionType.WebGLSystem],\n  name: \"renderTarget\"\n};\nfunction generateShaderSyncCode(shader, shaderSystem) {\n  const funcFragments = [];\n  const headerFragments = [`\n        var g = s.groups;\n        var sS = r.shader;\n        var p = s.glProgram;\n        var ugS = r.uniformGroup;\n        var resources;\n    `];\n  let addedTextreSystem = false;\n  let textureCount = 0;\n  const programData = shaderSystem._getProgramData(shader.glProgram);\n  for (const i in shader.groups) {\n    const group = shader.groups[i];\n    funcFragments.push(`\n            resources = g[${i}].resources;\n        `);\n    for (const j in group.resources) {\n      const resource = group.resources[j];\n      if (resource instanceof UniformGroup) {\n        if (resource.ubo) {\n          const resName = shader._uniformBindMap[i][Number(j)];\n          funcFragments.push(`\n                        sS.bindUniformBlock(\n                            resources[${j}],\n                            '${resName}',\n                            ${shader.glProgram._uniformBlockData[resName].index}\n                        );\n                    `);\n        } else {\n          funcFragments.push(`\n                        ugS.updateUniformGroup(resources[${j}], p, sD);\n                    `);\n        }\n      } else if (resource instanceof BufferResource) {\n        const resName = shader._uniformBindMap[i][Number(j)];\n        funcFragments.push(`\n                    sS.bindUniformBlock(\n                        resources[${j}],\n                        '${resName}',\n                        ${shader.glProgram._uniformBlockData[resName].index}\n                    );\n                `);\n      } else if (resource instanceof TextureSource) {\n        const uniformName = shader._uniformBindMap[i][j];\n        const uniformData = programData.uniformData[uniformName];\n        if (uniformData) {\n          if (!addedTextreSystem) {\n            addedTextreSystem = true;\n            headerFragments.push(`\n                        var tS = r.texture;\n                        `);\n          }\n          shaderSystem._gl.uniform1i(uniformData.location, textureCount);\n          funcFragments.push(`\n                        tS.bind(resources[${j}], ${textureCount});\n                    `);\n          textureCount++;\n        }\n      }\n    }\n  }\n  const functionSource = [...headerFragments, ...funcFragments].join(\"\\n\");\n  return new Function(\"r\", \"s\", \"sD\", functionSource);\n}\nclass GlProgramData {\n  /**\n   * Makes a new Pixi program.\n   * @param program - webgl program\n   * @param uniformData - uniforms\n   */\n  constructor(program, uniformData) {\n    this.program = program;\n    this.uniformData = uniformData;\n    this.uniformGroups = {};\n    this.uniformDirtyGroups = {};\n    this.uniformBlockBindings = {};\n  }\n  /** Destroys this program. */\n  destroy() {\n    this.uniformData = null;\n    this.uniformGroups = null;\n    this.uniformDirtyGroups = null;\n    this.uniformBlockBindings = null;\n    this.program = null;\n  }\n}\nfunction compileShader(gl, type, src) {\n  const shader = gl.createShader(type);\n  gl.shaderSource(shader, src);\n  gl.compileShader(shader);\n  return shader;\n}\nfunction booleanArray(size) {\n  const array = new Array(size);\n  for (let i = 0; i < array.length; i++) {\n    array[i] = false;\n  }\n  return array;\n}\nfunction defaultValue(type, size) {\n  switch (type) {\n    case \"float\":\n      return 0;\n    case \"vec2\":\n      return new Float32Array(2 * size);\n    case \"vec3\":\n      return new Float32Array(3 * size);\n    case \"vec4\":\n      return new Float32Array(4 * size);\n    case \"int\":\n    case \"uint\":\n    case \"sampler2D\":\n    case \"sampler2DArray\":\n      return 0;\n    case \"ivec2\":\n      return new Int32Array(2 * size);\n    case \"ivec3\":\n      return new Int32Array(3 * size);\n    case \"ivec4\":\n      return new Int32Array(4 * size);\n    case \"uvec2\":\n      return new Uint32Array(2 * size);\n    case \"uvec3\":\n      return new Uint32Array(3 * size);\n    case \"uvec4\":\n      return new Uint32Array(4 * size);\n    case \"bool\":\n      return false;\n    case \"bvec2\":\n      return booleanArray(2 * size);\n    case \"bvec3\":\n      return booleanArray(3 * size);\n    case \"bvec4\":\n      return booleanArray(4 * size);\n    case \"mat2\":\n      return new Float32Array([\n        1,\n        0,\n        0,\n        1\n      ]);\n    case \"mat3\":\n      return new Float32Array([\n        1,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        1\n      ]);\n    case \"mat4\":\n      return new Float32Array([\n        1,\n        0,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        0,\n        1,\n        0,\n        0,\n        0,\n        0,\n        1\n      ]);\n  }\n  return null;\n}\nlet GL_TABLE = null;\nconst GL_TO_GLSL_TYPES = {\n  FLOAT: \"float\",\n  FLOAT_VEC2: \"vec2\",\n  FLOAT_VEC3: \"vec3\",\n  FLOAT_VEC4: \"vec4\",\n  INT: \"int\",\n  INT_VEC2: \"ivec2\",\n  INT_VEC3: \"ivec3\",\n  INT_VEC4: \"ivec4\",\n  UNSIGNED_INT: \"uint\",\n  UNSIGNED_INT_VEC2: \"uvec2\",\n  UNSIGNED_INT_VEC3: \"uvec3\",\n  UNSIGNED_INT_VEC4: \"uvec4\",\n  BOOL: \"bool\",\n  BOOL_VEC2: \"bvec2\",\n  BOOL_VEC3: \"bvec3\",\n  BOOL_VEC4: \"bvec4\",\n  FLOAT_MAT2: \"mat2\",\n  FLOAT_MAT3: \"mat3\",\n  FLOAT_MAT4: \"mat4\",\n  SAMPLER_2D: \"sampler2D\",\n  INT_SAMPLER_2D: \"sampler2D\",\n  UNSIGNED_INT_SAMPLER_2D: \"sampler2D\",\n  SAMPLER_CUBE: \"samplerCube\",\n  INT_SAMPLER_CUBE: \"samplerCube\",\n  UNSIGNED_INT_SAMPLER_CUBE: \"samplerCube\",\n  SAMPLER_2D_ARRAY: \"sampler2DArray\",\n  INT_SAMPLER_2D_ARRAY: \"sampler2DArray\",\n  UNSIGNED_INT_SAMPLER_2D_ARRAY: \"sampler2DArray\"\n};\nconst GLSL_TO_VERTEX_TYPES = {\n  float: \"float32\",\n  vec2: \"float32x2\",\n  vec3: \"float32x3\",\n  vec4: \"float32x4\",\n  int: \"sint32\",\n  ivec2: \"sint32x2\",\n  ivec3: \"sint32x3\",\n  ivec4: \"sint32x4\",\n  uint: \"uint32\",\n  uvec2: \"uint32x2\",\n  uvec3: \"uint32x3\",\n  uvec4: \"uint32x4\",\n  bool: \"uint32\",\n  bvec2: \"uint32x2\",\n  bvec3: \"uint32x3\",\n  bvec4: \"uint32x4\"\n};\nfunction mapType(gl, type) {\n  if (!GL_TABLE) {\n    const typeNames = Object.keys(GL_TO_GLSL_TYPES);\n    GL_TABLE = {};\n    for (let i = 0; i < typeNames.length; ++i) {\n      const tn = typeNames[i];\n      GL_TABLE[gl[tn]] = GL_TO_GLSL_TYPES[tn];\n    }\n  }\n  return GL_TABLE[type];\n}\nfunction mapGlToVertexFormat(gl, type) {\n  const typeValue = mapType(gl, type);\n  return GLSL_TO_VERTEX_TYPES[typeValue] || \"float32\";\n}\nfunction extractAttributesFromGlProgram(program, gl, sortAttributes = false) {\n  const attributes = {};\n  const totalAttributes = gl.getProgramParameter(program, gl.ACTIVE_ATTRIBUTES);\n  for (let i = 0; i < totalAttributes; i++) {\n    const attribData = gl.getActiveAttrib(program, i);\n    if (attribData.name.startsWith(\"gl_\")) {\n      continue;\n    }\n    const format = mapGlToVertexFormat(gl, attribData.type);\n    attributes[attribData.name] = {\n      location: 0,\n      // set further down..\n      format,\n      stride: getAttributeInfoFromFormat(format).stride,\n      offset: 0,\n      instance: false,\n      start: 0\n    };\n  }\n  const keys = Object.keys(attributes);\n  if (sortAttributes) {\n    keys.sort((a, b) => a > b ? 1 : -1);\n    for (let i = 0; i < keys.length; i++) {\n      attributes[keys[i]].location = i;\n      gl.bindAttribLocation(program, i, keys[i]);\n    }\n    gl.linkProgram(program);\n  } else {\n    for (let i = 0; i < keys.length; i++) {\n      attributes[keys[i]].location = gl.getAttribLocation(program, keys[i]);\n    }\n  }\n  return attributes;\n}\nfunction getUboData(program, gl) {\n  if (!gl.ACTIVE_UNIFORM_BLOCKS)\n    return {};\n  const uniformBlocks = {};\n  const totalUniformsBlocks = gl.getProgramParameter(program, gl.ACTIVE_UNIFORM_BLOCKS);\n  for (let i = 0; i < totalUniformsBlocks; i++) {\n    const name = gl.getActiveUniformBlockName(program, i);\n    const uniformBlockIndex = gl.getUniformBlockIndex(program, name);\n    const size = gl.getActiveUniformBlockParameter(program, i, gl.UNIFORM_BLOCK_DATA_SIZE);\n    uniformBlocks[name] = {\n      name,\n      index: uniformBlockIndex,\n      size\n    };\n  }\n  return uniformBlocks;\n}\nfunction getUniformData(program, gl) {\n  const uniforms = {};\n  const totalUniforms = gl.getProgramParameter(program, gl.ACTIVE_UNIFORMS);\n  for (let i = 0; i < totalUniforms; i++) {\n    const uniformData = gl.getActiveUniform(program, i);\n    const name = uniformData.name.replace(/\\[.*?\\]$/, \"\");\n    const isArray = !!uniformData.name.match(/\\[.*?\\]$/);\n    const type = mapType(gl, uniformData.type);\n    uniforms[name] = {\n      name,\n      index: i,\n      type,\n      size: uniformData.size,\n      isArray,\n      value: defaultValue(type, uniformData.size)\n    };\n  }\n  return uniforms;\n}\nfunction logPrettyShaderError(gl, shader) {\n  const shaderSrc = gl.getShaderSource(shader).split(\"\\n\").map((line, index) => `${index}: ${line}`);\n  const shaderLog = gl.getShaderInfoLog(shader);\n  const splitShader = shaderLog.split(\"\\n\");\n  const dedupe = {};\n  const lineNumbers = splitShader.map((line) => parseFloat(line.replace(/^ERROR\\: 0\\:([\\d]+)\\:.*$/, \"$1\"))).filter((n) => {\n    if (n && !dedupe[n]) {\n      dedupe[n] = true;\n      return true;\n    }\n    return false;\n  });\n  const logArgs = [\"\"];\n  lineNumbers.forEach((number) => {\n    shaderSrc[number - 1] = `%c${shaderSrc[number - 1]}%c`;\n    logArgs.push(\"background: #FF0000; color:#FFFFFF; font-size: 10px\", \"font-size: 10px\");\n  });\n  const fragmentSourceToLog = shaderSrc.join(\"\\n\");\n  logArgs[0] = fragmentSourceToLog;\n  console.error(shaderLog);\n  console.groupCollapsed(\"click to view full shader code\");\n  console.warn(...logArgs);\n  console.groupEnd();\n}\nfunction logProgramError(gl, program, vertexShader, fragmentShader) {\n  if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {\n    if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {\n      logPrettyShaderError(gl, vertexShader);\n    }\n    if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {\n      logPrettyShaderError(gl, fragmentShader);\n    }\n    console.error(\"PixiJS Error: Could not initialize shader.\");\n    if (gl.getProgramInfoLog(program) !== \"\") {\n      console.warn(\"PixiJS Warning: gl.getProgramInfoLog()\", gl.getProgramInfoLog(program));\n    }\n  }\n}\nfunction generateProgram(gl, program) {\n  const glVertShader = compileShader(gl, gl.VERTEX_SHADER, program.vertex);\n  const glFragShader = compileShader(gl, gl.FRAGMENT_SHADER, program.fragment);\n  const webGLProgram = gl.createProgram();\n  gl.attachShader(webGLProgram, glVertShader);\n  gl.attachShader(webGLProgram, glFragShader);\n  const transformFeedbackVaryings = program.transformFeedbackVaryings;\n  if (transformFeedbackVaryings) {\n    if (typeof gl.transformFeedbackVaryings !== \"function\") {\n      warn(`TransformFeedback is not supported but TransformFeedbackVaryings are given.`);\n    } else {\n      gl.transformFeedbackVaryings(\n        webGLProgram,\n        transformFeedbackVaryings.names,\n        transformFeedbackVaryings.bufferMode === \"separate\" ? gl.SEPARATE_ATTRIBS : gl.INTERLEAVED_ATTRIBS\n      );\n    }\n  }\n  gl.linkProgram(webGLProgram);\n  if (!gl.getProgramParameter(webGLProgram, gl.LINK_STATUS)) {\n    logProgramError(gl, webGLProgram, glVertShader, glFragShader);\n  }\n  program._attributeData = extractAttributesFromGlProgram(\n    webGLProgram,\n    gl,\n    !/^[ \\t]*#[ \\t]*version[ \\t]+300[ \\t]+es[ \\t]*$/m.test(program.vertex)\n  );\n  program._uniformData = getUniformData(webGLProgram, gl);\n  program._uniformBlockData = getUboData(webGLProgram, gl);\n  gl.deleteShader(glVertShader);\n  gl.deleteShader(glFragShader);\n  const uniformData = {};\n  for (const i in program._uniformData) {\n    const data = program._uniformData[i];\n    uniformData[i] = {\n      location: gl.getUniformLocation(webGLProgram, i),\n      value: defaultValue(data.type, data.size)\n    };\n  }\n  const glProgram = new GlProgramData(webGLProgram, uniformData);\n  return glProgram;\n}\nconst defaultSyncData = {\n  textureCount: 0,\n  blockIndex: 0\n};\nclass GlShaderSystem {\n  constructor(renderer) {\n    this._activeProgram = null;\n    this._programDataHash = /* @__PURE__ */ Object.create(null);\n    this._shaderSyncFunctions = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_programDataHash\");\n  }\n  contextChange(gl) {\n    this._gl = gl;\n    this._programDataHash = /* @__PURE__ */ Object.create(null);\n    this._shaderSyncFunctions = /* @__PURE__ */ Object.create(null);\n    this._activeProgram = null;\n    this.maxTextures = getMaxTexturesPerBatch();\n  }\n  /**\n   * Changes the current shader to the one given in parameter.\n   * @param shader - the new shader\n   * @param skipSync - false if the shader should automatically sync its uniforms.\n   * @returns the glProgram that belongs to the shader.\n   */\n  bind(shader, skipSync) {\n    this._setProgram(shader.glProgram);\n    if (skipSync)\n      return;\n    defaultSyncData.textureCount = 0;\n    defaultSyncData.blockIndex = 0;\n    let syncFunction = this._shaderSyncFunctions[shader.glProgram._key];\n    if (!syncFunction) {\n      syncFunction = this._shaderSyncFunctions[shader.glProgram._key] = this._generateShaderSync(shader, this);\n    }\n    this._renderer.buffer.nextBindBase(!!shader.glProgram.transformFeedbackVaryings);\n    syncFunction(this._renderer, shader, defaultSyncData);\n  }\n  /**\n   * Updates the uniform group.\n   * @param uniformGroup - the uniform group to update\n   */\n  updateUniformGroup(uniformGroup) {\n    this._renderer.uniformGroup.updateUniformGroup(uniformGroup, this._activeProgram, defaultSyncData);\n  }\n  /**\n   * Binds a uniform block to the shader.\n   * @param uniformGroup - the uniform group to bind\n   * @param name - the name of the uniform block\n   * @param index - the index of the uniform block\n   */\n  bindUniformBlock(uniformGroup, name, index = 0) {\n    const bufferSystem = this._renderer.buffer;\n    const programData = this._getProgramData(this._activeProgram);\n    const isBufferResource = uniformGroup._bufferResource;\n    if (!isBufferResource) {\n      this._renderer.ubo.updateUniformGroup(uniformGroup);\n    }\n    const buffer = uniformGroup.buffer;\n    const glBuffer = bufferSystem.updateBuffer(buffer);\n    const boundLocation = bufferSystem.freeLocationForBufferBase(glBuffer);\n    if (isBufferResource) {\n      const { offset, size } = uniformGroup;\n      if (offset === 0 && size === buffer.data.byteLength) {\n        bufferSystem.bindBufferBase(glBuffer, boundLocation);\n      } else {\n        bufferSystem.bindBufferRange(glBuffer, boundLocation, offset);\n      }\n    } else if (bufferSystem.getLastBindBaseLocation(glBuffer) !== boundLocation) {\n      bufferSystem.bindBufferBase(glBuffer, boundLocation);\n    }\n    const uniformBlockIndex = this._activeProgram._uniformBlockData[name].index;\n    if (programData.uniformBlockBindings[index] === boundLocation)\n      return;\n    programData.uniformBlockBindings[index] = boundLocation;\n    this._renderer.gl.uniformBlockBinding(programData.program, uniformBlockIndex, boundLocation);\n  }\n  _setProgram(program) {\n    if (this._activeProgram === program)\n      return;\n    this._activeProgram = program;\n    const programData = this._getProgramData(program);\n    this._gl.useProgram(programData.program);\n  }\n  /**\n   * @param program - the program to get the data for\n   * @internal\n   * @private\n   */\n  _getProgramData(program) {\n    return this._programDataHash[program._key] || this._createProgramData(program);\n  }\n  _createProgramData(program) {\n    const key = program._key;\n    this._programDataHash[key] = generateProgram(this._gl, program);\n    return this._programDataHash[key];\n  }\n  destroy() {\n    for (const key of Object.keys(this._programDataHash)) {\n      const programData = this._programDataHash[key];\n      programData.destroy();\n      this._programDataHash[key] = null;\n    }\n    this._programDataHash = null;\n  }\n  /**\n   * Creates a function that can be executed that will sync the shader as efficiently as possible.\n   * Overridden by the unsafe eval package if you don't want eval used in your project.\n   * @param shader - the shader to generate the sync function for\n   * @param shaderSystem - the shader system to use\n   * @returns - the generated sync function\n   * @ignore\n   */\n  _generateShaderSync(shader, shaderSystem) {\n    return generateShaderSyncCode(shader, shaderSystem);\n  }\n  resetState() {\n    this._activeProgram = null;\n  }\n}\nGlShaderSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"shader\"\n};\nconst UNIFORM_TO_SINGLE_SETTERS = {\n  f32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1f(location, v);\n        }`,\n  \"vec2<f32>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2f(location, v[0], v[1]);\n        }`,\n  \"vec3<f32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3f(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<f32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4f(location, v[0], v[1], v[2], v[3]);\n        }`,\n  i32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1i(location, v);\n        }`,\n  \"vec2<i32>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2i(location, v[0], v[1]);\n        }`,\n  \"vec3<i32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3i(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<i32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n        }`,\n  u32: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1ui(location, v);\n        }`,\n  \"vec2<u32>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2ui(location, v[0], v[1]);\n        }`,\n  \"vec3<u32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3ui(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<u32>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4ui(location, v[0], v[1], v[2], v[3]);\n        }`,\n  bool: `if (cv !== v) {\n            cu.value = v;\n            gl.uniform1i(location, v);\n        }`,\n  \"vec2<bool>\": `if (cv[0] !== v[0] || cv[1] !== v[1]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            gl.uniform2i(location, v[0], v[1]);\n        }`,\n  \"vec3<bool>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            gl.uniform3i(location, v[0], v[1], v[2]);\n        }`,\n  \"vec4<bool>\": `if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {\n            cv[0] = v[0];\n            cv[1] = v[1];\n            cv[2] = v[2];\n            cv[3] = v[3];\n            gl.uniform4i(location, v[0], v[1], v[2], v[3]);\n        }`,\n  \"mat2x2<f32>\": `gl.uniformMatrix2fv(location, false, v);`,\n  \"mat3x3<f32>\": `gl.uniformMatrix3fv(location, false, v);`,\n  \"mat4x4<f32>\": `gl.uniformMatrix4fv(location, false, v);`\n};\nconst UNIFORM_TO_ARRAY_SETTERS = {\n  f32: `gl.uniform1fv(location, v);`,\n  \"vec2<f32>\": `gl.uniform2fv(location, v);`,\n  \"vec3<f32>\": `gl.uniform3fv(location, v);`,\n  \"vec4<f32>\": `gl.uniform4fv(location, v);`,\n  \"mat2x2<f32>\": `gl.uniformMatrix2fv(location, false, v);`,\n  \"mat3x3<f32>\": `gl.uniformMatrix3fv(location, false, v);`,\n  \"mat4x4<f32>\": `gl.uniformMatrix4fv(location, false, v);`,\n  i32: `gl.uniform1iv(location, v);`,\n  \"vec2<i32>\": `gl.uniform2iv(location, v);`,\n  \"vec3<i32>\": `gl.uniform3iv(location, v);`,\n  \"vec4<i32>\": `gl.uniform4iv(location, v);`,\n  u32: `gl.uniform1iv(location, v);`,\n  \"vec2<u32>\": `gl.uniform2iv(location, v);`,\n  \"vec3<u32>\": `gl.uniform3iv(location, v);`,\n  \"vec4<u32>\": `gl.uniform4iv(location, v);`,\n  bool: `gl.uniform1iv(location, v);`,\n  \"vec2<bool>\": `gl.uniform2iv(location, v);`,\n  \"vec3<bool>\": `gl.uniform3iv(location, v);`,\n  \"vec4<bool>\": `gl.uniform4iv(location, v);`\n};\nfunction generateUniformsSync(group, uniformData) {\n  const funcFragments = [`\n        var v = null;\n        var cv = null;\n        var cu = null;\n        var t = 0;\n        var gl = renderer.gl;\n        var name = null;\n    `];\n  for (const i in group.uniforms) {\n    if (!uniformData[i]) {\n      if (group.uniforms[i] instanceof UniformGroup) {\n        if (group.uniforms[i].ubo) {\n          funcFragments.push(`\n                        renderer.shader.bindUniformBlock(uv.${i}, \"${i}\");\n                    `);\n        } else {\n          funcFragments.push(`\n                        renderer.shader.updateUniformGroup(uv.${i});\n                    `);\n        }\n      } else if (group.uniforms[i] instanceof BufferResource) {\n        funcFragments.push(`\n                        renderer.shader.bindBufferResource(uv.${i}, \"${i}\");\n                    `);\n      }\n      continue;\n    }\n    const uniform = group.uniformStructures[i];\n    let parsed = false;\n    for (let j = 0; j < uniformParsers.length; j++) {\n      const parser = uniformParsers[j];\n      if (uniform.type === parser.type && parser.test(uniform)) {\n        funcFragments.push(`name = \"${i}\";`, uniformParsers[j].uniform);\n        parsed = true;\n        break;\n      }\n    }\n    if (!parsed) {\n      const templateType = uniform.size === 1 ? UNIFORM_TO_SINGLE_SETTERS : UNIFORM_TO_ARRAY_SETTERS;\n      const template = templateType[uniform.type].replace(\"location\", `ud[\"${i}\"].location`);\n      funcFragments.push(`\n            cu = ud[\"${i}\"];\n            cv = cu.value;\n            v = uv[\"${i}\"];\n            ${template};`);\n    }\n  }\n  return new Function(\"ud\", \"uv\", \"renderer\", \"syncData\", funcFragments.join(\"\\n\"));\n}\nclass GlUniformGroupSystem {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this._cache = {};\n    this._uniformGroupSyncHash = {};\n    this._renderer = renderer;\n    this.gl = null;\n    this._cache = {};\n  }\n  contextChange(gl) {\n    this.gl = gl;\n  }\n  /**\n   * Uploads the uniforms values to the currently bound shader.\n   * @param group - the uniforms values that be applied to the current shader\n   * @param program\n   * @param syncData\n   * @param syncData.textureCount\n   */\n  updateUniformGroup(group, program, syncData) {\n    const programData = this._renderer.shader._getProgramData(program);\n    if (!group.isStatic || group._dirtyId !== programData.uniformDirtyGroups[group.uid]) {\n      programData.uniformDirtyGroups[group.uid] = group._dirtyId;\n      const syncFunc = this._getUniformSyncFunction(group, program);\n      syncFunc(programData.uniformData, group.uniforms, this._renderer, syncData);\n    }\n  }\n  /**\n   * Overridable by the pixi.js/unsafe-eval package to use static syncUniforms instead.\n   * @param group\n   * @param program\n   */\n  _getUniformSyncFunction(group, program) {\n    return this._uniformGroupSyncHash[group._signature]?.[program._key] || this._createUniformSyncFunction(group, program);\n  }\n  _createUniformSyncFunction(group, program) {\n    const uniformGroupSyncHash = this._uniformGroupSyncHash[group._signature] || (this._uniformGroupSyncHash[group._signature] = {});\n    const id = this._getSignature(group, program._uniformData, \"u\");\n    if (!this._cache[id]) {\n      this._cache[id] = this._generateUniformsSync(group, program._uniformData);\n    }\n    uniformGroupSyncHash[program._key] = this._cache[id];\n    return uniformGroupSyncHash[program._key];\n  }\n  _generateUniformsSync(group, uniformData) {\n    return generateUniformsSync(group, uniformData);\n  }\n  /**\n   * Takes a uniform group and data and generates a unique signature for them.\n   * @param group - The uniform group to get signature of\n   * @param group.uniforms\n   * @param uniformData - Uniform information generated by the shader\n   * @param preFix\n   * @returns Unique signature of the uniform group\n   */\n  _getSignature(group, uniformData, preFix) {\n    const uniforms = group.uniforms;\n    const strings = [`${preFix}-`];\n    for (const i in uniforms) {\n      strings.push(i);\n      if (uniformData[i]) {\n        strings.push(uniformData[i].type);\n      }\n    }\n    return strings.join(\"-\");\n  }\n  /** Destroys this System and removes all its textures. */\n  destroy() {\n    this._renderer = null;\n    this._cache = null;\n  }\n}\nGlUniformGroupSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"uniformGroup\"\n};\nfunction mapWebGLBlendModesToPixi(gl) {\n  const blendMap = {};\n  blendMap.normal = [gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.add = [gl.ONE, gl.ONE];\n  blendMap.multiply = [gl.DST_COLOR, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.screen = [gl.ONE, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.none = [0, 0];\n  blendMap[\"normal-npm\"] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap[\"add-npm\"] = [gl.SRC_ALPHA, gl.ONE, gl.ONE, gl.ONE];\n  blendMap[\"screen-npm\"] = [gl.SRC_ALPHA, gl.ONE_MINUS_SRC_COLOR, gl.ONE, gl.ONE_MINUS_SRC_ALPHA];\n  blendMap.erase = [gl.ZERO, gl.ONE_MINUS_SRC_ALPHA];\n  const isWebGl2 = !(gl instanceof DOMAdapter.get().getWebGLRenderingContext());\n  if (isWebGl2) {\n    blendMap.min = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.MIN, gl.MIN];\n    blendMap.max = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, gl.MAX, gl.MAX];\n  } else {\n    const ext = gl.getExtension(\"EXT_blend_minmax\");\n    if (ext) {\n      blendMap.min = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, ext.MIN_EXT, ext.MIN_EXT];\n      blendMap.max = [gl.ONE, gl.ONE, gl.ONE, gl.ONE, ext.MAX_EXT, ext.MAX_EXT];\n    }\n  }\n  return blendMap;\n}\nconst BLEND = 0;\nconst OFFSET = 1;\nconst CULLING = 2;\nconst DEPTH_TEST = 3;\nconst WINDING = 4;\nconst DEPTH_MASK = 5;\nconst _GlStateSystem = class _GlStateSystem2 {\n  constructor() {\n    this.gl = null;\n    this.stateId = 0;\n    this.polygonOffset = 0;\n    this.blendMode = \"none\";\n    this._blendEq = false;\n    this.map = [];\n    this.map[BLEND] = this.setBlend;\n    this.map[OFFSET] = this.setOffset;\n    this.map[CULLING] = this.setCullFace;\n    this.map[DEPTH_TEST] = this.setDepthTest;\n    this.map[WINDING] = this.setFrontFace;\n    this.map[DEPTH_MASK] = this.setDepthMask;\n    this.checks = [];\n    this.defaultState = State.for2d();\n  }\n  contextChange(gl) {\n    this.gl = gl;\n    this.blendModesMap = mapWebGLBlendModesToPixi(gl);\n    this.resetState();\n  }\n  /**\n   * Sets the current state\n   * @param {*} state - The state to set.\n   */\n  set(state) {\n    state || (state = this.defaultState);\n    if (this.stateId !== state.data) {\n      let diff = this.stateId ^ state.data;\n      let i = 0;\n      while (diff) {\n        if (diff & 1) {\n          this.map[i].call(this, !!(state.data & 1 << i));\n        }\n        diff >>= 1;\n        i++;\n      }\n      this.stateId = state.data;\n    }\n    for (let i = 0; i < this.checks.length; i++) {\n      this.checks[i](this, state);\n    }\n  }\n  /**\n   * Sets the state, when previous state is unknown.\n   * @param {*} state - The state to set\n   */\n  forceState(state) {\n    state || (state = this.defaultState);\n    for (let i = 0; i < this.map.length; i++) {\n      this.map[i].call(this, !!(state.data & 1 << i));\n    }\n    for (let i = 0; i < this.checks.length; i++) {\n      this.checks[i](this, state);\n    }\n    this.stateId = state.data;\n  }\n  /**\n   * Sets whether to enable or disable blending.\n   * @param value - Turn on or off WebGl blending.\n   */\n  setBlend(value) {\n    this._updateCheck(_GlStateSystem2._checkBlendMode, value);\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.BLEND);\n  }\n  /**\n   * Sets whether to enable or disable polygon offset fill.\n   * @param value - Turn on or off webgl polygon offset testing.\n   */\n  setOffset(value) {\n    this._updateCheck(_GlStateSystem2._checkPolygonOffset, value);\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.POLYGON_OFFSET_FILL);\n  }\n  /**\n   * Sets whether to enable or disable depth test.\n   * @param value - Turn on or off webgl depth testing.\n   */\n  setDepthTest(value) {\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.DEPTH_TEST);\n  }\n  /**\n   * Sets whether to enable or disable depth mask.\n   * @param value - Turn on or off webgl depth mask.\n   */\n  setDepthMask(value) {\n    this.gl.depthMask(value);\n  }\n  /**\n   * Sets whether to enable or disable cull face.\n   * @param {boolean} value - Turn on or off webgl cull face.\n   */\n  setCullFace(value) {\n    this.gl[value ? \"enable\" : \"disable\"](this.gl.CULL_FACE);\n  }\n  /**\n   * Sets the gl front face.\n   * @param {boolean} value - true is clockwise and false is counter-clockwise\n   */\n  setFrontFace(value) {\n    this.gl.frontFace(this.gl[value ? \"CW\" : \"CCW\"]);\n  }\n  /**\n   * Sets the blend mode.\n   * @param {number} value - The blend mode to set to.\n   */\n  setBlendMode(value) {\n    if (!this.blendModesMap[value]) {\n      value = \"normal\";\n    }\n    if (value === this.blendMode) {\n      return;\n    }\n    this.blendMode = value;\n    const mode = this.blendModesMap[value];\n    const gl = this.gl;\n    if (mode.length === 2) {\n      gl.blendFunc(mode[0], mode[1]);\n    } else {\n      gl.blendFuncSeparate(mode[0], mode[1], mode[2], mode[3]);\n    }\n    if (mode.length === 6) {\n      this._blendEq = true;\n      gl.blendEquationSeparate(mode[4], mode[5]);\n    } else if (this._blendEq) {\n      this._blendEq = false;\n      gl.blendEquationSeparate(gl.FUNC_ADD, gl.FUNC_ADD);\n    }\n  }\n  /**\n   * Sets the polygon offset.\n   * @param {number} value - the polygon offset\n   * @param {number} scale - the polygon offset scale\n   */\n  setPolygonOffset(value, scale) {\n    this.gl.polygonOffset(value, scale);\n  }\n  // used\n  /** Resets all the logic and disables the VAOs. */\n  resetState() {\n    this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, false);\n    this.forceState(this.defaultState);\n    this._blendEq = true;\n    this.blendMode = \"\";\n    this.setBlendMode(\"normal\");\n  }\n  /**\n   * Checks to see which updates should be checked based on which settings have been activated.\n   *\n   * For example, if blend is enabled then we should check the blend modes each time the state is changed\n   * or if polygon fill is activated then we need to check if the polygon offset changes.\n   * The idea is that we only check what we have too.\n   * @param func - the checking function to add or remove\n   * @param value - should the check function be added or removed.\n   */\n  _updateCheck(func, value) {\n    const index = this.checks.indexOf(func);\n    if (value && index === -1) {\n      this.checks.push(func);\n    } else if (!value && index !== -1) {\n      this.checks.splice(index, 1);\n    }\n  }\n  /**\n   * A private little wrapper function that we call to check the blend mode.\n   * @param system - the System to perform the state check on\n   * @param state - the state that the blendMode will pulled from\n   */\n  static _checkBlendMode(system, state) {\n    system.setBlendMode(state.blendMode);\n  }\n  /**\n   * A private little wrapper function that we call to check the polygon offset.\n   * @param system - the System to perform the state check on\n   * @param state - the state that the blendMode will pulled from\n   */\n  static _checkPolygonOffset(system, state) {\n    system.setPolygonOffset(1, state.polygonOffset);\n  }\n  /**\n   * @ignore\n   */\n  destroy() {\n    this.gl = null;\n    this.checks.length = 0;\n  }\n};\n_GlStateSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"state\"\n};\nlet GlStateSystem = _GlStateSystem;\nclass GlTexture {\n  constructor(texture) {\n    this.target = GL_TARGETS.TEXTURE_2D;\n    this.texture = texture;\n    this.width = -1;\n    this.height = -1;\n    this.type = GL_TYPES.UNSIGNED_BYTE;\n    this.internalFormat = GL_FORMATS.RGBA;\n    this.format = GL_FORMATS.RGBA;\n    this.samplerType = 0;\n  }\n}\nconst glUploadBufferImageResource = {\n  id: \"buffer\",\n  upload(source, glTexture, gl) {\n    if (glTexture.width === source.width || glTexture.height === source.height) {\n      gl.texSubImage2D(\n        gl.TEXTURE_2D,\n        0,\n        0,\n        0,\n        source.width,\n        source.height,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    } else {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        source.width,\n        source.height,\n        0,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    }\n    glTexture.width = source.width;\n    glTexture.height = source.height;\n  }\n};\nconst compressedFormatMap = {\n  \"bc1-rgba-unorm\": true,\n  \"bc1-rgba-unorm-srgb\": true,\n  \"bc2-rgba-unorm\": true,\n  \"bc2-rgba-unorm-srgb\": true,\n  \"bc3-rgba-unorm\": true,\n  \"bc3-rgba-unorm-srgb\": true,\n  \"bc4-r-unorm\": true,\n  \"bc4-r-snorm\": true,\n  \"bc5-rg-unorm\": true,\n  \"bc5-rg-snorm\": true,\n  \"bc6h-rgb-ufloat\": true,\n  \"bc6h-rgb-float\": true,\n  \"bc7-rgba-unorm\": true,\n  \"bc7-rgba-unorm-srgb\": true,\n  // ETC2 compressed formats usable if \"texture-compression-etc2\" is both\n  // supported by the device/user agent and enabled in requestDevice.\n  \"etc2-rgb8unorm\": true,\n  \"etc2-rgb8unorm-srgb\": true,\n  \"etc2-rgb8a1unorm\": true,\n  \"etc2-rgb8a1unorm-srgb\": true,\n  \"etc2-rgba8unorm\": true,\n  \"etc2-rgba8unorm-srgb\": true,\n  \"eac-r11unorm\": true,\n  \"eac-r11snorm\": true,\n  \"eac-rg11unorm\": true,\n  \"eac-rg11snorm\": true,\n  // ASTC compressed formats usable if \"texture-compression-astc\" is both\n  // supported by the device/user agent and enabled in requestDevice.\n  \"astc-4x4-unorm\": true,\n  \"astc-4x4-unorm-srgb\": true,\n  \"astc-5x4-unorm\": true,\n  \"astc-5x4-unorm-srgb\": true,\n  \"astc-5x5-unorm\": true,\n  \"astc-5x5-unorm-srgb\": true,\n  \"astc-6x5-unorm\": true,\n  \"astc-6x5-unorm-srgb\": true,\n  \"astc-6x6-unorm\": true,\n  \"astc-6x6-unorm-srgb\": true,\n  \"astc-8x5-unorm\": true,\n  \"astc-8x5-unorm-srgb\": true,\n  \"astc-8x6-unorm\": true,\n  \"astc-8x6-unorm-srgb\": true,\n  \"astc-8x8-unorm\": true,\n  \"astc-8x8-unorm-srgb\": true,\n  \"astc-10x5-unorm\": true,\n  \"astc-10x5-unorm-srgb\": true,\n  \"astc-10x6-unorm\": true,\n  \"astc-10x6-unorm-srgb\": true,\n  \"astc-10x8-unorm\": true,\n  \"astc-10x8-unorm-srgb\": true,\n  \"astc-10x10-unorm\": true,\n  \"astc-10x10-unorm-srgb\": true,\n  \"astc-12x10-unorm\": true,\n  \"astc-12x10-unorm-srgb\": true,\n  \"astc-12x12-unorm\": true,\n  \"astc-12x12-unorm-srgb\": true\n};\nconst glUploadCompressedTextureResource = {\n  id: \"compressed\",\n  upload(source, glTexture, gl) {\n    gl.pixelStorei(gl.UNPACK_ALIGNMENT, 4);\n    let mipWidth = source.pixelWidth;\n    let mipHeight = source.pixelHeight;\n    const compressed = !!compressedFormatMap[source.format];\n    for (let i = 0; i < source.resource.length; i++) {\n      const levelBuffer = source.resource[i];\n      if (compressed) {\n        gl.compressedTexImage2D(\n          gl.TEXTURE_2D,\n          i,\n          glTexture.internalFormat,\n          mipWidth,\n          mipHeight,\n          0,\n          levelBuffer\n        );\n      } else {\n        gl.texImage2D(\n          gl.TEXTURE_2D,\n          i,\n          glTexture.internalFormat,\n          mipWidth,\n          mipHeight,\n          0,\n          glTexture.format,\n          glTexture.type,\n          levelBuffer\n        );\n      }\n      mipWidth = Math.max(mipWidth >> 1, 1);\n      mipHeight = Math.max(mipHeight >> 1, 1);\n    }\n  }\n};\nconst glUploadImageResource = {\n  id: \"image\",\n  upload(source, glTexture, gl, webGLVersion) {\n    const glWidth = glTexture.width;\n    const glHeight = glTexture.height;\n    const textureWidth = source.pixelWidth;\n    const textureHeight = source.pixelHeight;\n    const resourceWidth = source.resourceWidth;\n    const resourceHeight = source.resourceHeight;\n    if (resourceWidth < textureWidth || resourceHeight < textureHeight) {\n      if (glWidth !== textureWidth || glHeight !== textureHeight) {\n        gl.texImage2D(\n          glTexture.target,\n          0,\n          glTexture.internalFormat,\n          textureWidth,\n          textureHeight,\n          0,\n          glTexture.format,\n          glTexture.type,\n          null\n        );\n      }\n      if (webGLVersion === 2) {\n        gl.texSubImage2D(\n          gl.TEXTURE_2D,\n          0,\n          0,\n          0,\n          resourceWidth,\n          resourceHeight,\n          glTexture.format,\n          glTexture.type,\n          source.resource\n        );\n      } else {\n        gl.texSubImage2D(\n          gl.TEXTURE_2D,\n          0,\n          0,\n          0,\n          glTexture.format,\n          glTexture.type,\n          source.resource\n        );\n      }\n    } else if (glWidth === textureWidth && glHeight === textureHeight) {\n      gl.texSubImage2D(\n        gl.TEXTURE_2D,\n        0,\n        0,\n        0,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    } else if (webGLVersion === 2) {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        textureWidth,\n        textureHeight,\n        0,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    } else {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        glTexture.format,\n        glTexture.type,\n        source.resource\n      );\n    }\n    glTexture.width = textureWidth;\n    glTexture.height = textureHeight;\n  }\n};\nconst glUploadVideoResource = {\n  id: \"video\",\n  upload(source, glTexture, gl, webGLVersion) {\n    if (!source.isValid) {\n      gl.texImage2D(\n        glTexture.target,\n        0,\n        glTexture.internalFormat,\n        1,\n        1,\n        0,\n        glTexture.format,\n        glTexture.type,\n        null\n      );\n      return;\n    }\n    glUploadImageResource.upload(source, glTexture, gl, webGLVersion);\n  }\n};\nconst scaleModeToGlFilter = {\n  linear: 9729,\n  nearest: 9728\n};\nconst mipmapScaleModeToGlFilter = {\n  linear: {\n    linear: 9987,\n    nearest: 9985\n  },\n  nearest: {\n    linear: 9986,\n    nearest: 9984\n  }\n};\nconst wrapModeToGlAddress = {\n  \"clamp-to-edge\": 33071,\n  repeat: 10497,\n  \"mirror-repeat\": 33648\n};\nconst compareModeToGlCompare = {\n  never: 512,\n  less: 513,\n  equal: 514,\n  \"less-equal\": 515,\n  greater: 516,\n  \"not-equal\": 517,\n  \"greater-equal\": 518,\n  always: 519\n};\nfunction applyStyleParams(style, gl, mipmaps, anisotropicExt, glFunctionName, firstParam, forceClamp, firstCreation) {\n  const castParam = firstParam;\n  if (!firstCreation || style.addressModeU !== \"repeat\" || style.addressModeV !== \"repeat\" || style.addressModeW !== \"repeat\") {\n    const wrapModeS = wrapModeToGlAddress[forceClamp ? \"clamp-to-edge\" : style.addressModeU];\n    const wrapModeT = wrapModeToGlAddress[forceClamp ? \"clamp-to-edge\" : style.addressModeV];\n    const wrapModeR = wrapModeToGlAddress[forceClamp ? \"clamp-to-edge\" : style.addressModeW];\n    gl[glFunctionName](castParam, gl.TEXTURE_WRAP_S, wrapModeS);\n    gl[glFunctionName](castParam, gl.TEXTURE_WRAP_T, wrapModeT);\n    if (gl.TEXTURE_WRAP_R)\n      gl[glFunctionName](castParam, gl.TEXTURE_WRAP_R, wrapModeR);\n  }\n  if (!firstCreation || style.magFilter !== \"linear\") {\n    gl[glFunctionName](castParam, gl.TEXTURE_MAG_FILTER, scaleModeToGlFilter[style.magFilter]);\n  }\n  if (mipmaps) {\n    if (!firstCreation || style.mipmapFilter !== \"linear\") {\n      const glFilterMode = mipmapScaleModeToGlFilter[style.minFilter][style.mipmapFilter];\n      gl[glFunctionName](castParam, gl.TEXTURE_MIN_FILTER, glFilterMode);\n    }\n  } else {\n    gl[glFunctionName](castParam, gl.TEXTURE_MIN_FILTER, scaleModeToGlFilter[style.minFilter]);\n  }\n  if (anisotropicExt && style.maxAnisotropy > 1) {\n    const level = Math.min(style.maxAnisotropy, gl.getParameter(anisotropicExt.MAX_TEXTURE_MAX_ANISOTROPY_EXT));\n    gl[glFunctionName](castParam, anisotropicExt.TEXTURE_MAX_ANISOTROPY_EXT, level);\n  }\n  if (style.compare) {\n    gl[glFunctionName](castParam, gl.TEXTURE_COMPARE_FUNC, compareModeToGlCompare[style.compare]);\n  }\n}\nfunction mapFormatToGlFormat(gl) {\n  return {\n    // 8-bit formats\n    r8unorm: gl.RED,\n    r8snorm: gl.RED,\n    r8uint: gl.RED,\n    r8sint: gl.RED,\n    // 16-bit formats\n    r16uint: gl.RED,\n    r16sint: gl.RED,\n    r16float: gl.RED,\n    rg8unorm: gl.RG,\n    rg8snorm: gl.RG,\n    rg8uint: gl.RG,\n    rg8sint: gl.RG,\n    // 32-bit formats\n    r32uint: gl.RED,\n    r32sint: gl.RED,\n    r32float: gl.RED,\n    rg16uint: gl.RG,\n    rg16sint: gl.RG,\n    rg16float: gl.RG,\n    rgba8unorm: gl.RGBA,\n    \"rgba8unorm-srgb\": gl.RGBA,\n    // Packed 32-bit formats\n    rgba8snorm: gl.RGBA,\n    rgba8uint: gl.RGBA,\n    rgba8sint: gl.RGBA,\n    bgra8unorm: gl.RGBA,\n    \"bgra8unorm-srgb\": gl.RGBA,\n    rgb9e5ufloat: gl.RGB,\n    rgb10a2unorm: gl.RGBA,\n    rg11b10ufloat: gl.RGB,\n    // 64-bit formats\n    rg32uint: gl.RG,\n    rg32sint: gl.RG,\n    rg32float: gl.RG,\n    rgba16uint: gl.RGBA,\n    rgba16sint: gl.RGBA,\n    rgba16float: gl.RGBA,\n    // 128-bit formats\n    rgba32uint: gl.RGBA,\n    rgba32sint: gl.RGBA,\n    rgba32float: gl.RGBA,\n    // Depth/stencil formats\n    stencil8: gl.STENCIL_INDEX8,\n    depth16unorm: gl.DEPTH_COMPONENT,\n    depth24plus: gl.DEPTH_COMPONENT,\n    \"depth24plus-stencil8\": gl.DEPTH_STENCIL,\n    depth32float: gl.DEPTH_COMPONENT,\n    \"depth32float-stencil8\": gl.DEPTH_STENCIL\n  };\n}\nfunction mapFormatToGlInternalFormat(gl, extensions2) {\n  let srgb = {};\n  let bgra8unorm = gl.RGBA;\n  if (!(gl instanceof DOMAdapter.get().getWebGLRenderingContext())) {\n    srgb = {\n      \"rgba8unorm-srgb\": gl.SRGB8_ALPHA8,\n      \"bgra8unorm-srgb\": gl.SRGB8_ALPHA8\n    };\n    bgra8unorm = gl.RGBA8;\n  } else if (extensions2.srgb) {\n    srgb = {\n      \"rgba8unorm-srgb\": extensions2.srgb.SRGB8_ALPHA8_EXT,\n      \"bgra8unorm-srgb\": extensions2.srgb.SRGB8_ALPHA8_EXT\n    };\n  }\n  return {\n    // 8-bit formats\n    r8unorm: gl.R8,\n    r8snorm: gl.R8_SNORM,\n    r8uint: gl.R8UI,\n    r8sint: gl.R8I,\n    // 16-bit formats\n    r16uint: gl.R16UI,\n    r16sint: gl.R16I,\n    r16float: gl.R16F,\n    rg8unorm: gl.RG8,\n    rg8snorm: gl.RG8_SNORM,\n    rg8uint: gl.RG8UI,\n    rg8sint: gl.RG8I,\n    // 32-bit formats\n    r32uint: gl.R32UI,\n    r32sint: gl.R32I,\n    r32float: gl.R32F,\n    rg16uint: gl.RG16UI,\n    rg16sint: gl.RG16I,\n    rg16float: gl.RG16F,\n    rgba8unorm: gl.RGBA,\n    ...srgb,\n    // Packed 32-bit formats\n    rgba8snorm: gl.RGBA8_SNORM,\n    rgba8uint: gl.RGBA8UI,\n    rgba8sint: gl.RGBA8I,\n    bgra8unorm,\n    rgb9e5ufloat: gl.RGB9_E5,\n    rgb10a2unorm: gl.RGB10_A2,\n    rg11b10ufloat: gl.R11F_G11F_B10F,\n    // 64-bit formats\n    rg32uint: gl.RG32UI,\n    rg32sint: gl.RG32I,\n    rg32float: gl.RG32F,\n    rgba16uint: gl.RGBA16UI,\n    rgba16sint: gl.RGBA16I,\n    rgba16float: gl.RGBA16F,\n    // 128-bit formats\n    rgba32uint: gl.RGBA32UI,\n    rgba32sint: gl.RGBA32I,\n    rgba32float: gl.RGBA32F,\n    // Depth/stencil formats\n    stencil8: gl.STENCIL_INDEX8,\n    depth16unorm: gl.DEPTH_COMPONENT16,\n    depth24plus: gl.DEPTH_COMPONENT24,\n    \"depth24plus-stencil8\": gl.DEPTH24_STENCIL8,\n    depth32float: gl.DEPTH_COMPONENT32F,\n    \"depth32float-stencil8\": gl.DEPTH32F_STENCIL8,\n    // Compressed formats\n    ...extensions2.s3tc ? {\n      \"bc1-rgba-unorm\": extensions2.s3tc.COMPRESSED_RGBA_S3TC_DXT1_EXT,\n      \"bc2-rgba-unorm\": extensions2.s3tc.COMPRESSED_RGBA_S3TC_DXT3_EXT,\n      \"bc3-rgba-unorm\": extensions2.s3tc.COMPRESSED_RGBA_S3TC_DXT5_EXT\n    } : {},\n    ...extensions2.s3tc_sRGB ? {\n      \"bc1-rgba-unorm-srgb\": extensions2.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,\n      \"bc2-rgba-unorm-srgb\": extensions2.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,\n      \"bc3-rgba-unorm-srgb\": extensions2.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT\n    } : {},\n    ...extensions2.rgtc ? {\n      \"bc4-r-unorm\": extensions2.rgtc.COMPRESSED_RED_RGTC1_EXT,\n      \"bc4-r-snorm\": extensions2.rgtc.COMPRESSED_SIGNED_RED_RGTC1_EXT,\n      \"bc5-rg-unorm\": extensions2.rgtc.COMPRESSED_RED_GREEN_RGTC2_EXT,\n      \"bc5-rg-snorm\": extensions2.rgtc.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT\n    } : {},\n    ...extensions2.bptc ? {\n      \"bc6h-rgb-float\": extensions2.bptc.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT,\n      \"bc6h-rgb-ufloat\": extensions2.bptc.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT,\n      \"bc7-rgba-unorm\": extensions2.bptc.COMPRESSED_RGBA_BPTC_UNORM_EXT,\n      \"bc7-rgba-unorm-srgb\": extensions2.bptc.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT\n    } : {},\n    ...extensions2.etc ? {\n      \"etc2-rgb8unorm\": extensions2.etc.COMPRESSED_RGB8_ETC2,\n      \"etc2-rgb8unorm-srgb\": extensions2.etc.COMPRESSED_SRGB8_ETC2,\n      \"etc2-rgb8a1unorm\": extensions2.etc.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n      \"etc2-rgb8a1unorm-srgb\": extensions2.etc.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,\n      \"etc2-rgba8unorm\": extensions2.etc.COMPRESSED_RGBA8_ETC2_EAC,\n      \"etc2-rgba8unorm-srgb\": extensions2.etc.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,\n      \"eac-r11unorm\": extensions2.etc.COMPRESSED_R11_EAC,\n      // 'eac-r11snorm'\n      \"eac-rg11unorm\": extensions2.etc.COMPRESSED_SIGNED_RG11_EAC\n      // 'eac-rg11snorm'\n    } : {},\n    ...extensions2.astc ? {\n      \"astc-4x4-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_4x4_KHR,\n      \"astc-4x4-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,\n      \"astc-5x4-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_5x4_KHR,\n      \"astc-5x4-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR,\n      \"astc-5x5-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_5x5_KHR,\n      \"astc-5x5-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,\n      \"astc-6x5-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_6x5_KHR,\n      \"astc-6x5-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,\n      \"astc-6x6-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_6x6_KHR,\n      \"astc-6x6-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,\n      \"astc-8x5-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_8x5_KHR,\n      \"astc-8x5-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,\n      \"astc-8x6-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_8x6_KHR,\n      \"astc-8x6-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,\n      \"astc-8x8-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_8x8_KHR,\n      \"astc-8x8-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,\n      \"astc-10x5-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_10x5_KHR,\n      \"astc-10x5-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,\n      \"astc-10x6-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_10x6_KHR,\n      \"astc-10x6-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,\n      \"astc-10x8-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_10x8_KHR,\n      \"astc-10x8-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,\n      \"astc-10x10-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_10x10_KHR,\n      \"astc-10x10-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,\n      \"astc-12x10-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_12x10_KHR,\n      \"astc-12x10-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,\n      \"astc-12x12-unorm\": extensions2.astc.COMPRESSED_RGBA_ASTC_12x12_KHR,\n      \"astc-12x12-unorm-srgb\": extensions2.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR\n    } : {}\n  };\n}\nfunction mapFormatToGlType(gl) {\n  return {\n    // 8-bit formats\n    r8unorm: gl.UNSIGNED_BYTE,\n    r8snorm: gl.BYTE,\n    r8uint: gl.UNSIGNED_BYTE,\n    r8sint: gl.BYTE,\n    // 16-bit formats\n    r16uint: gl.UNSIGNED_SHORT,\n    r16sint: gl.SHORT,\n    r16float: gl.HALF_FLOAT,\n    rg8unorm: gl.UNSIGNED_BYTE,\n    rg8snorm: gl.BYTE,\n    rg8uint: gl.UNSIGNED_BYTE,\n    rg8sint: gl.BYTE,\n    // 32-bit formats\n    r32uint: gl.UNSIGNED_INT,\n    r32sint: gl.INT,\n    r32float: gl.FLOAT,\n    rg16uint: gl.UNSIGNED_SHORT,\n    rg16sint: gl.SHORT,\n    rg16float: gl.HALF_FLOAT,\n    rgba8unorm: gl.UNSIGNED_BYTE,\n    \"rgba8unorm-srgb\": gl.UNSIGNED_BYTE,\n    // Packed 32-bit formats\n    rgba8snorm: gl.BYTE,\n    rgba8uint: gl.UNSIGNED_BYTE,\n    rgba8sint: gl.BYTE,\n    bgra8unorm: gl.UNSIGNED_BYTE,\n    \"bgra8unorm-srgb\": gl.UNSIGNED_BYTE,\n    rgb9e5ufloat: gl.UNSIGNED_INT_5_9_9_9_REV,\n    rgb10a2unorm: gl.UNSIGNED_INT_2_10_10_10_REV,\n    rg11b10ufloat: gl.UNSIGNED_INT_10F_11F_11F_REV,\n    // 64-bit formats\n    rg32uint: gl.UNSIGNED_INT,\n    rg32sint: gl.INT,\n    rg32float: gl.FLOAT,\n    rgba16uint: gl.UNSIGNED_SHORT,\n    rgba16sint: gl.SHORT,\n    rgba16float: gl.HALF_FLOAT,\n    // 128-bit formats\n    rgba32uint: gl.UNSIGNED_INT,\n    rgba32sint: gl.INT,\n    rgba32float: gl.FLOAT,\n    // Depth/stencil formats\n    stencil8: gl.UNSIGNED_BYTE,\n    depth16unorm: gl.UNSIGNED_SHORT,\n    depth24plus: gl.UNSIGNED_INT,\n    \"depth24plus-stencil8\": gl.UNSIGNED_INT_24_8,\n    depth32float: gl.FLOAT,\n    \"depth32float-stencil8\": gl.FLOAT_32_UNSIGNED_INT_24_8_REV\n  };\n}\nconst BYTES_PER_PIXEL = 4;\nclass GlTextureSystem {\n  constructor(renderer) {\n    this.managedTextures = [];\n    this._glTextures = /* @__PURE__ */ Object.create(null);\n    this._glSamplers = /* @__PURE__ */ Object.create(null);\n    this._boundTextures = [];\n    this._activeTextureLocation = -1;\n    this._boundSamplers = /* @__PURE__ */ Object.create(null);\n    this._uploads = {\n      image: glUploadImageResource,\n      buffer: glUploadBufferImageResource,\n      video: glUploadVideoResource,\n      compressed: glUploadCompressedTextureResource\n    };\n    this._premultiplyAlpha = false;\n    this._useSeparateSamplers = false;\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_glTextures\");\n    this._renderer.renderableGC.addManagedHash(this, \"_glSamplers\");\n  }\n  contextChange(gl) {\n    this._gl = gl;\n    if (!this._mapFormatToInternalFormat) {\n      this._mapFormatToInternalFormat = mapFormatToGlInternalFormat(gl, this._renderer.context.extensions);\n      this._mapFormatToType = mapFormatToGlType(gl);\n      this._mapFormatToFormat = mapFormatToGlFormat(gl);\n    }\n    this._glTextures = /* @__PURE__ */ Object.create(null);\n    this._glSamplers = /* @__PURE__ */ Object.create(null);\n    this._boundSamplers = /* @__PURE__ */ Object.create(null);\n    this._premultiplyAlpha = false;\n    for (let i = 0; i < 16; i++) {\n      this.bind(Texture.EMPTY, i);\n    }\n  }\n  initSource(source) {\n    this.bind(source);\n  }\n  bind(texture, location = 0) {\n    const source = texture.source;\n    if (texture) {\n      this.bindSource(source, location);\n      if (this._useSeparateSamplers) {\n        this._bindSampler(source.style, location);\n      }\n    } else {\n      this.bindSource(null, location);\n      if (this._useSeparateSamplers) {\n        this._bindSampler(null, location);\n      }\n    }\n  }\n  bindSource(source, location = 0) {\n    const gl = this._gl;\n    source._touched = this._renderer.textureGC.count;\n    if (this._boundTextures[location] !== source) {\n      this._boundTextures[location] = source;\n      this._activateLocation(location);\n      source || (source = Texture.EMPTY.source);\n      const glTexture = this.getGlSource(source);\n      gl.bindTexture(glTexture.target, glTexture.texture);\n    }\n  }\n  _bindSampler(style, location = 0) {\n    const gl = this._gl;\n    if (!style) {\n      this._boundSamplers[location] = null;\n      gl.bindSampler(location, null);\n      return;\n    }\n    const sampler = this._getGlSampler(style);\n    if (this._boundSamplers[location] !== sampler) {\n      this._boundSamplers[location] = sampler;\n      gl.bindSampler(location, sampler);\n    }\n  }\n  unbind(texture) {\n    const source = texture.source;\n    const boundTextures = this._boundTextures;\n    const gl = this._gl;\n    for (let i = 0; i < boundTextures.length; i++) {\n      if (boundTextures[i] === source) {\n        this._activateLocation(i);\n        const glTexture = this.getGlSource(source);\n        gl.bindTexture(glTexture.target, null);\n        boundTextures[i] = null;\n      }\n    }\n  }\n  _activateLocation(location) {\n    if (this._activeTextureLocation !== location) {\n      this._activeTextureLocation = location;\n      this._gl.activeTexture(this._gl.TEXTURE0 + location);\n    }\n  }\n  _initSource(source) {\n    const gl = this._gl;\n    const glTexture = new GlTexture(gl.createTexture());\n    glTexture.type = this._mapFormatToType[source.format];\n    glTexture.internalFormat = this._mapFormatToInternalFormat[source.format];\n    glTexture.format = this._mapFormatToFormat[source.format];\n    if (source.autoGenerateMipmaps && (this._renderer.context.supports.nonPowOf2mipmaps || source.isPowerOfTwo)) {\n      const biggestDimension = Math.max(source.width, source.height);\n      source.mipLevelCount = Math.floor(Math.log2(biggestDimension)) + 1;\n    }\n    this._glTextures[source.uid] = glTexture;\n    if (!this.managedTextures.includes(source)) {\n      source.on(\"update\", this.onSourceUpdate, this);\n      source.on(\"resize\", this.onSourceUpdate, this);\n      source.on(\"styleChange\", this.onStyleChange, this);\n      source.on(\"destroy\", this.onSourceDestroy, this);\n      source.on(\"unload\", this.onSourceUnload, this);\n      source.on(\"updateMipmaps\", this.onUpdateMipmaps, this);\n      this.managedTextures.push(source);\n    }\n    this.onSourceUpdate(source);\n    this.updateStyle(source, false);\n    return glTexture;\n  }\n  onStyleChange(source) {\n    this.updateStyle(source, false);\n  }\n  updateStyle(source, firstCreation) {\n    const gl = this._gl;\n    const glTexture = this.getGlSource(source);\n    gl.bindTexture(gl.TEXTURE_2D, glTexture.texture);\n    this._boundTextures[this._activeTextureLocation] = source;\n    applyStyleParams(\n      source.style,\n      gl,\n      source.mipLevelCount > 1,\n      this._renderer.context.extensions.anisotropicFiltering,\n      \"texParameteri\",\n      gl.TEXTURE_2D,\n      // will force a clamp to edge if the texture is not a power of two\n      !this._renderer.context.supports.nonPowOf2wrapping && !source.isPowerOfTwo,\n      firstCreation\n    );\n  }\n  onSourceUnload(source) {\n    const glTexture = this._glTextures[source.uid];\n    if (!glTexture)\n      return;\n    this.unbind(source);\n    this._glTextures[source.uid] = null;\n    this._gl.deleteTexture(glTexture.texture);\n  }\n  onSourceUpdate(source) {\n    const gl = this._gl;\n    const glTexture = this.getGlSource(source);\n    gl.bindTexture(gl.TEXTURE_2D, glTexture.texture);\n    this._boundTextures[this._activeTextureLocation] = source;\n    const premultipliedAlpha = source.alphaMode === \"premultiply-alpha-on-upload\";\n    if (this._premultiplyAlpha !== premultipliedAlpha) {\n      this._premultiplyAlpha = premultipliedAlpha;\n      gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, premultipliedAlpha);\n    }\n    if (this._uploads[source.uploadMethodId]) {\n      this._uploads[source.uploadMethodId].upload(source, glTexture, gl, this._renderer.context.webGLVersion);\n    } else {\n      gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, source.pixelWidth, source.pixelHeight, 0, gl.RGBA, gl.UNSIGNED_BYTE, null);\n    }\n    if (source.autoGenerateMipmaps && source.mipLevelCount > 1) {\n      this.onUpdateMipmaps(source, false);\n    }\n  }\n  onUpdateMipmaps(source, bind = true) {\n    if (bind)\n      this.bindSource(source, 0);\n    const glTexture = this.getGlSource(source);\n    this._gl.generateMipmap(glTexture.target);\n  }\n  onSourceDestroy(source) {\n    source.off(\"destroy\", this.onSourceDestroy, this);\n    source.off(\"update\", this.onSourceUpdate, this);\n    source.off(\"resize\", this.onSourceUpdate, this);\n    source.off(\"unload\", this.onSourceUnload, this);\n    source.off(\"styleChange\", this.onStyleChange, this);\n    source.off(\"updateMipmaps\", this.onUpdateMipmaps, this);\n    this.managedTextures.splice(this.managedTextures.indexOf(source), 1);\n    this.onSourceUnload(source);\n  }\n  _initSampler(style) {\n    const gl = this._gl;\n    const glSampler = this._gl.createSampler();\n    this._glSamplers[style._resourceId] = glSampler;\n    applyStyleParams(\n      style,\n      gl,\n      this._boundTextures[this._activeTextureLocation].mipLevelCount > 1,\n      this._renderer.context.extensions.anisotropicFiltering,\n      \"samplerParameteri\",\n      glSampler,\n      false,\n      true\n    );\n    return this._glSamplers[style._resourceId];\n  }\n  _getGlSampler(sampler) {\n    return this._glSamplers[sampler._resourceId] || this._initSampler(sampler);\n  }\n  getGlSource(source) {\n    return this._glTextures[source.uid] || this._initSource(source);\n  }\n  generateCanvas(texture) {\n    const { pixels, width, height } = this.getPixels(texture);\n    const canvas = DOMAdapter.get().createCanvas();\n    canvas.width = width;\n    canvas.height = height;\n    const ctx = canvas.getContext(\"2d\");\n    if (ctx) {\n      const imageData = ctx.createImageData(width, height);\n      imageData.data.set(pixels);\n      ctx.putImageData(imageData, 0, 0);\n    }\n    return canvas;\n  }\n  getPixels(texture) {\n    const resolution = texture.source.resolution;\n    const frame = texture.frame;\n    const width = Math.max(Math.round(frame.width * resolution), 1);\n    const height = Math.max(Math.round(frame.height * resolution), 1);\n    const pixels = new Uint8Array(BYTES_PER_PIXEL * width * height);\n    const renderer = this._renderer;\n    const renderTarget = renderer.renderTarget.getRenderTarget(texture);\n    const glRenterTarget = renderer.renderTarget.getGpuRenderTarget(renderTarget);\n    const gl = renderer.gl;\n    gl.bindFramebuffer(gl.FRAMEBUFFER, glRenterTarget.resolveTargetFramebuffer);\n    gl.readPixels(\n      Math.round(frame.x * resolution),\n      Math.round(frame.y * resolution),\n      width,\n      height,\n      gl.RGBA,\n      gl.UNSIGNED_BYTE,\n      pixels\n    );\n    return { pixels: new Uint8ClampedArray(pixels.buffer), width, height };\n  }\n  destroy() {\n    this.managedTextures.slice().forEach((source) => this.onSourceDestroy(source));\n    this.managedTextures = null;\n    this._renderer = null;\n  }\n  resetState() {\n    this._activeTextureLocation = -1;\n    this._boundTextures.fill(Texture.EMPTY.source);\n    this._boundSamplers = /* @__PURE__ */ Object.create(null);\n  }\n}\nGlTextureSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem\n  ],\n  name: \"texture\"\n};\nclass GlGraphicsAdaptor {\n  init() {\n    const uniforms = new UniformGroup({\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    const maxTextures = getMaxTexturesPerBatch();\n    const glProgram = compileHighShaderGlProgram({\n      name: \"graphics\",\n      bits: [\n        colorBitGl,\n        generateTextureBatchBitGl(maxTextures),\n        localUniformBitGl,\n        roundPixelsBitGl\n      ]\n    });\n    this.shader = new Shader({\n      glProgram,\n      resources: {\n        localUniforms: uniforms,\n        batchSamplers: getBatchSamplersUniformGroup(maxTextures)\n      }\n    });\n  }\n  execute(graphicsPipe, renderable) {\n    const context = renderable.context;\n    const shader = context.customShader || this.shader;\n    const renderer = graphicsPipe.renderer;\n    const contextSystem = renderer.graphicsContext;\n    const {\n      batcher,\n      instructions\n    } = contextSystem.getContextRenderData(context);\n    shader.groups[0] = renderer.globalUniforms.bindGroup;\n    renderer.state.set(graphicsPipe.state);\n    renderer.shader.bind(shader);\n    renderer.geometry.bind(batcher.geometry, shader.glProgram);\n    const batches = instructions.instructions;\n    for (let i = 0; i < instructions.instructionSize; i++) {\n      const batch = batches[i];\n      if (batch.size) {\n        for (let j = 0; j < batch.textures.count; j++) {\n          renderer.texture.bind(batch.textures.textures[j], j);\n        }\n        renderer.geometry.draw(batch.topology, batch.size, batch.start);\n      }\n    }\n  }\n  destroy() {\n    this.shader.destroy(true);\n    this.shader = null;\n  }\n}\nGlGraphicsAdaptor.extension = {\n  type: [\n    ExtensionType.WebGLPipesAdaptor\n  ],\n  name: \"graphics\"\n};\nclass GlMeshAdaptor {\n  init() {\n    const glProgram = compileHighShaderGlProgram({\n      name: \"mesh\",\n      bits: [\n        localUniformBitGl,\n        textureBitGl,\n        roundPixelsBitGl\n      ]\n    });\n    this._shader = new Shader({\n      glProgram,\n      resources: {\n        uTexture: Texture.EMPTY.source,\n        textureUniforms: {\n          uTextureMatrix: { type: \"mat3x3<f32>\", value: new Matrix() }\n        }\n      }\n    });\n  }\n  execute(meshPipe, mesh) {\n    const renderer = meshPipe.renderer;\n    let shader = mesh._shader;\n    if (!shader) {\n      shader = this._shader;\n      const texture = mesh.texture;\n      const source = texture.source;\n      shader.resources.uTexture = source;\n      shader.resources.uSampler = source.style;\n      shader.resources.textureUniforms.uniforms.uTextureMatrix = texture.textureMatrix.mapCoord;\n    } else if (!shader.glProgram) {\n      warn(\"Mesh shader has no glProgram\", mesh.shader);\n      return;\n    }\n    shader.groups[100] = renderer.globalUniforms.bindGroup;\n    shader.groups[101] = meshPipe.localUniformsBindGroup;\n    renderer.encoder.draw({\n      geometry: mesh._geometry,\n      shader,\n      state: mesh.state\n    });\n  }\n  destroy() {\n    this._shader.destroy(true);\n    this._shader = null;\n  }\n}\nGlMeshAdaptor.extension = {\n  type: [\n    ExtensionType.WebGLPipesAdaptor\n  ],\n  name: \"mesh\"\n};\nconst DefaultWebGLSystems = [\n  ...SharedSystems,\n  GlUboSystem,\n  GlBackBufferSystem,\n  GlContextSystem,\n  GlBufferSystem,\n  GlTextureSystem,\n  GlRenderTargetSystem,\n  GlGeometrySystem,\n  GlUniformGroupSystem,\n  GlShaderSystem,\n  GlEncoderSystem,\n  GlStateSystem,\n  GlStencilSystem,\n  GlColorMaskSystem\n];\nconst DefaultWebGLPipes = [...SharedRenderPipes];\nconst DefaultWebGLAdapters = [GlBatchAdaptor, GlMeshAdaptor, GlGraphicsAdaptor];\nconst systems = [];\nconst renderPipes = [];\nconst renderPipeAdaptors = [];\nextensions.handleByNamedList(ExtensionType.WebGLSystem, systems);\nextensions.handleByNamedList(ExtensionType.WebGLPipes, renderPipes);\nextensions.handleByNamedList(ExtensionType.WebGLPipesAdaptor, renderPipeAdaptors);\nextensions.add(...DefaultWebGLSystems, ...DefaultWebGLPipes, ...DefaultWebGLAdapters);\nclass WebGLRenderer extends AbstractRenderer {\n  constructor() {\n    const systemConfig = {\n      name: \"webgl\",\n      type: RendererType.WEBGL,\n      systems,\n      renderPipes,\n      renderPipeAdaptors\n    };\n    super(systemConfig);\n  }\n}\nexport {\n  WebGLRenderer\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACpC,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,GAAG;AACH,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE;AACrC,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtD,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC7C,KAAK;AACL,IAAI,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAC7E,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;AACvD,GAAG;AACH,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE;AAC5B,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAChD,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC7C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACpE,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,iBAAiB;AACnC,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,CAAC,CAAC;AACF,IAAI,WAAW,mBAAmB,CAAC,CAAC,YAAY,KAAK;AACrD,EAAE,YAAY,CAAC,YAAY,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC;AACtF,EAAE,YAAY,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC;AACtE,EAAE,YAAY,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC;AAC1E,EAAE,OAAO,YAAY,CAAC;AACtB,CAAC,EAAE,WAAW,IAAI,EAAE,CAAC,CAAC;AACtB,MAAM,QAAQ,CAAC;AACf,EAAE,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC;AACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD,MAAM,cAAc,CAAC;AACrB;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,iBAAiB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjE,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACpD,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AACpE,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,GAAG;AACH;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AAC5C,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,2BAA2B,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC;AAC7G,GAAG;AACH,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACvE,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AAC7B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC9C,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAClD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE;AAClC,IAAI,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;AACpD,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AAC/C,MAAM,QAAQ,CAAC,qBAAqB,GAAG,KAAK,CAAC;AAC7C,MAAM,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnE,KAAK;AACL,GAAG;AACH,EAAE,YAAY,CAAC,oBAAoB,EAAE;AACrC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC9B,IAAI,IAAI,oBAAoB,EAAE;AAC9B,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACvC,MAAM,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAChC,MAAM,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;AACvC,QAAQ,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;AACpC,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,yBAAyB,CAAC,QAAQ,EAAE;AACtC,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AAC3D,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC5C,MAAM,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC;AAClD,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AACjB,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC5C,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE;AACrB,MAAM,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;AAC1C,QAAQ,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC1C,QAAQ,IAAI,EAAE,CAAC;AACf,OAAO;AACP,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;AACvD,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,eAAe,KAAK,IAAI,CAAC,WAAW,EAAE;AACjE,QAAQ,SAAS,EAAE,CAAC;AACpB,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,MAAM;AACZ,KAAK;AACL,IAAI,SAAS,GAAG,SAAS,CAAC;AAC1B,IAAI,IAAI,CAAC,kBAAkB,GAAG,SAAS,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE;AACnB,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,KAAK;AACL,IAAI,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC;AAChD,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AAC7C,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,uBAAuB,CAAC,QAAQ,EAAE;AACpC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,qBAAqB,CAAC;AACjD,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;AACpD,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;AACjD,IAAI,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AAC7B,IAAI,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3B,IAAI,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACzC,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AAClG,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,MAAM,EAAE;AACvB,IAAI,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AAC7B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC9C,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,CAAC,QAAQ,EAAE;AAChD,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,IAAI,QAAQ,CAAC,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AACzC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAClD,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAC7B,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC;AACrG,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE;AAClD,QAAQ,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACjG,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC9C,QAAQ,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACrD,OAAO;AACP,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;AACnD,MAAM,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAClE,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH;AACA,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;AACvC,MAAM,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE;AACvC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAClD,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC;AACxC,IAAI,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE;AACrD,MAAM,IAAI,GAAG,WAAW,CAAC,oBAAoB,CAAC;AAC9C,KAAK,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,WAAW,CAAC,OAAO,EAAE;AAC9D,MAAM,IAAI,GAAG,WAAW,CAAC,cAAc,CAAC;AACxC,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AAC5C,IAAI,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACrD,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,iBAAiB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjE,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,MAAM,iBAAiB,CAAC;AACjD;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA,MAAM,aAAa,EAAE,IAAI;AACzB;AACA,MAAM,mBAAmB,EAAE,IAAI;AAC/B;AACA,MAAM,iBAAiB,EAAE,IAAI;AAC7B;AACA,MAAM,YAAY,EAAE,IAAI;AACxB;AACA,MAAM,iBAAiB,EAAE,IAAI;AAC7B;AACA,MAAM,IAAI,EAAE,IAAI;AAChB;AACA,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,UAAU,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvE,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AAC/C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,GAAG,EAAE,GAAG,iBAAiB,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AAClE,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AACvD,IAAI,IAAI,OAAO,CAAC,OAAO,IAAI,SAAS,EAAE;AACtC,MAAM,IAAI,CAAC,+GAA+G,CAAC,CAAC;AAC5H,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7G,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE;AACzB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC5C,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;AACxD,MAAM,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,IAAI,CAAC;AACpE,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC;AACtF,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE;AACrD,QAAQ,KAAK;AACb,QAAQ,kBAAkB;AAC1B,QAAQ,SAAS;AACjB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;AAC5D,QAAQ,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,SAAS;AAC7D,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,gBAAgB,CAAC,YAAY,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACzB,MAAM,IAAI,YAAY,KAAK,IAAI,CAAC,MAAM,EAAE;AACxC,QAAQ,IAAI,CAAC,gEAAgE,CAAC,CAAC;AAC/E,OAAO;AACP,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE;AAClF,MAAM,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;AACtE,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AACzE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,EAAE,EAAE;AACtB,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,YAAY,UAAU,CAAC,GAAG,EAAE,CAAC,wBAAwB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1F,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,IAAI,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAChF,IAAI,OAAO,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;AACxF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,kBAAkB,EAAE,OAAO,EAAE;AAC7C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,kBAAkB,KAAK,CAAC,EAAE;AAClC,MAAM,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC/C,MAAM,IAAI,CAAC,EAAE,EAAE;AACf,QAAQ,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;AAC9F,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,GAAG;AACH;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACxB,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,oBAAoB,EAAE,EAAE,CAAC,YAAY,CAAC,gCAAgC,CAAC;AAC7E,MAAM,kBAAkB,EAAE,EAAE,CAAC,YAAY,CAAC,0BAA0B,CAAC;AACrE,MAAM,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,+BAA+B,CAAC;AAC5D,MAAM,SAAS,EAAE,EAAE,CAAC,YAAY,CAAC,oCAAoC,CAAC;AACtE;AACA,MAAM,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,8BAA8B,CAAC;AAC1D,MAAM,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,+BAA+B,CAAC;AAC5D,MAAM,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,gCAAgC,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,uCAAuC,CAAC;AAC1H,MAAM,GAAG,EAAE,EAAE,CAAC,YAAY,CAAC,8BAA8B,CAAC;AAC1D,MAAM,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,+BAA+B,CAAC;AAC5D,MAAM,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,8BAA8B,CAAC;AAC3D,MAAM,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,8BAA8B,CAAC;AAC3D,MAAM,WAAW,EAAE,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC;AACxD,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;AACjC,MAAM,IAAI,CAAC,UAAU,GAAG;AACxB,QAAQ,GAAG,MAAM;AACjB,QAAQ,WAAW,EAAE,EAAE,CAAC,YAAY,CAAC,oBAAoB,CAAC;AAC1D,QAAQ,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,qBAAqB,CAAC;AAC5D,QAAQ,iBAAiB,EAAE,EAAE,CAAC,YAAY,CAAC,yBAAyB,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,6BAA6B,CAAC,IAAI,EAAE,CAAC,YAAY,CAAC,gCAAgC,CAAC;AAC5K,QAAQ,kBAAkB,EAAE,EAAE,CAAC,YAAY,CAAC,wBAAwB,CAAC;AACrE;AACA,QAAQ,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,mBAAmB,CAAC;AAC1D,QAAQ,kBAAkB,EAAE,EAAE,CAAC,YAAY,CAAC,0BAA0B,CAAC;AACvE,QAAQ,gBAAgB,EAAE,EAAE,CAAC,YAAY,CAAC,wBAAwB,CAAC;AACnE,QAAQ,sBAAsB,EAAE,EAAE,CAAC,YAAY,CAAC,+BAA+B,CAAC;AAChF,QAAQ,wBAAwB,EAAE,EAAE,CAAC,YAAY,CAAC,wBAAwB,CAAC;AAC3E,QAAQ,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC;AACzC,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,UAAU,GAAG;AACxB,QAAQ,GAAG,MAAM;AACjB,QAAQ,gBAAgB,EAAE,EAAE,CAAC,YAAY,CAAC,wBAAwB,CAAC;AACnE,OAAO,CAAC;AACR,MAAM,MAAM,UAAU,GAAG,EAAE,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;AACnE,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,UAAU,CAAC,oBAAoB,CAAC,UAAU,CAAC,6BAA6B,CAAC,CAAC;AAClF,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,KAAK,EAAE;AAC3B,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACjC,MAAM,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACtC,MAAM,UAAU,CAAC,MAAM;AACvB,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,EAAE;AACrC,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;AACxD,SAAS;AACT,OAAO,EAAE,CAAC,CAAC,CAAC;AACZ,KAAK;AACL,GAAG;AACH;AACA,EAAE,qBAAqB,GAAG;AAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC5E,IAAI,OAAO,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACpF,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC;AAC/C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC;AAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,EAAE,EAAE;AACtB,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC,oBAAoB,EAAE,CAAC;AACjD,IAAI,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AAC3C,MAAM,IAAI,CAAC,uFAAuF,CAAC,CAAC;AACpG,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC;AAC7C,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;AACxC,IAAI,QAAQ,CAAC,aAAa,GAAG,QAAQ,IAAI,CAAC,CAAC,WAAW,CAAC,kBAAkB,CAAC;AAC1E,IAAI,QAAQ,CAAC,mBAAmB,GAAG,QAAQ,CAAC;AAC5C,IAAI,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,IAAI,CAAC,CAAC,WAAW,CAAC,iBAAiB,CAAC;AAC7E,IAAI,QAAQ,CAAC,YAAY,GAAG,QAAQ,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC;AAC3D,IAAI,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAC;AAC1C,IAAI,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AACzC,IAAI,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;AACjC,MAAM,IAAI,CAAC,gGAAgG,CAAC,CAAC;AAC7G,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS;AACjB,CAAC,CAAC;AACF,gBAAgB,CAAC,cAAc,GAAG;AAClC;AACA;AACA;AACA;AACA,EAAE,OAAO,EAAE,IAAI;AACf;AACA;AACA;AACA;AACA,EAAE,kBAAkB,EAAE,IAAI;AAC1B;AACA;AACA;AACA;AACA,EAAE,qBAAqB,EAAE,KAAK;AAC9B;AACA;AACA;AACA;AACA,EAAE,eAAe,EAAE,KAAK,CAAC;AACzB;AACA;AACA;AACA;AACA,EAAE,kBAAkB,EAAE,CAAC;AACvB;AACA;AACA;AACA;AACA,EAAE,SAAS,EAAE,KAAK;AAClB,CAAC,CAAC;AACF,IAAI,eAAe,GAAG,gBAAgB,CAAC;AACvC,IAAI,UAAU,mBAAmB,CAAC,CAAC,WAAW,KAAK;AACnD,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC;AACnD,EAAE,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;AACjD,EAAE,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC;AAChD,EAAE,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;AACjD,EAAE,WAAW,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC;AACpE,EAAE,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC;AAClE,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC;AAChE,EAAE,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC;AAClE,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;AACrD,EAAE,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,GAAG,WAAW,CAAC;AAC7D,EAAE,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,GAAG,iBAAiB,CAAC;AACzE,EAAE,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,GAAG,iBAAiB,CAAC;AACzE,EAAE,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC;AACtE,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;AACrB,IAAI,UAAU,mBAAmB,CAAC,CAAC,WAAW,KAAK;AACnD,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,YAAY,CAAC;AAC/D,EAAE,WAAW,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC,GAAG,kBAAkB,CAAC;AAC5E,EAAE,WAAW,CAAC,WAAW,CAAC,kBAAkB,CAAC,GAAG,KAAK,CAAC,GAAG,kBAAkB,CAAC;AAC5E,EAAE,WAAW,CAAC,WAAW,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,GAAG,6BAA6B,CAAC;AAClG,EAAE,WAAW,CAAC,WAAW,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,GAAG,6BAA6B,CAAC;AAClG,EAAE,WAAW,CAAC,WAAW,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,GAAG,6BAA6B,CAAC;AAClG,EAAE,WAAW,CAAC,WAAW,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,GAAG,6BAA6B,CAAC;AAClG,EAAE,WAAW,CAAC,WAAW,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,GAAG,6BAA6B,CAAC;AAClG,EAAE,WAAW,CAAC,WAAW,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,GAAG,6BAA6B,CAAC;AAClG,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;AACrB,IAAI,QAAQ,mBAAmB,CAAC,CAAC,SAAS,KAAK;AAC/C,EAAE,SAAS,CAAC,SAAS,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,GAAG,eAAe,CAAC;AACjE,EAAE,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,GAAG,gBAAgB,CAAC;AACnE,EAAE,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,GAAG,sBAAsB,CAAC;AAChF,EAAE,SAAS,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,KAAK,CAAC,GAAG,wBAAwB,CAAC;AACpF,EAAE,SAAS,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,KAAK,CAAC,GAAG,wBAAwB,CAAC;AACpF,EAAE,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,GAAG,cAAc,CAAC;AAC/D,EAAE,SAAS,CAAC,SAAS,CAAC,8BAA8B,CAAC,GAAG,KAAK,CAAC,GAAG,8BAA8B,CAAC;AAChG,EAAE,SAAS,CAAC,SAAS,CAAC,6BAA6B,CAAC,GAAG,KAAK,CAAC,GAAG,6BAA6B,CAAC;AAC9F,EAAE,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,KAAK,CAAC,GAAG,mBAAmB,CAAC;AAC1E,EAAE,SAAS,CAAC,SAAS,CAAC,0BAA0B,CAAC,GAAG,KAAK,CAAC,GAAG,0BAA0B,CAAC;AACxF,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC;AAC/C,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;AACjD,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;AAC7C,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;AACjD,EAAE,SAAS,CAAC,SAAS,CAAC,gCAAgC,CAAC,GAAG,KAAK,CAAC,GAAG,gCAAgC,CAAC;AACpG,EAAE,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC;AAC5D,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;AACnB,MAAM,OAAO,GAAG;AAChB,EAAE,OAAO,EAAE,QAAQ,CAAC,aAAa;AACjC,EAAE,OAAO,EAAE,QAAQ,CAAC,aAAa;AACjC,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI;AACxB,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI;AACxB,EAAE,QAAQ,EAAE,QAAQ,CAAC,aAAa;AAClC,EAAE,QAAQ,EAAE,QAAQ,CAAC,aAAa;AAClC,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI;AACzB,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI;AACzB,EAAE,QAAQ,EAAE,QAAQ,CAAC,cAAc;AACnC,EAAE,QAAQ,EAAE,QAAQ,CAAC,cAAc;AACnC,EAAE,QAAQ,EAAE,QAAQ,CAAC,KAAK;AAC1B,EAAE,QAAQ,EAAE,QAAQ,CAAC,KAAK;AAC1B,EAAE,SAAS,EAAE,QAAQ,CAAC,cAAc;AACpC,EAAE,SAAS,EAAE,QAAQ,CAAC,cAAc;AACpC,EAAE,SAAS,EAAE,QAAQ,CAAC,KAAK;AAC3B,EAAE,SAAS,EAAE,QAAQ,CAAC,KAAK;AAC3B,EAAE,SAAS,EAAE,QAAQ,CAAC,UAAU;AAChC,EAAE,SAAS,EAAE,QAAQ,CAAC,UAAU;AAChC,EAAE,OAAO,EAAE,QAAQ,CAAC,KAAK;AACzB,EAAE,SAAS,EAAE,QAAQ,CAAC,KAAK;AAC3B,EAAE,SAAS,EAAE,QAAQ,CAAC,KAAK;AAC3B,EAAE,SAAS,EAAE,QAAQ,CAAC,KAAK;AAC3B,EAAE,MAAM,EAAE,QAAQ,CAAC,YAAY;AAC/B,EAAE,QAAQ,EAAE,QAAQ,CAAC,YAAY;AACjC,EAAE,QAAQ,EAAE,QAAQ,CAAC,YAAY;AACjC,EAAE,QAAQ,EAAE,QAAQ,CAAC,YAAY;AACjC,EAAE,MAAM,EAAE,QAAQ,CAAC,GAAG;AACtB,EAAE,QAAQ,EAAE,QAAQ,CAAC,GAAG;AACxB,EAAE,QAAQ,EAAE,QAAQ,CAAC,GAAG;AACxB,EAAE,QAAQ,EAAE,QAAQ,CAAC,GAAG;AACxB,CAAC,CAAC;AACF,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACrC,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC;AAC5C,CAAC;AACD,MAAM,eAAe,GAAG;AACxB,EAAE,YAAY,EAAE,CAAC;AACjB,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,YAAY,EAAE,CAAC;AACjB,EAAE,eAAe,EAAE,CAAC;AACpB,EAAE,gBAAgB,EAAE,CAAC;AACrB,CAAC,CAAC;AACF,MAAM,gBAAgB,CAAC;AACvB;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,gBAAgB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AACzE,GAAG;AACH;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE;AAC5D,MAAM,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;AACxF,KAAK;AACL,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC;AACnF,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,EAAE,CAAC,iBAAiB,GAAG,MAAM,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;AAC7E,MAAM,EAAE,CAAC,eAAe,GAAG,CAAC,GAAG,KAAK,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC/E,MAAM,EAAE,CAAC,iBAAiB,GAAG,CAAC,GAAG,KAAK,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;AACnF,KAAK;AACL,IAAI,MAAM,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,wBAAwB,CAAC;AAChG,IAAI,IAAI,wBAAwB,EAAE;AAClC,MAAM,EAAE,CAAC,mBAAmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC/C,QAAQ,wBAAwB,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE,OAAO,CAAC;AACR,MAAM,EAAE,CAAC,qBAAqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AACpD,QAAQ,wBAAwB,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,OAAO,CAAC;AACR,MAAM,EAAE,CAAC,mBAAmB,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,wBAAwB,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjG,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,gBAAgB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE;AAC1B,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;AACpC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC/C,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,GAAG,EAAE;AACjC,MAAM,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AAC5B,MAAM,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AACzB,GAAG;AACH;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;AAClB,GAAG;AACH;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1C,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAC/C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtD,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACzC,MAAM,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACxC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE;AACxC,IAAI,MAAM,kBAAkB,GAAG,QAAQ,CAAC,UAAU,CAAC;AACnD,IAAI,MAAM,gBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC;AACpD,IAAI,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE;AACtC,MAAM,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,wDAAwD,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AACnG,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;AAClC,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC;AACxC,IAAI,MAAM,gBAAgB,GAAG,OAAO,CAAC,cAAc,CAAC;AACpD,IAAI,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;AACxC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;AAC7B,MAAM,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE;AAC/B,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AACtD,OAAO;AACP,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1G,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI,EAAE;AAC1D,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC/C,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC9C,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChF,MAAM,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9D,IAAI,IAAI,GAAG,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;AACvC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACxC,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK;AACL,IAAI,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;AACvD,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrC,IAAI,GAAG,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;AACjC,IAAI,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC5B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACxC,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACtC,IAAI,aAAa,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;AACnC,IAAI,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC7B,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,QAAQ,EAAE,WAAW,EAAE;AAC3C,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9D,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACvB,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,KAAK,MAAM,CAAC,IAAI,aAAa,EAAE;AACvC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,aAAa,CAAC,CAAC,CAAC,EAAE;AACpD,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,WAAW;AACX,UAAU,EAAE,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACjD,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,WAAW,GAAG,KAAK,EAAE;AAClC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACvB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC3C,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;AAClD,UAAU,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AACzD,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,aAAa,EAAE;AACjD,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,WAAW;AACX,UAAU,EAAE,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACtC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AACjC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAC/C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AAC3C,IAAI,IAAI,QAAQ,CAAC,WAAW,EAAE;AAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;AAC1B,IAAI,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;AAChC,MAAM,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AACtC,MAAM,MAAM,QAAQ,GAAG,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACxD,MAAM,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACtD,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,IAAI,UAAU,KAAK,QAAQ,EAAE;AACrC,UAAU,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,UAAU,UAAU,GAAG,QAAQ,CAAC;AAChC,SAAS;AACT,QAAQ,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AAChD,QAAQ,EAAE,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AAC7C,QAAQ,MAAM,aAAa,GAAG,0BAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3E,QAAQ,MAAM,IAAI,GAAG,mBAAmB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC3D,QAAQ,IAAI,aAAa,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;AAC7D,UAAU,EAAE,CAAC,oBAAoB;AACjC,YAAY,QAAQ;AACpB,YAAY,aAAa,CAAC,IAAI;AAC9B,YAAY,IAAI;AAChB,YAAY,SAAS,CAAC,MAAM;AAC5B,YAAY,SAAS,CAAC,MAAM;AAC5B,WAAW,CAAC;AACZ,SAAS,MAAM;AACf,UAAU,EAAE,CAAC,mBAAmB;AAChC,YAAY,QAAQ;AACpB,YAAY,aAAa,CAAC,IAAI;AAC9B,YAAY,IAAI;AAChB,YAAY,aAAa,CAAC,UAAU;AACpC,YAAY,SAAS,CAAC,MAAM;AAC5B,YAAY,SAAS,CAAC,MAAM;AAC5B,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,IAAI,SAAS,CAAC,QAAQ,EAAE;AAChC,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE;AAChC,YAAY,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,IAAI,CAAC,CAAC;AACnD,YAAY,EAAE,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACtD,WAAW,MAAM;AACjB,YAAY,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;AAC9F,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE;AAC7C,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1C,IAAI,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACtE,IAAI,aAAa,KAAK,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9D,IAAI,IAAI,QAAQ,CAAC,WAAW,EAAE;AAC9B,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;AACnE,MAAM,MAAM,MAAM,GAAG,QAAQ,KAAK,CAAC,GAAG,EAAE,CAAC,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC;AAC1E,MAAM,IAAI,aAAa,GAAG,CAAC,EAAE;AAC7B,QAAQ,EAAE,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,QAAQ,EAAE,aAAa,CAAC,CAAC;AACvI,OAAO,MAAM;AACb,QAAQ,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC;AAC/G,OAAO;AACP,KAAK,MAAM,IAAI,aAAa,GAAG,CAAC,EAAE;AAClC,MAAM,EAAE,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE,aAAa,CAAC,CAAC;AAChG,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;AACxE,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;AACnB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH,CAAC;AACD,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG,IAAI,QAAQ,CAAC;AACzC,EAAE,UAAU,EAAE;AACd,IAAI,SAAS,EAAE;AACf,MAAM,CAAC,CAAC;AACR,MAAM,CAAC,CAAC;AACR;AACA,MAAM,CAAC;AACP,MAAM,CAAC,CAAC;AACR;AACA,MAAM,CAAC,CAAC;AACR,MAAM,CAAC;AACP;AACA,KAAK;AACL,GAAG;AACH,CAAC,CAAC,CAAC;AACH,MAAM,mBAAmB,GAAG,MAAM,oBAAoB,CAAC;AACvD,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,IAAI,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE,EAAE;AACrB,IAAI,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,oBAAoB,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AAChG,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC,IAAI,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;AAC/C,MAAM,IAAI,CAAC,8DAA8D,CAAC,CAAC;AAC3E,MAAM,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAChC,IAAI,MAAM,kBAAkB,GAAG,IAAI,SAAS,CAAC;AAC7C,MAAM,MAAM,EAAE,CAAC;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC;AAClB,MAAM,QAAQ,EAAE,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC;AAClB,MAAM,IAAI,EAAE,cAAc;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC;AACzC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE;AACjB,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM;AACtC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACrF,IAAI,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;AAChF,IAAI,IAAI,IAAI,CAAC,wBAAwB,EAAE;AACvC,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACxF,MAAM,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,YAAY,CAAC;AACvD,MAAM,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AAC9E,KAAK;AACL,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,kBAAkB,GAAG;AACvB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;AAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,wBAAwB;AACtC,MAAM,OAAO;AACb,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;AAChF,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;AAC1B,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,MAAM,EAAE,IAAI,CAAC,kBAAkB;AACrC,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM;AACxB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,qBAAqB,CAAC,mBAAmB,EAAE;AAC7C,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,IAAI,OAAO,CAAC;AACrE,MAAM,MAAM,EAAE,IAAI,aAAa,CAAC;AAChC,QAAQ,KAAK,EAAE,mBAAmB,CAAC,KAAK;AACxC,QAAQ,MAAM,EAAE,mBAAmB,CAAC,MAAM;AAC1C,QAAQ,UAAU,EAAE,mBAAmB,CAAC,WAAW;AACnD,QAAQ,SAAS,EAAE,IAAI,CAAC,UAAU;AAClC,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM;AACzC,MAAM,mBAAmB,CAAC,KAAK;AAC/B,MAAM,mBAAmB,CAAC,MAAM;AAChC,MAAM,mBAAmB,CAAC,WAAW;AACrC,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC;AACnC,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACjC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;AACxC,MAAM,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACrC,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,SAAS,GAAG;AAChC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC;AACF,mBAAmB,CAAC,cAAc,GAAG;AACrC;AACA,EAAE,aAAa,EAAE,KAAK;AACtB,CAAC,CAAC;AACF,IAAI,kBAAkB,GAAG,mBAAmB,CAAC;AAC7C,MAAM,iBAAiB,CAAC;AACxB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,CAAC,SAAS,EAAE;AACrB,IAAI,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS;AAC1C,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;AACrC,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS;AAC/B,MAAM,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;AACvB,MAAM,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;AACvB,MAAM,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;AACvB,MAAM,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;AACvB,KAAK,CAAC;AACN,GAAG;AACH,CAAC;AACD,iBAAiB,CAAC,SAAS,GAAG;AAC9B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC,CAAC;AACF,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AAC7C,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE;AAChC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AACtG,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3C,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AACrE,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,IAAI,QAAQ,CAAC,aAAa,CAAC,CAAC;AACvF,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS;AACjB,CAAC,CAAC;AACF,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AACtB,IAAI,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC/B,GAAG;AACH,CAAC;AACD,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,aAAa,GAAG;AACzB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,gBAAgB,EAAE,CAAC;AACzB,MAAM,WAAW,EAAE,aAAa,CAAC,IAAI;AACrC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,yBAAyB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzE,IAAI,QAAQ,CAAC,YAAY,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,sBAAsB,GAAG;AAClC,MAAM,MAAM,EAAE,EAAE,CAAC,MAAM;AACvB,MAAM,KAAK,EAAE,EAAE,CAAC,KAAK;AACrB,MAAM,KAAK,EAAE,EAAE,CAAC,KAAK;AACrB,MAAM,WAAW,EAAE,EAAE,CAAC,QAAQ;AAC9B,MAAM,IAAI,EAAE,EAAE,CAAC,IAAI;AACnB,MAAM,YAAY,EAAE,EAAE,CAAC,MAAM;AAC7B,MAAM,OAAO,EAAE,EAAE,CAAC,OAAO;AACzB,MAAM,eAAe,EAAE,EAAE,CAAC,MAAM;AAChC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,kBAAkB,GAAG;AAC9B,MAAM,IAAI,EAAE,EAAE,CAAC,IAAI;AACnB,MAAM,IAAI,EAAE,EAAE,CAAC,IAAI;AACnB,MAAM,OAAO,EAAE,EAAE,CAAC,OAAO;AACzB,MAAM,MAAM,EAAE,EAAE,CAAC,MAAM;AACvB,MAAM,iBAAiB,EAAE,EAAE,CAAC,IAAI;AAChC,MAAM,iBAAiB,EAAE,EAAE,CAAC,IAAI;AAChC,MAAM,gBAAgB,EAAE,EAAE,CAAC,SAAS;AACpC,MAAM,gBAAgB,EAAE,EAAE,CAAC,SAAS;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC;AACvC,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC;AACxD,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,oBAAoB,CAAC,YAAY,EAAE;AACrC,IAAI,IAAI,IAAI,CAAC,mBAAmB,KAAK,YAAY;AACjD,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;AAC5C,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACxE,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG;AACxE,QAAQ,WAAW,EAAE,aAAa,CAAC,QAAQ;AAC3C,QAAQ,gBAAgB,EAAE,CAAC;AAC3B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,EAAE,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACjF,GAAG;AACH,EAAE,cAAc,CAAC,WAAW,EAAE,gBAAgB,EAAE;AAChD,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;AACtF,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,MAAM,IAAI,GAAG,qBAAqB,CAAC,WAAW,CAAC,CAAC;AACpD,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7C,IAAI,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;AAC3C,IAAI,YAAY,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACrD,IAAI,IAAI,WAAW,KAAK,aAAa,CAAC,QAAQ,EAAE;AAChD,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;AACtC,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC;AAC3C,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;AACpC,OAAO;AACP,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;AACrC,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC;AACxC,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,WAAW,KAAK,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,gBAAgB,KAAK,gBAAgB,EAAE;AAC1G,MAAM,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;AAC9C,MAAM,aAAa,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACxD,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,gBAAgB,EAAE,GAAG,CAAC,CAAC;AACnG,MAAM,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AACvF,KAAK;AACL,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS;AACjB,CAAC,CAAC;AACF,MAAM,kBAAkB,GAAG;AAC3B,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,GAAG,EAAE,CAAC;AACR,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,WAAW,EAAE,EAAE;AACjB,EAAE,WAAW,EAAE,EAAE;AACjB,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,WAAW,EAAE,EAAE;AACjB,EAAE,WAAW,EAAE,EAAE;AACjB,EAAE,aAAa,EAAE,EAAE,GAAG,CAAC;AACvB,EAAE,aAAa,EAAE,EAAE,GAAG,CAAC;AACvB,EAAE,aAAa,EAAE,EAAE,GAAG,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AACF,SAAS,sBAAsB,CAAC,WAAW,EAAE;AAC7C,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACjD,IAAI,IAAI;AACR,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,IAAI,EAAE,CAAC;AACX,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;AACvB,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI,IAAI,GAAG,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;AAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9D,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC7C,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3B,IAAI,MAAM,SAAS,GAAG,MAAM,GAAG,SAAS,CAAC;AACzC,IAAI,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,SAAS,GAAG,QAAQ,EAAE;AAC3D,MAAM,MAAM,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,EAAE,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,CAAC,IAAI,GAAG,SAAS,GAAG,IAAI,IAAI,IAAI,CAAC;AACjD,KAAK;AACL,IAAI,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B,IAAI,MAAM,IAAI,IAAI,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AACvC,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AACvC,CAAC;AACD,SAAS,sBAAsB,CAAC,UAAU,EAAE,WAAW,EAAE;AACzD,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7E,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1E,EAAE,MAAM,SAAS,GAAG,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9C,EAAE,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC;AAC/E,EAAE,OAAO,CAAC;AACV,eAAe,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,kBAAkB,EAAE,WAAW,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,yBAAyB,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;AAC1D;AACA,+BAA+B,EAAE,WAAW,CAAC;AAC7C;AACA,gBAAgB,EAAE,IAAI,CAAC;AACvB;AACA,YAAY,EAAE,SAAS,KAAK,CAAC,GAAG,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACpE;AACA,IAAI,CAAC,CAAC;AACN,CAAC;AACD,SAAS,0BAA0B,CAAC,WAAW,EAAE;AACjD,EAAE,OAAO,qBAAqB;AAC9B,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,sBAAsB;AAC1B,IAAI,qBAAqB;AACzB,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,WAAW,SAAS,SAAS,CAAC;AACpC,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC;AACV,MAAM,iBAAiB,EAAE,sBAAsB;AAC/C,MAAM,eAAe,EAAE,0BAA0B;AACjD,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,WAAW,CAAC,SAAS,GAAG;AACxB,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK;AACb,CAAC,CAAC;AACF,MAAM,qBAAqB,CAAC;AAC5B,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAC1C,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,EAAE,kBAAkB,EAAE;AACrC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;AAClD,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7C,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAC1C,GAAG;AACH,EAAE,aAAa,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE;AAC7F,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,cAAc,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;AAC7F,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;AACtD,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AACjD,IAAI,EAAE,CAAC,iBAAiB;AACxB,MAAM,EAAE,CAAC,UAAU;AACnB,MAAM,CAAC;AACP,MAAM,UAAU,CAAC,CAAC;AAClB,MAAM,UAAU,CAAC,CAAC;AAClB,MAAM,SAAS,CAAC,CAAC;AACjB,MAAM,SAAS,CAAC,CAAC;AACjB,MAAM,IAAI,CAAC,KAAK;AAChB,MAAM,IAAI,CAAC,MAAM;AACjB,KAAK,CAAC;AACN,IAAI,OAAO,kBAAkB,CAAC;AAC9B,GAAG;AACH,EAAE,eAAe,CAAC,YAAY,EAAE,KAAK,GAAG,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE;AACpE,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC;AAC7C,IAAI,MAAM,eAAe,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAChF,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC/B,IAAI,IAAI,YAAY,CAAC,MAAM,EAAE;AAC7B,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC;AACvD,KAAK;AACL,IAAI,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACpD,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC7C,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC;AACpE,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;AAC9C,IAAI,IAAI,aAAa,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,KAAK,SAAS,IAAI,aAAa,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,IAAI,aAAa,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE;AAC/J,MAAM,aAAa,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;AACnC,MAAM,aAAa,CAAC,CAAC,GAAG,SAAS,CAAC;AAClC,MAAM,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC3C,MAAM,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC7C,MAAM,EAAE,CAAC,QAAQ;AACjB,QAAQ,QAAQ,CAAC,CAAC;AAClB,QAAQ,SAAS;AACjB,QAAQ,QAAQ,CAAC,KAAK;AACtB,QAAQ,QAAQ,CAAC,MAAM;AACvB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,wBAAwB,KAAK,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AACnG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,gBAAgB,CAAC,YAAY,EAAE;AACjC,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,MAAM,cAAc,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAC/E,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI;AAC5B,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC;AACxE,IAAI,EAAE,CAAC,eAAe;AACtB,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,cAAc,CAAC,KAAK;AAC1B,MAAM,cAAc,CAAC,MAAM;AAC3B,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,cAAc,CAAC,KAAK;AAC1B,MAAM,cAAc,CAAC,MAAM;AAC3B,MAAM,EAAE,CAAC,gBAAgB;AACzB,MAAM,EAAE,CAAC,OAAO;AAChB,KAAK,CAAC;AACN,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC;AACnE,GAAG;AACH,EAAE,mBAAmB,CAAC,YAAY,EAAE;AACpC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC3B,IAAI,MAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;AAChD,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;AACnD,IAAI,IAAI,YAAY,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM,EAAE;AACnD,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAClF,MAAM,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC;AACxC,MAAM,OAAO,cAAc,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AAClD,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC7C,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG;AACH,EAAE,sBAAsB,CAAC,eAAe,EAAE;AAC1C,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,IAAI,eAAe,CAAC,WAAW,EAAE;AACrC,MAAM,EAAE,CAAC,iBAAiB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AACxD,MAAM,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;AACzC,KAAK;AACL,IAAI,IAAI,eAAe,CAAC,wBAAwB,EAAE;AAClD,MAAM,EAAE,CAAC,iBAAiB,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;AACrE,MAAM,eAAe,CAAC,wBAAwB,GAAG,IAAI,CAAC;AACtD,KAAK;AACL,IAAI,IAAI,eAAe,CAAC,wBAAwB,EAAE;AAClD,MAAM,EAAE,CAAC,kBAAkB,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;AACtE,MAAM,eAAe,CAAC,wBAAwB,GAAG,IAAI,CAAC;AACtD,KAAK;AACL,IAAI,eAAe,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,YAAY,KAAK;AAC/D,MAAM,EAAE,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAC1C,KAAK,CAAC,CAAC;AACP,IAAI,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC5C,GAAG;AACH,EAAE,KAAK,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE;AAC1C,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO;AACb,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AACpC,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC;AAC7C,KAAK;AACL,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;AAC7B,MAAM,UAAU,KAAK,UAAU,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AACxE,MAAM,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACpD,MAAM,MAAM,eAAe,GAAG,UAAU,CAAC;AACzC,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,CAAC,EAAE;AAC5L,QAAQ,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAChD,QAAQ,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAChD,QAAQ,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAChD,QAAQ,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAChD,QAAQ,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;AACtG,OAAO;AACP,KAAK;AACL,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpB,GAAG;AACH,EAAE,qBAAqB,CAAC,YAAY,EAAE;AACtC,IAAI,IAAI,YAAY,CAAC,MAAM;AAC3B,MAAM,OAAO;AACb,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,MAAM,cAAc,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAC/E,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACpD,IAAI,IAAI,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,KAAK,EAAE;AACpD,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG;AACH,EAAE,UAAU,CAAC,YAAY,EAAE,cAAc,EAAE;AAC3C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC3B,IAAI,MAAM,wBAAwB,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;AAC5D,IAAI,cAAc,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;AACvE,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;AACjE,IAAI,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC;AACvE,IAAI,cAAc,CAAC,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC;AACzE,IAAI,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AAC5D,MAAM,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACzC,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;AAC5B,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;AAC5C,UAAU,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC;AACrC,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,qEAAqE,CAAC,CAAC;AACtF,SAAS;AACT,OAAO;AACP,MAAM,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC7C,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC5D,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC;AACzC,MAAM,EAAE,CAAC,oBAAoB;AAC7B,QAAQ,EAAE,CAAC,WAAW;AACtB,QAAQ,EAAE,CAAC,iBAAiB,GAAG,CAAC;AAChC,QAAQ,IAAI;AACZ;AACA,QAAQ,SAAS;AACjB,QAAQ,CAAC;AACT,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE;AAC7B,MAAM,MAAM,eAAe,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;AACrD,MAAM,cAAc,CAAC,WAAW,GAAG,eAAe,CAAC;AACnD,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;AAC1D,MAAM,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACnD,QAAQ,MAAM,gBAAgB,GAAG,EAAE,CAAC,kBAAkB,EAAE,CAAC;AACzD,QAAQ,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;AAC9D,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,cAAc,CAAC,WAAW,GAAG,wBAAwB,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,YAAY,CAAC,YAAY,EAAE,cAAc,EAAE;AAC7C,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;AACpD,IAAI,cAAc,CAAC,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC;AAC7C,IAAI,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC;AAC/C,IAAI,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AAC5D,MAAM,IAAI,CAAC,KAAK,CAAC;AACjB,QAAQ,OAAO;AACf,MAAM,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;AAClF,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE;AAC7B,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACtC,MAAM,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC7B,MAAM,MAAM,eAAe,GAAG,cAAc,CAAC,WAAW,CAAC;AACzD,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;AAC1D,MAAM,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AAC9D,QAAQ,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC;AAC5C,QAAQ,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAChD,QAAQ,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAQ,MAAM,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC;AACzD,QAAQ,MAAM,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AACpE,QAAQ,EAAE,CAAC,gBAAgB;AAC3B,UAAU,EAAE,CAAC,YAAY;AACzB,UAAU,gBAAgB;AAC1B,SAAS,CAAC;AACV,QAAQ,EAAE,CAAC,8BAA8B;AACzC,UAAU,EAAE,CAAC,YAAY;AACzB,UAAU,CAAC;AACX,UAAU,gBAAgB;AAC1B,UAAU,OAAO,CAAC,UAAU;AAC5B,UAAU,OAAO,CAAC,WAAW;AAC7B,SAAS,CAAC;AACV,QAAQ,EAAE,CAAC,uBAAuB;AAClC,UAAU,EAAE,CAAC,WAAW;AACxB,UAAU,EAAE,CAAC,iBAAiB,GAAG,CAAC;AAClC,UAAU,EAAE,CAAC,YAAY;AACzB,UAAU,gBAAgB;AAC1B,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,YAAY,CAAC,cAAc,EAAE;AAC/B,IAAI,IAAI,cAAc,CAAC,WAAW,KAAK,IAAI;AAC3C,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,MAAM,wBAAwB,GAAG,EAAE,CAAC,kBAAkB,EAAE,CAAC;AAC7D,IAAI,cAAc,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;AACvE,IAAI,EAAE,CAAC,gBAAgB;AACvB,MAAM,EAAE,CAAC,YAAY;AACrB,MAAM,wBAAwB;AAC9B,KAAK,CAAC;AACN,IAAI,EAAE,CAAC,uBAAuB;AAC9B,MAAM,EAAE,CAAC,WAAW;AACpB,MAAM,EAAE,CAAC,wBAAwB;AACjC,MAAM,EAAE,CAAC,YAAY;AACrB,MAAM,wBAAwB;AAC9B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,cAAc,CAAC,cAAc,EAAE;AACjC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACjC,IAAI,EAAE,CAAC,gBAAgB;AACvB,MAAM,EAAE,CAAC,YAAY;AACrB,MAAM,cAAc,CAAC,wBAAwB;AAC7C,KAAK,CAAC;AACN,IAAI,IAAI,cAAc,CAAC,IAAI,EAAE;AAC7B,MAAM,EAAE,CAAC,8BAA8B;AACvC,QAAQ,EAAE,CAAC,YAAY;AACvB,QAAQ,CAAC;AACT,QAAQ,EAAE,CAAC,gBAAgB;AAC3B,QAAQ,cAAc,CAAC,KAAK;AAC5B,QAAQ,cAAc,CAAC,MAAM;AAC7B,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,mBAAmB;AAC5B,QAAQ,EAAE,CAAC,YAAY;AACvB,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,GAAG,EAAE,CAAC,aAAa;AAC1F,QAAQ,cAAc,CAAC,KAAK;AAC5B,QAAQ,cAAc,CAAC,MAAM;AAC7B,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,YAAY,EAAE;AAC1B,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC;AACxD,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACzE,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACxD,KAAK;AACL,GAAG;AACH,EAAE,UAAU,CAAC,YAAY,EAAE;AAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS;AACzC,MAAM,OAAO;AACb,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;AAC/D,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1D,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;AACrD,MAAM,YAAY,CAAC,SAAS,CAAC,SAAS;AACtC,QAAQ,aAAa;AACrB,QAAQ,CAAC;AACT,QAAQ,YAAY,CAAC,WAAW,GAAG,aAAa,CAAC,MAAM;AACvD,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,oBAAoB,SAAS,kBAAkB,CAAC;AACtD,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,qBAAqB,EAAE,CAAC;AAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACtC,GAAG;AACH,CAAC;AACD,oBAAoB,CAAC,SAAS,GAAG;AACjC,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC;AACnC,EAAE,IAAI,EAAE,cAAc;AACtB,CAAC,CAAC;AACF,SAAS,sBAAsB,CAAC,MAAM,EAAE,YAAY,EAAE;AACtD,EAAE,MAAM,aAAa,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,eAAe,GAAG,CAAC,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC,CAAC;AACP,EAAE,IAAI,iBAAiB,GAAG,KAAK,CAAC;AAChC,EAAE,IAAI,YAAY,GAAG,CAAC,CAAC;AACvB,EAAE,MAAM,WAAW,GAAG,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACrE,EAAE,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;AACjC,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AACxB,0BAA0B,EAAE,CAAC,CAAC;AAC9B,QAAQ,CAAC,CAAC,CAAC;AACX,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,EAAE;AACrC,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1C,MAAM,IAAI,QAAQ,YAAY,YAAY,EAAE;AAC5C,QAAQ,IAAI,QAAQ,CAAC,GAAG,EAAE;AAC1B,UAAU,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,UAAU,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9B;AACA,sCAAsC,EAAE,CAAC,CAAC;AAC1C,6BAA6B,EAAE,OAAO,CAAC;AACvC,4BAA4B,EAAE,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AAChF;AACA,oBAAoB,CAAC,CAAC,CAAC;AACvB,SAAS,MAAM;AACf,UAAU,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9B,yDAAyD,EAAE,CAAC,CAAC;AAC7D,oBAAoB,CAAC,CAAC,CAAC;AACvB,SAAS;AACT,OAAO,MAAM,IAAI,QAAQ,YAAY,cAAc,EAAE;AACrD,QAAQ,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,QAAQ,aAAa,CAAC,IAAI,CAAC,CAAC;AAC5B;AACA,kCAAkC,EAAE,CAAC,CAAC;AACtC,yBAAyB,EAAE,OAAO,CAAC;AACnC,wBAAwB,EAAE,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AAC5E;AACA,gBAAgB,CAAC,CAAC,CAAC;AACnB,OAAO,MAAM,IAAI,QAAQ,YAAY,aAAa,EAAE;AACpD,QAAQ,MAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,QAAQ,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACjE,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,IAAI,CAAC,iBAAiB,EAAE;AAClC,YAAY,iBAAiB,GAAG,IAAI,CAAC;AACrC,YAAY,eAAe,CAAC,IAAI,CAAC,CAAC;AAClC;AACA,wBAAwB,CAAC,CAAC,CAAC;AAC3B,WAAW;AACX,UAAU,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACzE,UAAU,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9B,0CAA0C,EAAE,CAAC,CAAC,GAAG,EAAE,YAAY,CAAC;AAChE,oBAAoB,CAAC,CAAC,CAAC;AACvB,UAAU,YAAY,EAAE,CAAC;AACzB,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,MAAM,cAAc,GAAG,CAAC,GAAG,eAAe,EAAE,GAAG,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3E,EAAE,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;AACtD,CAAC;AACD,MAAM,aAAa,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,EAAE;AACpC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;AACjC,IAAI,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;AACnC,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACnC,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACrC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,GAAG;AACH,CAAC;AACD,SAAS,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AACtC,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACvC,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC/B,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC3B,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACrB,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE;AAClC,EAAE,QAAQ,IAAI;AACd,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,IAAI,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACxC,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,IAAI,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACxC,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,IAAI,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACxC,IAAI,KAAK,KAAK,CAAC;AACf,IAAI,KAAK,MAAM,CAAC;AAChB,IAAI,KAAK,WAAW,CAAC;AACrB,IAAI,KAAK,gBAAgB;AACzB,MAAM,OAAO,CAAC,CAAC;AACf,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACtC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACtC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACtC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACvC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACvC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACvC,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACpC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACpC,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACpC,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,IAAI,YAAY,CAAC;AAC9B,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,OAAO,CAAC,CAAC;AACT,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,IAAI,YAAY,CAAC;AAC9B,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,OAAO,CAAC,CAAC;AACT,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,IAAI,YAAY,CAAC;AAC9B,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,OAAO,CAAC,CAAC;AACT,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,IAAI,QAAQ,GAAG,IAAI,CAAC;AACpB,MAAM,gBAAgB,GAAG;AACzB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,GAAG,EAAE,KAAK;AACZ,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,YAAY,EAAE,MAAM;AACtB,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE,WAAW;AACzB,EAAE,cAAc,EAAE,WAAW;AAC7B,EAAE,uBAAuB,EAAE,WAAW;AACtC,EAAE,YAAY,EAAE,aAAa;AAC7B,EAAE,gBAAgB,EAAE,aAAa;AACjC,EAAE,yBAAyB,EAAE,aAAa;AAC1C,EAAE,gBAAgB,EAAE,gBAAgB;AACpC,EAAE,oBAAoB,EAAE,gBAAgB;AACxC,EAAE,6BAA6B,EAAE,gBAAgB;AACjD,CAAC,CAAC;AACF,MAAM,oBAAoB,GAAG;AAC7B,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,GAAG,EAAE,QAAQ;AACf,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,KAAK,EAAE,UAAU;AACnB,EAAE,KAAK,EAAE,UAAU;AACnB,CAAC,CAAC;AACF,SAAS,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE;AAC3B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpD,IAAI,QAAQ,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC/C,MAAM,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B,MAAM,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxB,CAAC;AACD,SAAS,mBAAmB,CAAC,EAAE,EAAE,IAAI,EAAE;AACvC,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACtC,EAAE,OAAO,oBAAoB,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC;AACtD,CAAC;AACD,SAAS,8BAA8B,CAAC,OAAO,EAAE,EAAE,EAAE,cAAc,GAAG,KAAK,EAAE;AAC7E,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,eAAe,GAAG,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC;AAChF,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACtD,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3C,MAAM,SAAS;AACf,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,mBAAmB,CAAC,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;AAC5D,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG;AAClC,MAAM,QAAQ,EAAE,CAAC;AACjB;AACA,MAAM,MAAM;AACZ,MAAM,MAAM,EAAE,0BAA0B,CAAC,MAAM,CAAC,CAAC,MAAM;AACvD,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,QAAQ,EAAE,KAAK;AACrB,MAAM,KAAK,EAAE,CAAC;AACd,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACvC,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC;AACvC,MAAM,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC5B,GAAG,MAAM;AACT,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL,GAAG;AACH,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;AACD,SAAS,UAAU,CAAC,OAAO,EAAE,EAAE,EAAE;AACjC,EAAE,IAAI,CAAC,EAAE,CAAC,qBAAqB;AAC/B,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,MAAM,aAAa,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,mBAAmB,GAAG,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,qBAAqB,CAAC,CAAC;AACxF,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,mBAAmB,EAAE,CAAC,EAAE,EAAE;AAChD,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,yBAAyB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC1D,IAAI,MAAM,iBAAiB,GAAG,EAAE,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACrE,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,8BAA8B,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,uBAAuB,CAAC,CAAC;AAC3F,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG;AAC1B,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,IAAI;AACV,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,aAAa,CAAC;AACvB,CAAC;AACD,SAAS,cAAc,CAAC,OAAO,EAAE,EAAE,EAAE;AACrC,EAAE,MAAM,QAAQ,GAAG,EAAE,CAAC;AACtB,EAAE,MAAM,aAAa,GAAG,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC;AAC5E,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE;AAC1C,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACxD,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAC1D,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACzD,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,EAAE,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/C,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG;AACrB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,IAAI;AACV,MAAM,IAAI,EAAE,WAAW,CAAC,IAAI;AAC5B,MAAM,OAAO;AACb,MAAM,KAAK,EAAE,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC;AACjD,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACD,SAAS,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE;AAC1C,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACrG,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAChD,EAAE,MAAM,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC5C,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;AAC1H,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;AACzB,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;AACvB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAClC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC3D,IAAI,OAAO,CAAC,IAAI,CAAC,qDAAqD,EAAE,iBAAiB,CAAC,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnD,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC;AACnC,EAAE,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC3B,EAAE,OAAO,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;AAC3D,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3B,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC;AACD,SAAS,eAAe,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE;AACpE,EAAE,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE;AACxD,IAAI,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,YAAY,EAAE,EAAE,CAAC,cAAc,CAAC,EAAE;AACjE,MAAM,oBAAoB,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,EAAE;AACnE,MAAM,oBAAoB,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;AAChE,IAAI,IAAI,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE;AAC9C,MAAM,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;AAC5F,KAAK;AACL,GAAG;AACH,CAAC;AACD,SAAS,eAAe,CAAC,EAAE,EAAE,OAAO,EAAE;AACtC,EAAE,MAAM,YAAY,GAAG,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3E,EAAE,MAAM,YAAY,GAAG,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC/E,EAAE,MAAM,YAAY,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;AAC1C,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC9C,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AAC9C,EAAE,MAAM,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;AACtE,EAAE,IAAI,yBAAyB,EAAE;AACjC,IAAI,IAAI,OAAO,EAAE,CAAC,yBAAyB,KAAK,UAAU,EAAE;AAC5D,MAAM,IAAI,CAAC,CAAC,2EAA2E,CAAC,CAAC,CAAC;AAC1F,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,yBAAyB;AAClC,QAAQ,YAAY;AACpB,QAAQ,yBAAyB,CAAC,KAAK;AACvC,QAAQ,yBAAyB,CAAC,UAAU,KAAK,UAAU,GAAG,EAAE,CAAC,gBAAgB,GAAG,EAAE,CAAC,mBAAmB;AAC1G,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,EAAE,EAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AAC/B,EAAE,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE;AAC7D,IAAI,eAAe,CAAC,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,OAAO,CAAC,cAAc,GAAG,8BAA8B;AACzD,IAAI,YAAY;AAChB,IAAI,EAAE;AACN,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AAC1E,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAC1D,EAAE,OAAO,CAAC,iBAAiB,GAAG,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAC3D,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAChC,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAChC,EAAE,MAAM,WAAW,GAAG,EAAE,CAAC;AACzB,EAAE,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE;AACxC,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG;AACrB,MAAM,QAAQ,EAAE,EAAE,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC;AACtD,MAAM,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC;AAC/C,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACjE,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC;AACD,MAAM,eAAe,GAAG;AACxB,EAAE,YAAY,EAAE,CAAC;AACjB,EAAE,UAAU,EAAE,CAAC;AACf,CAAC,CAAC;AACF,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,gBAAgB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,oBAAoB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AACzE,GAAG;AACH,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,gBAAgB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,oBAAoB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,WAAW,GAAG,sBAAsB,EAAE,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE;AACzB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACvC,IAAI,IAAI,QAAQ;AAChB,MAAM,OAAO;AACb,IAAI,eAAe,CAAC,YAAY,GAAG,CAAC,CAAC;AACrC,IAAI,eAAe,CAAC,UAAU,GAAG,CAAC,CAAC;AACnC,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACxE,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/G,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;AACrF,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,YAAY,EAAE;AACnC,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;AACvG,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE;AAClD,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAC/C,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAClE,IAAI,MAAM,gBAAgB,GAAG,YAAY,CAAC,eAAe,CAAC;AAC1D,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACvC,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACvD,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AAC3E,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC;AAC5C,MAAM,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;AAC3D,QAAQ,YAAY,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AAC7D,OAAO,MAAM;AACb,QAAQ,YAAY,CAAC,eAAe,CAAC,QAAQ,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;AACtE,OAAO;AACP,KAAK,MAAM,IAAI,YAAY,CAAC,uBAAuB,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE;AACjF,MAAM,YAAY,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AAChF,IAAI,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,KAAK,aAAa;AACjE,MAAM,OAAO;AACb,IAAI,WAAW,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC;AAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,mBAAmB,CAAC,WAAW,CAAC,OAAO,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;AACjG,GAAG;AACH,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO;AACvC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;AAClC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AACnF,GAAG;AACH,EAAE,kBAAkB,CAAC,OAAO,EAAE;AAC9B,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7B,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACpE,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;AAC1D,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACrD,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;AAC5B,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,MAAM,EAAE,YAAY,EAAE;AAC5C,IAAI,OAAO,sBAAsB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,MAAM,yBAAyB,GAAG;AAClC,EAAE,GAAG,EAAE,CAAC;AACR;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,GAAG,EAAE,CAAC;AACR;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,IAAI,EAAE,CAAC;AACT;AACA;AACA,SAAS,CAAC;AACV,EAAE,YAAY,EAAE,CAAC;AACjB;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,YAAY,EAAE,CAAC;AACjB;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,YAAY,EAAE,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,EAAE,aAAa,EAAE,CAAC,wCAAwC,CAAC;AAC3D,EAAE,aAAa,EAAE,CAAC,wCAAwC,CAAC;AAC3D,EAAE,aAAa,EAAE,CAAC,wCAAwC,CAAC;AAC3D,CAAC,CAAC;AACF,MAAM,wBAAwB,GAAG;AACjC,EAAE,GAAG,EAAE,CAAC,2BAA2B,CAAC;AACpC,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,aAAa,EAAE,CAAC,wCAAwC,CAAC;AAC3D,EAAE,aAAa,EAAE,CAAC,wCAAwC,CAAC;AAC3D,EAAE,aAAa,EAAE,CAAC,wCAAwC,CAAC;AAC3D,EAAE,GAAG,EAAE,CAAC,2BAA2B,CAAC;AACpC,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,GAAG,EAAE,CAAC,2BAA2B,CAAC;AACpC,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,WAAW,EAAE,CAAC,2BAA2B,CAAC;AAC5C,EAAE,IAAI,EAAE,CAAC,2BAA2B,CAAC;AACrC,EAAE,YAAY,EAAE,CAAC,2BAA2B,CAAC;AAC7C,EAAE,YAAY,EAAE,CAAC,2BAA2B,CAAC;AAC7C,EAAE,YAAY,EAAE,CAAC,2BAA2B,CAAC;AAC7C,CAAC,CAAC;AACF,SAAS,oBAAoB,CAAC,KAAK,EAAE,WAAW,EAAE;AAClD,EAAE,MAAM,aAAa,GAAG,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC,CAAC;AACP,EAAE,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE;AAClC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;AACzB,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,YAAY,EAAE;AACrD,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;AACnC,UAAU,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9B,4DAA4D,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACvE,oBAAoB,CAAC,CAAC,CAAC;AACvB,SAAS,MAAM;AACf,UAAU,aAAa,CAAC,IAAI,CAAC,CAAC;AAC9B,8DAA8D,EAAE,CAAC,CAAC;AAClE,oBAAoB,CAAC,CAAC,CAAC;AACvB,SAAS;AACT,OAAO,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,cAAc,EAAE;AAC9D,QAAQ,aAAa,CAAC,IAAI,CAAC,CAAC;AAC5B,8DAA8D,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACzE,oBAAoB,CAAC,CAAC,CAAC;AACvB,OAAO;AACP,MAAM,SAAS;AACf,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAC/C,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpD,MAAM,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAChE,QAAQ,aAAa,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACxE,QAAQ,MAAM,GAAG,IAAI,CAAC;AACtB,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,yBAAyB,GAAG,wBAAwB,CAAC;AACrG,MAAM,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7F,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC;AAC1B,qBAAqB,EAAE,CAAC,CAAC;AACzB;AACA,oBAAoB,EAAE,CAAC,CAAC;AACxB,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACpF,CAAC;AACD,MAAM,oBAAoB,CAAC;AAC3B;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;AACpC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;AACnB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,GAAG;AACH,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC/C,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACvE,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,WAAW,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACzF,MAAM,WAAW,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;AACjE,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACpE,MAAM,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAClF,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,uBAAuB,CAAC,KAAK,EAAE,OAAO,EAAE;AAC1C,IAAI,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC3H,GAAG;AACH,EAAE,0BAA0B,CAAC,KAAK,EAAE,OAAO,EAAE;AAC7C,IAAI,MAAM,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;AACrI,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;AAC1B,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;AAChF,KAAK;AACL,IAAI,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACzD,IAAI,OAAO,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,qBAAqB,CAAC,KAAK,EAAE,WAAW,EAAE;AAC5C,IAAI,OAAO,oBAAoB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACpD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE;AAC5C,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;AAC9B,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE;AAC1B,QAAQ,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC1C,OAAO;AACP,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH,CAAC;AACD,oBAAoB,CAAC,SAAS,GAAG;AACjC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,cAAc;AACtB,CAAC,CAAC;AACF,SAAS,wBAAwB,CAAC,EAAE,EAAE;AACtC,EAAE,MAAM,QAAQ,GAAG,EAAE,CAAC;AACtB,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,CAAC;AACrD,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,CAAC;AAC7F,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,CAAC;AACrF,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,EAAE,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,CAAC;AAClG,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAC/D,EAAE,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,CAAC;AAClG,EAAE,QAAQ,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,mBAAmB,CAAC,CAAC;AACrD,EAAE,MAAM,QAAQ,GAAG,EAAE,EAAE,YAAY,UAAU,CAAC,GAAG,EAAE,CAAC,wBAAwB,EAAE,CAAC,CAAC;AAChF,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AACpE,IAAI,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AACpE,GAAG,MAAM;AACT,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;AACpD,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAChF,MAAM,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAChF,KAAK;AACL,GAAG;AACH,EAAE,OAAO,QAAQ,CAAC;AAClB,CAAC;AACD,MAAM,KAAK,GAAG,CAAC,CAAC;AAChB,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,OAAO,GAAG,CAAC,CAAC;AAClB,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,OAAO,GAAG,CAAC,CAAC;AAClB,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC;AAC7C,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACtC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AACzC,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAC7C,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAC1C,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAC7C,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACtC,GAAG;AACH,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,aAAa,GAAG,wBAAwB,CAAC,EAAE,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,KAAK,EAAE;AACb,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;AACzC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;AACrC,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AAC3C,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,IAAI,GAAG,CAAC,EAAE;AACtB,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1D,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,CAAC;AACnB,QAAQ,CAAC,EAAE,CAAC;AACZ,OAAO;AACP,MAAM,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AAChC,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAClC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;AACzC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC9C,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE;AAClB,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AACzD,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE;AACnB,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC;AACvE,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;AAC9D,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;AAC7D,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;AACpC,MAAM,KAAK,GAAG,QAAQ,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAClC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3C,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACvB,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3B,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3B,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B,MAAM,EAAE,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC9B,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC5B,MAAM,EAAE,CAAC,qBAAqB,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;AACzD,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxC,GAAG;AACH;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE;AAC5B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAC/B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,KAAK,MAAM,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AACvC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACnC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE;AACxC,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACzC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE;AAC5C,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;AACpD,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;AACnB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3B,GAAG;AACH,CAAC,CAAC;AACF,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,CAAC,CAAC;AACF,IAAI,aAAa,GAAG,cAAc,CAAC;AACnC,MAAM,SAAS,CAAC;AAChB,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC;AACxC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC;AACvC,IAAI,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC;AAClC,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,GAAG;AACH,CAAC;AACD,MAAM,2BAA2B,GAAG;AACpC,EAAE,EAAE,EAAE,QAAQ;AACd,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;AAChC,IAAI,IAAI,SAAS,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;AAChF,MAAM,EAAE,CAAC,aAAa;AACtB,QAAQ,EAAE,CAAC,UAAU;AACrB,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,MAAM,CAAC,KAAK;AACpB,QAAQ,MAAM,CAAC,MAAM;AACrB,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,SAAS,CAAC,IAAI;AACtB,QAAQ,MAAM,CAAC,QAAQ;AACvB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,UAAU;AACnB,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,cAAc;AAChC,QAAQ,MAAM,CAAC,KAAK;AACpB,QAAQ,MAAM,CAAC,MAAM;AACrB,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,SAAS,CAAC,IAAI;AACtB,QAAQ,MAAM,CAAC,QAAQ;AACvB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACnC,IAAI,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AACrC,GAAG;AACH,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG;AAC5B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,aAAa,EAAE,IAAI;AACrB,EAAE,aAAa,EAAE,IAAI;AACrB,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,iBAAiB,EAAE,IAAI;AACzB,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B;AACA;AACA,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,kBAAkB,EAAE,IAAI;AAC1B,EAAE,uBAAuB,EAAE,IAAI;AAC/B,EAAE,iBAAiB,EAAE,IAAI;AACzB,EAAE,sBAAsB,EAAE,IAAI;AAC9B,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,eAAe,EAAE,IAAI;AACvB,EAAE,eAAe,EAAE,IAAI;AACvB;AACA;AACA,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,gBAAgB,EAAE,IAAI;AACxB,EAAE,qBAAqB,EAAE,IAAI;AAC7B,EAAE,iBAAiB,EAAE,IAAI;AACzB,EAAE,sBAAsB,EAAE,IAAI;AAC9B,EAAE,iBAAiB,EAAE,IAAI;AACzB,EAAE,sBAAsB,EAAE,IAAI;AAC9B,EAAE,iBAAiB,EAAE,IAAI;AACzB,EAAE,sBAAsB,EAAE,IAAI;AAC9B,EAAE,kBAAkB,EAAE,IAAI;AAC1B,EAAE,uBAAuB,EAAE,IAAI;AAC/B,EAAE,kBAAkB,EAAE,IAAI;AAC1B,EAAE,uBAAuB,EAAE,IAAI;AAC/B,EAAE,kBAAkB,EAAE,IAAI;AAC1B,EAAE,uBAAuB,EAAE,IAAI;AAC/B,CAAC,CAAC;AACF,MAAM,iCAAiC,GAAG;AAC1C,EAAE,EAAE,EAAE,YAAY;AAClB,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;AAChC,IAAI,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;AAC3C,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC;AACrC,IAAI,IAAI,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC;AACvC,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC5D,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7C,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,EAAE,CAAC,oBAAoB;AAC/B,UAAU,EAAE,CAAC,UAAU;AACvB,UAAU,CAAC;AACX,UAAU,SAAS,CAAC,cAAc;AAClC,UAAU,QAAQ;AAClB,UAAU,SAAS;AACnB,UAAU,CAAC;AACX,UAAU,WAAW;AACrB,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,EAAE,CAAC,UAAU;AACrB,UAAU,EAAE,CAAC,UAAU;AACvB,UAAU,CAAC;AACX,UAAU,SAAS,CAAC,cAAc;AAClC,UAAU,QAAQ;AAClB,UAAU,SAAS;AACnB,UAAU,CAAC;AACX,UAAU,SAAS,CAAC,MAAM;AAC1B,UAAU,SAAS,CAAC,IAAI;AACxB,UAAU,WAAW;AACrB,SAAS,CAAC;AACV,OAAO;AACP,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,MAAM,qBAAqB,GAAG;AAC9B,EAAE,EAAE,EAAE,OAAO;AACb,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,EAAE;AAC9C,IAAI,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC;AACpC,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;AACtC,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC;AAC3C,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC;AAC7C,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAC/C,IAAI,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;AACjD,IAAI,IAAI,aAAa,GAAG,YAAY,IAAI,cAAc,GAAG,aAAa,EAAE;AACxE,MAAM,IAAI,OAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,aAAa,EAAE;AAClE,QAAQ,EAAE,CAAC,UAAU;AACrB,UAAU,SAAS,CAAC,MAAM;AAC1B,UAAU,CAAC;AACX,UAAU,SAAS,CAAC,cAAc;AAClC,UAAU,YAAY;AACtB,UAAU,aAAa;AACvB,UAAU,CAAC;AACX,UAAU,SAAS,CAAC,MAAM;AAC1B,UAAU,SAAS,CAAC,IAAI;AACxB,UAAU,IAAI;AACd,SAAS,CAAC;AACV,OAAO;AACP,MAAM,IAAI,YAAY,KAAK,CAAC,EAAE;AAC9B,QAAQ,EAAE,CAAC,aAAa;AACxB,UAAU,EAAE,CAAC,UAAU;AACvB,UAAU,CAAC;AACX,UAAU,CAAC;AACX,UAAU,CAAC;AACX,UAAU,aAAa;AACvB,UAAU,cAAc;AACxB,UAAU,SAAS,CAAC,MAAM;AAC1B,UAAU,SAAS,CAAC,IAAI;AACxB,UAAU,MAAM,CAAC,QAAQ;AACzB,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,EAAE,CAAC,aAAa;AACxB,UAAU,EAAE,CAAC,UAAU;AACvB,UAAU,CAAC;AACX,UAAU,CAAC;AACX,UAAU,CAAC;AACX,UAAU,SAAS,CAAC,MAAM;AAC1B,UAAU,SAAS,CAAC,IAAI;AACxB,UAAU,MAAM,CAAC,QAAQ;AACzB,SAAS,CAAC;AACV,OAAO;AACP,KAAK,MAAM,IAAI,OAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,aAAa,EAAE;AACvE,MAAM,EAAE,CAAC,aAAa;AACtB,QAAQ,EAAE,CAAC,UAAU;AACrB,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,SAAS,CAAC,IAAI;AACtB,QAAQ,MAAM,CAAC,QAAQ;AACvB,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,YAAY,KAAK,CAAC,EAAE;AACnC,MAAM,EAAE,CAAC,UAAU;AACnB,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,cAAc;AAChC,QAAQ,YAAY;AACpB,QAAQ,aAAa;AACrB,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,SAAS,CAAC,IAAI;AACtB,QAAQ,MAAM,CAAC,QAAQ;AACvB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,UAAU;AACnB,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,cAAc;AAChC,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,SAAS,CAAC,IAAI;AACtB,QAAQ,MAAM,CAAC,QAAQ;AACvB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,GAAG,YAAY,CAAC;AACnC,IAAI,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC;AACrC,GAAG;AACH,CAAC,CAAC;AACF,MAAM,qBAAqB,GAAG;AAC9B,EAAE,EAAE,EAAE,OAAO;AACb,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,EAAE;AAC9C,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AACzB,MAAM,EAAE,CAAC,UAAU;AACnB,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,cAAc;AAChC,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,SAAS,CAAC,IAAI;AACtB,QAAQ,IAAI;AACZ,OAAO,CAAC;AACR,MAAM,OAAO;AACb,KAAK;AACL,IAAI,qBAAqB,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;AACtE,GAAG;AACH,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG;AAC5B,EAAE,MAAM,EAAE,IAAI;AACd,EAAE,OAAO,EAAE,IAAI;AACf,CAAC,CAAC;AACF,MAAM,yBAAyB,GAAG;AAClC,EAAE,MAAM,EAAE;AACV,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG;AAC5B,EAAE,eAAe,EAAE,KAAK;AACxB,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,eAAe,EAAE,KAAK;AACxB,CAAC,CAAC;AACF,MAAM,sBAAsB,GAAG;AAC/B,EAAE,KAAK,EAAE,GAAG;AACZ,EAAE,IAAI,EAAE,GAAG;AACX,EAAE,KAAK,EAAE,GAAG;AACZ,EAAE,YAAY,EAAE,GAAG;AACnB,EAAE,OAAO,EAAE,GAAG;AACd,EAAE,WAAW,EAAE,GAAG;AAClB,EAAE,eAAe,EAAE,GAAG;AACtB,EAAE,MAAM,EAAE,GAAG;AACb,CAAC,CAAC;AACF,SAAS,gBAAgB,CAAC,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE;AACrH,EAAE,MAAM,SAAS,GAAG,UAAU,CAAC;AAC/B,EAAE,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,YAAY,KAAK,QAAQ,IAAI,KAAK,CAAC,YAAY,KAAK,QAAQ,IAAI,KAAK,CAAC,YAAY,KAAK,QAAQ,EAAE;AAC/H,IAAI,MAAM,SAAS,GAAG,mBAAmB,CAAC,UAAU,GAAG,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7F,IAAI,MAAM,SAAS,GAAG,mBAAmB,CAAC,UAAU,GAAG,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7F,IAAI,MAAM,SAAS,GAAG,mBAAmB,CAAC,UAAU,GAAG,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC7F,IAAI,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;AAChE,IAAI,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;AAChE,IAAI,IAAI,EAAE,CAAC,cAAc;AACzB,MAAM,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,SAAS,KAAK,QAAQ,EAAE;AACtD,IAAI,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/F,GAAG;AACH,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,YAAY,KAAK,QAAQ,EAAE;AAC3D,MAAM,MAAM,YAAY,GAAG,yBAAyB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC1F,MAAM,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;AACzE,KAAK;AACL,GAAG,MAAM;AACT,IAAI,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/F,GAAG;AACH,EAAE,IAAI,cAAc,IAAI,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE;AACjD,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC,CAAC;AAChH,IAAI,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;AACpF,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,EAAE,CAAC,cAAc,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AAClG,GAAG;AACH,CAAC;AACD,SAAS,mBAAmB,CAAC,EAAE,EAAE;AACjC,EAAE,OAAO;AACT;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,GAAG;AACnB,IAAI,OAAO,EAAE,EAAE,CAAC,GAAG;AACnB,IAAI,MAAM,EAAE,EAAE,CAAC,GAAG;AAClB,IAAI,MAAM,EAAE,EAAE,CAAC,GAAG;AAClB;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,GAAG;AACnB,IAAI,OAAO,EAAE,EAAE,CAAC,GAAG;AACnB,IAAI,QAAQ,EAAE,EAAE,CAAC,GAAG;AACpB,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnB,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnB,IAAI,OAAO,EAAE,EAAE,CAAC,EAAE;AAClB,IAAI,OAAO,EAAE,EAAE,CAAC,EAAE;AAClB;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,GAAG;AACnB,IAAI,OAAO,EAAE,EAAE,CAAC,GAAG;AACnB,IAAI,QAAQ,EAAE,EAAE,CAAC,GAAG;AACpB,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnB,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnB,IAAI,SAAS,EAAE,EAAE,CAAC,EAAE;AACpB,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,iBAAiB,EAAE,EAAE,CAAC,IAAI;AAC9B;AACA,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,SAAS,EAAE,EAAE,CAAC,IAAI;AACtB,IAAI,SAAS,EAAE,EAAE,CAAC,IAAI;AACtB,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,iBAAiB,EAAE,EAAE,CAAC,IAAI;AAC9B,IAAI,YAAY,EAAE,EAAE,CAAC,GAAG;AACxB,IAAI,YAAY,EAAE,EAAE,CAAC,IAAI;AACzB,IAAI,aAAa,EAAE,EAAE,CAAC,GAAG;AACzB;AACA,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnB,IAAI,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnB,IAAI,SAAS,EAAE,EAAE,CAAC,EAAE;AACpB,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,WAAW,EAAE,EAAE,CAAC,IAAI;AACxB;AACA,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,WAAW,EAAE,EAAE,CAAC,IAAI;AACxB;AACA,IAAI,QAAQ,EAAE,EAAE,CAAC,cAAc;AAC/B,IAAI,YAAY,EAAE,EAAE,CAAC,eAAe;AACpC,IAAI,WAAW,EAAE,EAAE,CAAC,eAAe;AACnC,IAAI,sBAAsB,EAAE,EAAE,CAAC,aAAa;AAC5C,IAAI,YAAY,EAAE,EAAE,CAAC,eAAe;AACpC,IAAI,uBAAuB,EAAE,EAAE,CAAC,aAAa;AAC7C,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,2BAA2B,CAAC,EAAE,EAAE,WAAW,EAAE;AACtD,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC;AAC3B,EAAE,IAAI,EAAE,EAAE,YAAY,UAAU,CAAC,GAAG,EAAE,CAAC,wBAAwB,EAAE,CAAC,EAAE;AACpE,IAAI,IAAI,GAAG;AACX,MAAM,iBAAiB,EAAE,EAAE,CAAC,YAAY;AACxC,MAAM,iBAAiB,EAAE,EAAE,CAAC,YAAY;AACxC,KAAK,CAAC;AACN,IAAI,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC;AAC1B,GAAG,MAAM,IAAI,WAAW,CAAC,IAAI,EAAE;AAC/B,IAAI,IAAI,GAAG;AACX,MAAM,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB;AAC1D,MAAM,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB;AAC1D,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO;AACT;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,EAAE;AAClB,IAAI,OAAO,EAAE,EAAE,CAAC,QAAQ;AACxB,IAAI,MAAM,EAAE,EAAE,CAAC,IAAI;AACnB,IAAI,MAAM,EAAE,EAAE,CAAC,GAAG;AAClB;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,KAAK;AACrB,IAAI,OAAO,EAAE,EAAE,CAAC,IAAI;AACpB,IAAI,QAAQ,EAAE,EAAE,CAAC,IAAI;AACrB,IAAI,QAAQ,EAAE,EAAE,CAAC,GAAG;AACpB,IAAI,QAAQ,EAAE,EAAE,CAAC,SAAS;AAC1B,IAAI,OAAO,EAAE,EAAE,CAAC,KAAK;AACrB,IAAI,OAAO,EAAE,EAAE,CAAC,IAAI;AACpB;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,KAAK;AACrB,IAAI,OAAO,EAAE,EAAE,CAAC,IAAI;AACpB,IAAI,QAAQ,EAAE,EAAE,CAAC,IAAI;AACrB,IAAI,QAAQ,EAAE,EAAE,CAAC,MAAM;AACvB,IAAI,QAAQ,EAAE,EAAE,CAAC,KAAK;AACtB,IAAI,SAAS,EAAE,EAAE,CAAC,KAAK;AACvB,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,GAAG,IAAI;AACX;AACA,IAAI,UAAU,EAAE,EAAE,CAAC,WAAW;AAC9B,IAAI,SAAS,EAAE,EAAE,CAAC,OAAO;AACzB,IAAI,SAAS,EAAE,EAAE,CAAC,MAAM;AACxB,IAAI,UAAU;AACd,IAAI,YAAY,EAAE,EAAE,CAAC,OAAO;AAC5B,IAAI,YAAY,EAAE,EAAE,CAAC,QAAQ;AAC7B,IAAI,aAAa,EAAE,EAAE,CAAC,cAAc;AACpC;AACA,IAAI,QAAQ,EAAE,EAAE,CAAC,MAAM;AACvB,IAAI,QAAQ,EAAE,EAAE,CAAC,KAAK;AACtB,IAAI,SAAS,EAAE,EAAE,CAAC,KAAK;AACvB,IAAI,UAAU,EAAE,EAAE,CAAC,QAAQ;AAC3B,IAAI,UAAU,EAAE,EAAE,CAAC,OAAO;AAC1B,IAAI,WAAW,EAAE,EAAE,CAAC,OAAO;AAC3B;AACA,IAAI,UAAU,EAAE,EAAE,CAAC,QAAQ;AAC3B,IAAI,UAAU,EAAE,EAAE,CAAC,OAAO;AAC1B,IAAI,WAAW,EAAE,EAAE,CAAC,OAAO;AAC3B;AACA,IAAI,QAAQ,EAAE,EAAE,CAAC,cAAc;AAC/B,IAAI,YAAY,EAAE,EAAE,CAAC,iBAAiB;AACtC,IAAI,WAAW,EAAE,EAAE,CAAC,iBAAiB;AACrC,IAAI,sBAAsB,EAAE,EAAE,CAAC,gBAAgB;AAC/C,IAAI,YAAY,EAAE,EAAE,CAAC,kBAAkB;AACvC,IAAI,uBAAuB,EAAE,EAAE,CAAC,iBAAiB;AACjD;AACA,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG;AAC1B,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,6BAA6B;AACtE,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,6BAA6B;AACtE,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,6BAA6B;AACtE,KAAK,GAAG,EAAE;AACV,IAAI,GAAG,WAAW,CAAC,SAAS,GAAG;AAC/B,MAAM,qBAAqB,EAAE,WAAW,CAAC,SAAS,CAAC,mCAAmC;AACtF,MAAM,qBAAqB,EAAE,WAAW,CAAC,SAAS,CAAC,mCAAmC;AACtF,MAAM,qBAAqB,EAAE,WAAW,CAAC,SAAS,CAAC,mCAAmC;AACtF,KAAK,GAAG,EAAE;AACV,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG;AAC1B,MAAM,aAAa,EAAE,WAAW,CAAC,IAAI,CAAC,wBAAwB;AAC9D,MAAM,aAAa,EAAE,WAAW,CAAC,IAAI,CAAC,+BAA+B;AACrE,MAAM,cAAc,EAAE,WAAW,CAAC,IAAI,CAAC,8BAA8B;AACrE,MAAM,cAAc,EAAE,WAAW,CAAC,IAAI,CAAC,qCAAqC;AAC5E,KAAK,GAAG,EAAE;AACV,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG;AAC1B,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAC7E,MAAM,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,sCAAsC;AAChF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,8BAA8B;AACvE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,KAAK,GAAG,EAAE;AACV,IAAI,GAAG,WAAW,CAAC,GAAG,GAAG;AACzB,MAAM,gBAAgB,EAAE,WAAW,CAAC,GAAG,CAAC,oBAAoB;AAC5D,MAAM,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAAC,qBAAqB;AAClE,MAAM,kBAAkB,EAAE,WAAW,CAAC,GAAG,CAAC,wCAAwC;AAClF,MAAM,uBAAuB,EAAE,WAAW,CAAC,GAAG,CAAC,yCAAyC;AACxF,MAAM,iBAAiB,EAAE,WAAW,CAAC,GAAG,CAAC,yBAAyB;AAClE,MAAM,sBAAsB,EAAE,WAAW,CAAC,GAAG,CAAC,gCAAgC;AAC9E,MAAM,cAAc,EAAE,WAAW,CAAC,GAAG,CAAC,kBAAkB;AACxD;AACA,MAAM,eAAe,EAAE,WAAW,CAAC,GAAG,CAAC,0BAA0B;AACjE;AACA,KAAK,GAAG,EAAE;AACV,IAAI,GAAG,WAAW,CAAC,IAAI,GAAG;AAC1B,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,gBAAgB,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AACrE,MAAM,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,oCAAoC;AAClF,MAAM,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,6BAA6B;AACvE,MAAM,sBAAsB,EAAE,WAAW,CAAC,IAAI,CAAC,qCAAqC;AACpF,MAAM,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,6BAA6B;AACvE,MAAM,sBAAsB,EAAE,WAAW,CAAC,IAAI,CAAC,qCAAqC;AACpF,MAAM,iBAAiB,EAAE,WAAW,CAAC,IAAI,CAAC,6BAA6B;AACvE,MAAM,sBAAsB,EAAE,WAAW,CAAC,IAAI,CAAC,qCAAqC;AACpF,MAAM,kBAAkB,EAAE,WAAW,CAAC,IAAI,CAAC,8BAA8B;AACzE,MAAM,uBAAuB,EAAE,WAAW,CAAC,IAAI,CAAC,sCAAsC;AACtF,MAAM,kBAAkB,EAAE,WAAW,CAAC,IAAI,CAAC,8BAA8B;AACzE,MAAM,uBAAuB,EAAE,WAAW,CAAC,IAAI,CAAC,sCAAsC;AACtF,MAAM,kBAAkB,EAAE,WAAW,CAAC,IAAI,CAAC,8BAA8B;AACzE,MAAM,uBAAuB,EAAE,WAAW,CAAC,IAAI,CAAC,sCAAsC;AACtF,KAAK,GAAG,EAAE;AACV,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,iBAAiB,CAAC,EAAE,EAAE;AAC/B,EAAE,OAAO;AACT;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,aAAa;AAC7B,IAAI,OAAO,EAAE,EAAE,CAAC,IAAI;AACpB,IAAI,MAAM,EAAE,EAAE,CAAC,aAAa;AAC5B,IAAI,MAAM,EAAE,EAAE,CAAC,IAAI;AACnB;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,cAAc;AAC9B,IAAI,OAAO,EAAE,EAAE,CAAC,KAAK;AACrB,IAAI,QAAQ,EAAE,EAAE,CAAC,UAAU;AAC3B,IAAI,QAAQ,EAAE,EAAE,CAAC,aAAa;AAC9B,IAAI,QAAQ,EAAE,EAAE,CAAC,IAAI;AACrB,IAAI,OAAO,EAAE,EAAE,CAAC,aAAa;AAC7B,IAAI,OAAO,EAAE,EAAE,CAAC,IAAI;AACpB;AACA,IAAI,OAAO,EAAE,EAAE,CAAC,YAAY;AAC5B,IAAI,OAAO,EAAE,EAAE,CAAC,GAAG;AACnB,IAAI,QAAQ,EAAE,EAAE,CAAC,KAAK;AACtB,IAAI,QAAQ,EAAE,EAAE,CAAC,cAAc;AAC/B,IAAI,QAAQ,EAAE,EAAE,CAAC,KAAK;AACtB,IAAI,SAAS,EAAE,EAAE,CAAC,UAAU;AAC5B,IAAI,UAAU,EAAE,EAAE,CAAC,aAAa;AAChC,IAAI,iBAAiB,EAAE,EAAE,CAAC,aAAa;AACvC;AACA,IAAI,UAAU,EAAE,EAAE,CAAC,IAAI;AACvB,IAAI,SAAS,EAAE,EAAE,CAAC,aAAa;AAC/B,IAAI,SAAS,EAAE,EAAE,CAAC,IAAI;AACtB,IAAI,UAAU,EAAE,EAAE,CAAC,aAAa;AAChC,IAAI,iBAAiB,EAAE,EAAE,CAAC,aAAa;AACvC,IAAI,YAAY,EAAE,EAAE,CAAC,wBAAwB;AAC7C,IAAI,YAAY,EAAE,EAAE,CAAC,2BAA2B;AAChD,IAAI,aAAa,EAAE,EAAE,CAAC,4BAA4B;AAClD;AACA,IAAI,QAAQ,EAAE,EAAE,CAAC,YAAY;AAC7B,IAAI,QAAQ,EAAE,EAAE,CAAC,GAAG;AACpB,IAAI,SAAS,EAAE,EAAE,CAAC,KAAK;AACvB,IAAI,UAAU,EAAE,EAAE,CAAC,cAAc;AACjC,IAAI,UAAU,EAAE,EAAE,CAAC,KAAK;AACxB,IAAI,WAAW,EAAE,EAAE,CAAC,UAAU;AAC9B;AACA,IAAI,UAAU,EAAE,EAAE,CAAC,YAAY;AAC/B,IAAI,UAAU,EAAE,EAAE,CAAC,GAAG;AACtB,IAAI,WAAW,EAAE,EAAE,CAAC,KAAK;AACzB;AACA,IAAI,QAAQ,EAAE,EAAE,CAAC,aAAa;AAC9B,IAAI,YAAY,EAAE,EAAE,CAAC,cAAc;AACnC,IAAI,WAAW,EAAE,EAAE,CAAC,YAAY;AAChC,IAAI,sBAAsB,EAAE,EAAE,CAAC,iBAAiB;AAChD,IAAI,YAAY,EAAE,EAAE,CAAC,KAAK;AAC1B,IAAI,uBAAuB,EAAE,EAAE,CAAC,8BAA8B;AAC9D,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,QAAQ,GAAG;AACpB,MAAM,KAAK,EAAE,qBAAqB;AAClC,MAAM,MAAM,EAAE,2BAA2B;AACzC,MAAM,KAAK,EAAE,qBAAqB;AAClC,MAAM,UAAU,EAAE,iCAAiC;AACnD,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACnC,IAAI,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;AACtC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;AAC1C,MAAM,IAAI,CAAC,0BAA0B,GAAG,2BAA2B,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC3G,MAAM,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,EAAE,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,kBAAkB,GAAG,mBAAmB,CAAC,EAAE,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACnC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACjC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAClC,KAAK;AACL,GAAG;AACH,EAAE,UAAU,CAAC,MAAM,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtB,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,EAAE;AAC9B,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACxC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;AACrC,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAClD,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACtC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;AACrC,QAAQ,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1C,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,UAAU,CAAC,MAAM,EAAE,QAAQ,GAAG,CAAC,EAAE;AACnC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;AACrD,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;AAClD,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAC7C,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACvC,MAAM,MAAM,KAAK,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAChD,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjD,MAAM,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;AAC1D,KAAK;AACL,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE,QAAQ,GAAG,CAAC,EAAE;AACpC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC3C,MAAM,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACrC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;AACnD,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;AAC9C,MAAM,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACxC,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,OAAO,EAAE;AAClB,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;AAC9C,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;AACvC,QAAQ,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;AAClC,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACnD,QAAQ,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/C,QAAQ,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAChC,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,QAAQ,EAAE;AAC9B,IAAI,IAAI,IAAI,CAAC,sBAAsB,KAAK,QAAQ,EAAE;AAClD,MAAM,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC;AAC7C,MAAM,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC;AACxD,IAAI,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC1D,IAAI,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9E,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9D,IAAI,IAAI,MAAM,CAAC,mBAAmB,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE;AACjH,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AACrE,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;AACzE,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;AAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAChD,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACzD,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACvD,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AAC7D,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,aAAa,CAAC,MAAM,EAAE;AACxB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE;AACrC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,MAAM,CAAC;AAC9D,IAAI,gBAAgB;AACpB,MAAM,MAAM,CAAC,KAAK;AAClB,MAAM,EAAE;AACR,MAAM,MAAM,CAAC,aAAa,GAAG,CAAC;AAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,oBAAoB;AAC5D,MAAM,eAAe;AACrB,MAAM,EAAE,CAAC,UAAU;AACnB;AACA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,YAAY;AAChF,MAAM,aAAa;AACnB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,SAAS;AAClB,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxC,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,MAAM,CAAC;AAC9D,IAAI,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,KAAK,6BAA6B,CAAC;AAClF,IAAI,IAAI,IAAI,CAAC,iBAAiB,KAAK,kBAAkB,EAAE;AACvD,MAAM,IAAI,CAAC,iBAAiB,GAAG,kBAAkB,CAAC;AAClD,MAAM,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,8BAA8B,EAAE,kBAAkB,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;AAC9C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9G,KAAK,MAAM;AACX,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AAC1H,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,EAAE;AAChE,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG;AACH,EAAE,eAAe,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,EAAE;AACvC,IAAI,IAAI,IAAI;AACZ,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACjC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,eAAe,CAAC,MAAM,EAAE;AAC1B,IAAI,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACtD,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACxD,IAAI,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;AAC/C,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;AACpD,IAAI,gBAAgB;AACpB,MAAM,KAAK;AACX,MAAM,EAAE;AACR,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,aAAa,GAAG,CAAC;AACxE,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,oBAAoB;AAC5D,MAAM,mBAAmB;AACzB,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,IAAI;AACV,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,aAAa,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAC/E,GAAG;AACH,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,cAAc,CAAC,OAAO,EAAE;AAC1B,IAAI,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC9D,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AACnD,IAAI,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACzB,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,MAAM,SAAS,GAAG,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC3D,MAAM,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACjC,MAAM,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,SAAS,CAAC,OAAO,EAAE;AACrB,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC;AACjD,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AAChC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE,IAAI,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,eAAe,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;AACpE,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACxE,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAClF,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC3B,IAAI,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,wBAAwB,CAAC,CAAC;AAChF,IAAI,EAAE,CAAC,UAAU;AACjB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,CAAC;AACtC,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,CAAC;AACtC,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,EAAE,CAAC,IAAI;AACb,MAAM,EAAE,CAAC,aAAa;AACtB,MAAM,MAAM;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,EAAE,MAAM,EAAE,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AAC3E,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AACnF,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS;AACjB,CAAC,CAAC;AACF,MAAM,iBAAiB,CAAC;AACxB,EAAE,IAAI,GAAG;AACT,IAAI,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC;AACtC,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC1E,MAAM,gBAAgB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACpE,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AACvC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,WAAW,GAAG,sBAAsB,EAAE,CAAC;AACjD,IAAI,MAAM,SAAS,GAAG,0BAA0B,CAAC;AACjD,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU;AAClB,QAAQ,yBAAyB,CAAC,WAAW,CAAC;AAC9C,QAAQ,iBAAiB;AACzB,QAAQ,gBAAgB;AACxB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;AAC7B,MAAM,SAAS;AACf,MAAM,SAAS,EAAE;AACjB,QAAQ,aAAa,EAAE,QAAQ;AAC/B,QAAQ,aAAa,EAAE,4BAA4B,CAAC,WAAW,CAAC;AAChE,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,YAAY,EAAE,UAAU,EAAE;AACpC,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACvC,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC;AACvD,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;AAC3C,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC;AACnD,IAAI,MAAM;AACV,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AACzD,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC3C,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;AAC/D,IAAI,MAAM,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC;AAC9C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE;AAC3D,MAAM,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE;AACtB,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;AACvD,UAAU,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/D,SAAS;AACT,QAAQ,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACxE,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH,CAAC;AACD,iBAAiB,CAAC,SAAS,GAAG;AAC9B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,iBAAiB;AACnC,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,MAAM,aAAa,CAAC;AACpB,EAAE,IAAI,GAAG;AACT,IAAI,MAAM,SAAS,GAAG,0BAA0B,CAAC;AACjD,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE;AACZ,QAAQ,iBAAiB;AACzB,QAAQ,YAAY;AACpB,QAAQ,gBAAgB;AACxB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC;AAC9B,MAAM,SAAS;AACf,MAAM,SAAS,EAAE;AACjB,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM;AACtC,QAAQ,eAAe,EAAE;AACzB,UAAU,cAAc,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;AACtE,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC1B,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;AACvC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AACpC,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC;AACzC,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;AAC/C,MAAM,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;AAChG,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAClC,MAAM,IAAI,CAAC,8BAA8B,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACxD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AAC3D,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,sBAAsB,CAAC;AACzD,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;AAC1B,MAAM,QAAQ,EAAE,IAAI,CAAC,SAAS;AAC9B,MAAM,MAAM;AACZ,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,GAAG;AACH,CAAC;AACD,aAAa,CAAC,SAAS,GAAG;AAC1B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,iBAAiB;AACnC,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG;AAC5B,EAAE,GAAG,aAAa;AAClB,EAAE,WAAW;AACb,EAAE,kBAAkB;AACpB,EAAE,eAAe;AACjB,EAAE,cAAc;AAChB,EAAE,eAAe;AACjB,EAAE,oBAAoB;AACtB,EAAE,gBAAgB;AAClB,EAAE,oBAAoB;AACtB,EAAE,cAAc;AAChB,EAAE,eAAe;AACjB,EAAE,aAAa;AACf,EAAE,eAAe;AACjB,EAAE,iBAAiB;AACnB,CAAC,CAAC;AACF,MAAM,iBAAiB,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC;AACjD,MAAM,oBAAoB,GAAG,CAAC,cAAc,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;AAChF,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAC9B,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AACjE,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AACpE,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;AAClF,UAAU,CAAC,GAAG,CAAC,GAAG,mBAAmB,EAAE,GAAG,iBAAiB,EAAE,GAAG,oBAAoB,CAAC,CAAC;AACtF,MAAM,aAAa,SAAS,gBAAgB,CAAC;AAC7C,EAAE,WAAW,GAAG;AAChB,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,IAAI,EAAE,YAAY,CAAC,KAAK;AAC9B,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,kBAAkB;AACxB,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AACxB,GAAG;AACH;;;;"}