#!/usr/bin/env python3
"""
移动model-00004文件到正确位置
"""

import os
import shutil

def move_file():
    """移动文件到正确位置"""
    source = "models/model-00004-of-00007.safetensors"
    target = "models/models--ByteDance-Seed--UI-TARS-1.5-7B/snapshots/683d002dd99d8f95104d31e70391a39348857f4e/model-00004-of-00007.safetensors"
    
    if os.path.exists(source):
        print(f"📁 源文件存在: {source}")
        print(f"📁 目标位置: {target}")
        
        try:
            shutil.move(source, target)
            print("✅ 文件移动成功！")
            
            # 验证文件是否存在
            if os.path.exists(target):
                file_size = os.path.getsize(target)
                print(f"📊 文件大小: {file_size / (1024**3):.2f} GB")
                print("✅ 文件验证成功！")
            else:
                print("❌ 文件移动后验证失败")
                
        except Exception as e:
            print(f"❌ 文件移动失败: {str(e)}")
    else:
        print(f"❌ 源文件不存在: {source}")

if __name__ == "__main__":
    move_file()

    # 验证所有文件是否完整
    model_dir = "models/models--ByteDance-Seed--UI-TARS-1.5-7B/snapshots/683d002dd99d8f95104d31e70391a39348857f4e"
    required_files = [
        "model-00001-of-00007.safetensors",
        "model-00002-of-00007.safetensors",
        "model-00003-of-00007.safetensors",
        "model-00004-of-00007.safetensors",
        "model-00005-of-00007.safetensors",
        "model-00006-of-00007.safetensors",
        "model-00007-of-00007.safetensors"
    ]

    print("\n📋 验证所有模型文件:")
    all_present = True
    for file in required_files:
        file_path = os.path.join(model_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024**3)
            print(f"✅ {file}: {size:.2f} GB")
        else:
            print(f"❌ {file}: 缺失")
            all_present = False

    if all_present:
        print("\n🎉 所有模型文件完整！可以开始测试模型了。")
    else:
        print("\n❌ 仍有文件缺失，需要重新下载。")
