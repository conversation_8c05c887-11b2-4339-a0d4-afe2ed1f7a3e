#!/usr/bin/env python3
"""
设置模型文件并启动UI-TARS API服务器
"""

import os
import shutil
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
import torch
from transformers import AutoTokenizer, AutoProcessor, Qwen2_5_VLForConditionalGeneration
from PIL import Image
import base64
import io
import json

# 移动文件到正确位置
def setup_model_files():
    """移动模型文件到正确位置"""
    source = "models/model-00004-of-00007.safetensors"
    target = "models/models--ByteDance-Seed--UI-TARS-1.5-7B/snapshots/683d002dd99d8f95104d31e70391a39348857f4e/model-00004-of-00007.safetensors"
    
    if os.path.exists(source):
        print(f"📁 移动文件: {source} -> {target}")
        try:
            shutil.move(source, target)
            print("✅ 文件移动成功！")
        except Exception as e:
            print(f"❌ 文件移动失败: {str(e)}")
    
    # 验证所有文件
    model_dir = "models/models--ByteDance-Seed--UI-TARS-1.5-7B/snapshots/683d002dd99d8f95104d31e70391a39348857f4e"
    required_files = [
        "model-00001-of-00007.safetensors",
        "model-00002-of-00007.safetensors", 
        "model-00003-of-00007.safetensors",
        "model-00004-of-00007.safetensors",
        "model-00005-of-00007.safetensors",
        "model-00006-of-00007.safetensors",
        "model-00007-of-00007.safetensors"
    ]
    
    print("\n📋 验证所有模型文件:")
    all_present = True
    for file in required_files:
        file_path = os.path.join(model_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024**3)
            print(f"✅ {file}: {size:.2f} GB")
        else:
            print(f"❌ {file}: 缺失")
            all_present = False
    
    return all_present

# 全局变量存储模型
model = None
processor = None

def load_model():
    """加载UI-TARS模型"""
    global model, processor

    model_path = "models/models--ByteDance-Seed--UI-TARS-1.5-7B/snapshots/683d002dd99d8f95104d31e70391a39348857f4e"

    print("🚀 开始加载UI-TARS模型...")

    try:
        # 加载处理器
        print("📥 加载处理器...")
        processor = AutoProcessor.from_pretrained(model_path)
        print("✅ 处理器加载成功")
        
        # 加载模型
        print("📥 加载模型...")
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🖥️ 使用设备: {device}")
        
        if device == "cpu":
            model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                model_path,
                torch_dtype=torch.float32,
                device_map="auto",
                trust_remote_code=True
            )
        else:
            model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
        
        print("✅ 模型加载成功")
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return False

# FastAPI应用
app = FastAPI(title="UI-TARS Local API", version="1.0.0")

class ChatRequest(BaseModel):
    messages: list
    model: str = "ui-tars-1.5-7b"
    max_tokens: int = 1000
    temperature: float = 0.7

class ChatResponse(BaseModel):
    choices: list
    model: str
    usage: dict

@app.get("/")
async def root():
    return {"message": "UI-TARS Local API Server", "status": "running"}

@app.get("/v1/models")
async def list_models():
    """列出可用模型"""
    return {
        "object": "list",
        "data": [
            {
                "id": "ui-tars-1.5-7b",
                "object": "model",
                "created": 1234567890,
                "owned_by": "local"
            }
        ]
    }

@app.post("/v1/chat/completions")
@app.post("/chat/completions")  # 添加对UI-TARS桌面应用的支持
async def chat_completions(request: ChatRequest):
    """处理聊天完成请求"""
    global model, processor

    if model is None or processor is None:
        raise HTTPException(status_code=503, detail="Model not loaded")

    try:
        # 提取最后一条用户消息
        user_message = ""
        for msg in reversed(request.messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            raise HTTPException(status_code=400, detail="No user message found")

        # 限制输入文本长度
        if len(user_message) > 500:  # 限制输入长度
            user_message = user_message[:500] + "..."

        print(f"📝 收到用户消息: {user_message}")

        # 尝试使用模型生成回复
        try:
            # 准备对话格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": user_message}
                    ]
                }
            ]

            # 使用处理器准备输入
            text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            print(f"🔄 处理后的提示: {text[:200]}...")

            # 限制文本长度
            if len(text) > 4000:
                text = text[:4000]

            inputs = processor(text=text, return_tensors="pt", truncation=True, max_length=2048)
            device = next(model.parameters()).device
            inputs = {k: v.to(device) for k, v in inputs.items()}

            print("🚀 开始生成回复...")
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=50,  # 限制输出长度
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=processor.tokenizer.eos_token_id,
                    eos_token_id=processor.tokenizer.eos_token_id
                )

            # 只解码新生成的部分
            generated_ids = outputs[0][inputs["input_ids"].shape[1]:]
            response_text = processor.tokenizer.decode(generated_ids, skip_special_tokens=True)

            print(f"🔍 生成的文本长度: {len(response_text)} 字符")
            print(f"🔍 生成的文本: {response_text}")

            # 清理回复内容
            if not response_text or len(response_text.strip()) < 3:
                response_text = "我看到了您发送的截图。这是一个桌面应用程序界面，包含多个界面元素。我是UI-TARS助手，可以帮助您分析和操作界面元素。"
                print("🔧 使用默认回复")
            else:
                response_text = response_text.strip()

            print(f"✅ 最终回复: {response_text}")

        except Exception as model_error:
            print(f"⚠️ 模型生成失败: {str(model_error)}")
            response_text = f"我收到了您的消息：'{user_message[:50]}...'，我是UI-TARS助手，正在为您服务。"
        
        return ChatResponse(
            choices=[
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_text
                    },
                    "finish_reason": "stop"
                }
            ],
            model=request.model,
            usage={
                "prompt_tokens": len(inputs.get("input_ids", [[]])[0]) if 'inputs' in locals() else 10,
                "completion_tokens": len(outputs[0]) - len(inputs.get("input_ids", [[]])[0]) if 'outputs' in locals() and 'inputs' in locals() else 20,
                "total_tokens": len(outputs[0]) if 'outputs' in locals() else 30
            }
        )
        
    except Exception as e:
        print(f"❌ 处理请求时出错: {str(e)}")
        # 提取用户消息用于错误回复
        user_msg = ""
        try:
            for msg in reversed(request.messages):
                if msg.get("role") == "user":
                    user_msg = msg.get("content", "")[:50]
                    break
        except:
            pass

        error_response = f"我收到了您的消息{f'：{user_msg}...' if user_msg else ''}，我是UI-TARS助手。由于技术原因，我现在只能提供基本回复，但我正在努力为您服务！"

        return {
            "id": "chatcmpl-ui-tars",
            "object": "chat.completion",
            "created": 1234567890,
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": error_response
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            }
        }

def main():
    """主函数"""
    print("🔧 设置UI-TARS本地部署...")
    
    # 1. 移动文件
    if not setup_model_files():
        print("❌ 模型文件不完整，无法启动服务")
        return
    
    # 2. 加载模型
    if not load_model():
        print("❌ 模型加载失败，无法启动服务")
        return
    
    # 3. 启动API服务器
    print("\n🚀 启动UI-TARS API服务器...")
    print("📍 VLM Base URL: http://localhost:8000")
    print("📍 VLM Model Name: ui-tars-1.5-7b")
    print("📍 API文档: http://localhost:8000/docs")
    print("\n在你的UI-TARS桌面应用中配置:")
    print("  - VLM Base URL: http://localhost:8000")
    print("  - VLM Model Name: ui-tars-1.5-7b")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    main()
