#!/usr/bin/env python3
"""
UI-TARS 1.5-7B Web界面部署
提供简单易用的Web界面来与模型交互
"""

import gradio as gr
import torch
from transformers import AutoTokenizer, AutoModel, AutoProcessor
from PIL import Image
import os
import tempfile
import logging
from typing import Optional, Tuple
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UITarsWebInterface:
    def __init__(self, model_path: str = "ByteDance-Seed/UI-TARS-1.5-7B", device: str = "auto"):
        """
        初始化UI-TARS Web界面
        
        Args:
            model_path: 模型路径
            device: 设备类型
        """
        self.model_path = model_path
        self.device = self._get_device(device)
        self.model = None
        self.tokenizer = None
        self.processor = None
        self.temp_dir = tempfile.mkdtemp()
        
    def _get_device(self, device: str) -> str:
        """获取可用设备"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        return device
    
    def load_model(self):
        """加载模型"""
        logger.info(f"正在加载模型: {self.model_path}")
        logger.info(f"使用设备: {self.device}")
        
        try:
            # 加载tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # 加载processor
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # 加载模型
            model_kwargs = {
                "trust_remote_code": True,
                "low_cpu_mem_usage": True
            }
            
            if self.device == "cuda":
                model_kwargs.update({
                    "torch_dtype": torch.float16,
                    "device_map": "auto"
                })
            else:
                model_kwargs["torch_dtype"] = torch.float32
            
            self.model = AutoModel.from_pretrained(
                self.model_path,
                **model_kwargs
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
                
            logger.info("模型加载完成!")
            return "✅ 模型加载成功!"
            
        except Exception as e:
            error_msg = f"❌ 模型加载失败: {e}"
            logger.error(error_msg)
            return error_msg
    
    def chat_with_ui(self, 
                     message: str, 
                     image: Optional[Image.Image] = None,
                     history: list = None,
                     max_tokens: int = 512,
                     temperature: float = 0.7) -> Tuple[str, list]:
        """
        与UI-TARS聊天
        
        Args:
            message: 用户消息
            image: 上传的图像
            history: 聊天历史
            max_tokens: 最大token数
            temperature: 温度参数
            
        Returns:
            (响应, 更新的历史)
        """
        if history is None:
            history = []
            
        if self.model is None:
            response = "❌ 模型未加载，请先点击'加载模型'按钮"
            history.append((message, response))
            return "", history
        
        try:
            # 处理输入
            if image is not None:
                # 保存临时图像
                temp_image_path = os.path.join(self.temp_dir, "temp_image.png")
                image.save(temp_image_path)
                
                inputs = self.processor(
                    text=message,
                    images=image,
                    return_tensors="pt"
                ).to(self.device)
                
                display_message = f"{message} [包含图像]"
            else:
                inputs = self.tokenizer(
                    message,
                    return_tensors="pt"
                ).to(self.device)
                
                display_message = message
            
            # 生成响应
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码响应
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            ).strip()
            
            # 更新历史
            history.append((display_message, response))
            
            return "", history
            
        except Exception as e:
            error_response = f"❌ 生成响应时出错: {e}"
            history.append((message, error_response))
            return "", history

def create_interface():
    """创建Gradio界面"""
    
    # 初始化UI-TARS
    ui_tars = UITarsWebInterface()
    
    # 创建界面
    with gr.Blocks(title="UI-TARS 1.5-7B 本地部署", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🤖 UI-TARS 1.5-7B 本地部署
        
        欢迎使用UI-TARS 1.5-7B本地部署版本！这是一个专门用于UI界面理解和交互的多模态AI助手。
        
        ## 使用说明：
        1. 首先点击"加载模型"按钮
        2. 输入您的问题或指令
        3. 可选：上传UI截图进行分析
        4. 点击发送开始对话
        """)
        
        # 模型加载区域
        with gr.Row():
            load_btn = gr.Button("🔄 加载模型", variant="primary")
            status_text = gr.Textbox(label="状态", value="等待加载模型...", interactive=False)
        
        # 聊天区域
        with gr.Row():
            with gr.Column(scale=2):
                chatbot = gr.Chatbot(
                    label="对话历史",
                    height=400,
                    show_copy_button=True
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题或指令...",
                        lines=2,
                        scale=4
                    )
                    send_btn = gr.Button("📤 发送", variant="primary", scale=1)
                
                clear_btn = gr.Button("🗑️ 清空对话", variant="secondary")
            
            with gr.Column(scale=1):
                image_input = gr.Image(
                    label="上传UI截图（可选）",
                    type="pil",
                    height=300
                )
                
                # 参数设置
                with gr.Accordion("⚙️ 高级设置", open=False):
                    max_tokens = gr.Slider(
                        label="最大生成长度",
                        minimum=50,
                        maximum=1024,
                        value=512,
                        step=50
                    )
                    temperature = gr.Slider(
                        label="创造性 (Temperature)",
                        minimum=0.1,
                        maximum=2.0,
                        value=0.7,
                        step=0.1
                    )
        
        # 示例区域
        with gr.Accordion("💡 示例问题", open=False):
            gr.Examples(
                examples=[
                    ["请分析这个界面的布局结构"],
                    ["这个按钮的位置在哪里？"],
                    ["如何改进这个界面的用户体验？"],
                    ["请描述这个页面的主要功能"],
                    ["这个界面有什么可访问性问题吗？"]
                ],
                inputs=msg_input
            )
        
        # 事件绑定
        load_btn.click(
            fn=ui_tars.load_model,
            outputs=status_text
        )
        
        send_btn.click(
            fn=ui_tars.chat_with_ui,
            inputs=[msg_input, image_input, chatbot, max_tokens, temperature],
            outputs=[msg_input, chatbot]
        )
        
        msg_input.submit(
            fn=ui_tars.chat_with_ui,
            inputs=[msg_input, image_input, chatbot, max_tokens, temperature],
            outputs=[msg_input, chatbot]
        )
        
        clear_btn.click(
            fn=lambda: ([], None),
            outputs=[chatbot, image_input]
        )
    
    return demo

def main():
    parser = argparse.ArgumentParser(description="UI-TARS Web界面")
    parser.add_argument("--host", default="127.0.0.1", help="服务主机")
    parser.add_argument("--port", type=int, default=7860, help="服务端口")
    parser.add_argument("--share", action="store_true", help="创建公共链接")
    
    args = parser.parse_args()
    
    # 创建并启动界面
    demo = create_interface()
    
    print(f"🚀 启动UI-TARS Web界面...")
    print(f"📍 访问地址: http://{args.host}:{args.port}")
    
    demo.launch(
        server_name=args.host,
        server_port=args.port,
        share=args.share,
        show_error=True
    )

if __name__ == "__main__":
    main()
