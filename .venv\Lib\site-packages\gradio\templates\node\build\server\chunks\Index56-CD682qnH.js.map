{"version": 3, "file": "Index56-CD682qnH.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index56.js"], "sourcesContent": ["import { create_ssr_component, validate_component, add_attribute, missing_component } from \"svelte/internal\";\nimport { B as BlockLabel, H as File, I as IconButtonWrapper, h as IconButton, _ as Undo, D as Download, n as Block, S as Static, k as Empty, U as UploadText } from \"./client.js\";\nimport { createEventDispatcher } from \"svelte\";\nimport { U as Upload, M as ModifyUpload } from \"./ModifyUpload.js\";\nimport { default as default2 } from \"./Example19.js\";\nvar has = Object.prototype.hasOwnProperty;\nfunction find(iter, tar, key) {\n  for (key of iter.keys()) {\n    if (dequal(key, tar))\n      return key;\n  }\n}\nfunction dequal(foo, bar) {\n  var ctor, len, tmp;\n  if (foo === bar)\n    return true;\n  if (foo && bar && (ctor = foo.constructor) === bar.constructor) {\n    if (ctor === Date)\n      return foo.getTime() === bar.getTime();\n    if (ctor === RegExp)\n      return foo.toString() === bar.toString();\n    if (ctor === Array) {\n      if ((len = foo.length) === bar.length) {\n        while (len-- && dequal(foo[len], bar[len]))\n          ;\n      }\n      return len === -1;\n    }\n    if (ctor === Set) {\n      if (foo.size !== bar.size) {\n        return false;\n      }\n      for (len of foo) {\n        tmp = len;\n        if (tmp && typeof tmp === \"object\") {\n          tmp = find(bar, tmp);\n          if (!tmp)\n            return false;\n        }\n        if (!bar.has(tmp))\n          return false;\n      }\n      return true;\n    }\n    if (ctor === Map) {\n      if (foo.size !== bar.size) {\n        return false;\n      }\n      for (len of foo) {\n        tmp = len[0];\n        if (tmp && typeof tmp === \"object\") {\n          tmp = find(bar, tmp);\n          if (!tmp)\n            return false;\n        }\n        if (!dequal(len[1], bar.get(tmp))) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (ctor === ArrayBuffer) {\n      foo = new Uint8Array(foo);\n      bar = new Uint8Array(bar);\n    } else if (ctor === DataView) {\n      if ((len = foo.byteLength) === bar.byteLength) {\n        while (len-- && foo.getInt8(len) === bar.getInt8(len))\n          ;\n      }\n      return len === -1;\n    }\n    if (ArrayBuffer.isView(foo)) {\n      if ((len = foo.byteLength) === bar.byteLength) {\n        while (len-- && foo[len] === bar[len])\n          ;\n      }\n      return len === -1;\n    }\n    if (!ctor || typeof foo === \"object\") {\n      len = 0;\n      for (ctor in foo) {\n        if (has.call(foo, ctor) && ++len && !has.call(bar, ctor))\n          return false;\n        if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor]))\n          return false;\n      }\n      return Object.keys(bar).length === len;\n    }\n  }\n  return foo !== foo && bar !== bar;\n}\nconst css$1 = {\n  code: \".model3D.svelte-1mxwah3{display:flex;position:relative;width:var(--size-full);height:var(--size-full);border-radius:var(--block-radius);overflow:hidden}.model3D.svelte-1mxwah3 canvas{width:var(--size-full);height:var(--size-full);object-fit:contain;overflow:hidden}\",\n  map: `{\"version\":3,\"file\":\"Model3D.svelte\",\"sources\":[\"Model3D.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { BlockLabel, IconButton, IconButtonWrapper } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { File, Download, Undo } from \\\\\"@gradio/icons\\\\\";\\\\nimport { dequal } from \\\\\"dequal\\\\\";\\\\nexport let value;\\\\nexport let display_mode = \\\\\"solid\\\\\";\\\\nexport let clear_color = [0, 0, 0, 0];\\\\nexport let label = \\\\\"\\\\\";\\\\nexport let show_label;\\\\nexport let i18n;\\\\nexport let zoom_speed = 1;\\\\nexport let pan_speed = 1;\\\\nexport let camera_position = [\\\\n    null,\\\\n    null,\\\\n    null\\\\n];\\\\nexport let has_change_history = false;\\\\nlet current_settings = { camera_position, zoom_speed, pan_speed };\\\\nlet use_3dgs = false;\\\\nlet Canvas3DGSComponent;\\\\nlet Canvas3DComponent;\\\\nasync function loadCanvas3D() {\\\\n    const module = await import(\\\\\"./Canvas3D.svelte\\\\\");\\\\n    return module.default;\\\\n}\\\\nasync function loadCanvas3DGS() {\\\\n    const module = await import(\\\\\"./Canvas3DGS.svelte\\\\\");\\\\n    return module.default;\\\\n}\\\\n$: if (value) {\\\\n    use_3dgs = value.path.endsWith(\\\\\".splat\\\\\") || value.path.endsWith(\\\\\".ply\\\\\");\\\\n    if (use_3dgs) {\\\\n        loadCanvas3DGS().then((component) => {\\\\n            Canvas3DGSComponent = component;\\\\n        });\\\\n    }\\\\n    else {\\\\n        loadCanvas3D().then((component) => {\\\\n            Canvas3DComponent = component;\\\\n        });\\\\n    }\\\\n}\\\\nlet canvas3d;\\\\nfunction handle_undo() {\\\\n    canvas3d?.reset_camera_position();\\\\n}\\\\n$: {\\\\n    if (!dequal(current_settings.camera_position, camera_position) || current_settings.zoom_speed !== zoom_speed || current_settings.pan_speed !== pan_speed) {\\\\n        canvas3d?.update_camera(camera_position, zoom_speed, pan_speed);\\\\n        current_settings = { camera_position, zoom_speed, pan_speed };\\\\n    }\\\\n}\\\\nlet resolved_url;\\\\n<\\/script>\\\\n\\\\n<BlockLabel\\\\n\\\\t{show_label}\\\\n\\\\tIcon={File}\\\\n\\\\tlabel={label || i18n(\\\\\"3D_model.3d_model\\\\\")}\\\\n/>\\\\n{#if value}\\\\n\\\\t<div class=\\\\\"model3D\\\\\">\\\\n\\\\t\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t\\\\t{#if !use_3dgs}\\\\n\\\\t\\\\t\\\\t\\\\t<!-- Canvas3DGS doesn't implement the undo method (reset_camera_position) -->\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Undo}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tlabel=\\\\\"Undo\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_undo()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={!has_change_history}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t<a\\\\n\\\\t\\\\t\\\\t\\\\thref={resolved_url}\\\\n\\\\t\\\\t\\\\t\\\\ttarget={window.__is_colab__ ? \\\\\"_blank\\\\\" : null}\\\\n\\\\t\\\\t\\\\t\\\\tdownload={window.__is_colab__ ? null : value.orig_name || value.path}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton Icon={Download} label={i18n(\\\\\"common.download\\\\\")} />\\\\n\\\\t\\\\t\\\\t</a>\\\\n\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\n\\\\t\\\\t{#if use_3dgs}\\\\n\\\\t\\\\t\\\\t<svelte:component\\\\n\\\\t\\\\t\\\\t\\\\tthis={Canvas3DGSComponent}\\\\n\\\\t\\\\t\\\\t\\\\tbind:resolved_url\\\\n\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t{zoom_speed}\\\\n\\\\t\\\\t\\\\t\\\\t{pan_speed}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t<svelte:component\\\\n\\\\t\\\\t\\\\t\\\\tthis={Canvas3DComponent}\\\\n\\\\t\\\\t\\\\t\\\\tbind:this={canvas3d}\\\\n\\\\t\\\\t\\\\t\\\\tbind:resolved_url\\\\n\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t{display_mode}\\\\n\\\\t\\\\t\\\\t\\\\t{clear_color}\\\\n\\\\t\\\\t\\\\t\\\\t{camera_position}\\\\n\\\\t\\\\t\\\\t\\\\t{zoom_speed}\\\\n\\\\t\\\\t\\\\t\\\\t{pan_speed}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.model3D {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tborder-radius: var(--block-radius);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\t.model3D :global(canvas) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA0GC,uBAAS,CACR,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,QAAQ,CAAE,MACX,CACA,uBAAQ,CAAS,MAAQ,CACxB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OAAO,CACnB,QAAQ,CAAE,MACX\"}`\n};\nasync function loadCanvas3D$1() {\n  const module = await import(\"./Canvas3D.js\");\n  return module.default;\n}\nasync function loadCanvas3DGS$1() {\n  const module = await import(\"./Canvas3DGS.js\");\n  return module.default;\n}\nconst Model3D = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { display_mode = \"solid\" } = $$props;\n  let { clear_color = [0, 0, 0, 0] } = $$props;\n  let { label = \"\" } = $$props;\n  let { show_label } = $$props;\n  let { i18n } = $$props;\n  let { zoom_speed = 1 } = $$props;\n  let { pan_speed = 1 } = $$props;\n  let { camera_position = [null, null, null] } = $$props;\n  let { has_change_history = false } = $$props;\n  let current_settings = { camera_position, zoom_speed, pan_speed };\n  let use_3dgs = false;\n  let Canvas3DGSComponent;\n  let Canvas3DComponent;\n  let canvas3d;\n  let resolved_url;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.display_mode === void 0 && $$bindings.display_mode && display_mode !== void 0)\n    $$bindings.display_mode(display_mode);\n  if ($$props.clear_color === void 0 && $$bindings.clear_color && clear_color !== void 0)\n    $$bindings.clear_color(clear_color);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.zoom_speed === void 0 && $$bindings.zoom_speed && zoom_speed !== void 0)\n    $$bindings.zoom_speed(zoom_speed);\n  if ($$props.pan_speed === void 0 && $$bindings.pan_speed && pan_speed !== void 0)\n    $$bindings.pan_speed(pan_speed);\n  if ($$props.camera_position === void 0 && $$bindings.camera_position && camera_position !== void 0)\n    $$bindings.camera_position(camera_position);\n  if ($$props.has_change_history === void 0 && $$bindings.has_change_history && has_change_history !== void 0)\n    $$bindings.has_change_history(has_change_history);\n  $$result.css.add(css$1);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      if (value) {\n        use_3dgs = value.path.endsWith(\".splat\") || value.path.endsWith(\".ply\");\n        if (use_3dgs) {\n          loadCanvas3DGS$1().then((component) => {\n            Canvas3DGSComponent = component;\n          });\n        } else {\n          loadCanvas3D$1().then((component) => {\n            Canvas3DComponent = component;\n          });\n        }\n      }\n    }\n    {\n      {\n        if (!dequal(current_settings.camera_position, camera_position) || current_settings.zoom_speed !== zoom_speed || current_settings.pan_speed !== pan_speed) {\n          canvas3d?.update_camera(camera_position, zoom_speed, pan_speed);\n          current_settings = { camera_position, zoom_speed, pan_speed };\n        }\n      }\n    }\n    $$rendered = `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n      $$result,\n      {\n        show_label,\n        Icon: File,\n        label: label || i18n(\"3D_model.3d_model\")\n      },\n      {},\n      {}\n    )} ${value ? `<div class=\"model3D svelte-1mxwah3\">${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, {}, {}, {\n      default: () => {\n        return `${!use_3dgs ? ` ${validate_component(IconButton, \"IconButton\").$$render(\n          $$result,\n          {\n            Icon: Undo,\n            label: \"Undo\",\n            disabled: !has_change_history\n          },\n          {},\n          {}\n        )}` : ``} <a${add_attribute(\"href\", resolved_url, 0)}${add_attribute(\"target\", window.__is_colab__ ? \"_blank\" : null, 0)}${add_attribute(\n          \"download\",\n          window.__is_colab__ ? null : value.orig_name || value.path,\n          0\n        )}>${validate_component(IconButton, \"IconButton\").$$render(\n          $$result,\n          {\n            Icon: Download,\n            label: i18n(\"common.download\")\n          },\n          {},\n          {}\n        )}</a>`;\n      }\n    })} ${use_3dgs ? `${validate_component(Canvas3DGSComponent || missing_component, \"svelte:component\").$$render(\n      $$result,\n      {\n        value,\n        zoom_speed,\n        pan_speed,\n        resolved_url\n      },\n      {\n        resolved_url: ($$value) => {\n          resolved_url = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}` : `${validate_component(Canvas3DComponent || missing_component, \"svelte:component\").$$render(\n      $$result,\n      {\n        value,\n        display_mode,\n        clear_color,\n        camera_position,\n        zoom_speed,\n        pan_speed,\n        this: canvas3d,\n        resolved_url\n      },\n      {\n        this: ($$value) => {\n          canvas3d = $$value;\n          $$settled = false;\n        },\n        resolved_url: ($$value) => {\n          resolved_url = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}`}</div>` : ``}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst Model3D$1 = Model3D;\nconst css = {\n  code: \".input-model.svelte-jub4pj{display:flex;position:relative;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full);border-radius:var(--block-radius);overflow:hidden}.input-model.svelte-jub4pj canvas{width:var(--size-full);height:var(--size-full);object-fit:contain;overflow:hidden}\",\n  map: '{\"version\":3,\"file\":\"Model3DUpload.svelte\",\"sources\":[\"Model3DUpload.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, tick } from \\\\\"svelte\\\\\";\\\\nimport { Upload, ModifyUpload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { BlockLabel } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { File } from \\\\\"@gradio/icons\\\\\";\\\\nexport let value;\\\\nexport let display_mode = \\\\\"solid\\\\\";\\\\nexport let clear_color = [0, 0, 0, 0];\\\\nexport let label = \\\\\"\\\\\";\\\\nexport let show_label;\\\\nexport let root;\\\\nexport let i18n;\\\\nexport let zoom_speed = 1;\\\\nexport let pan_speed = 1;\\\\nexport let max_file_size = null;\\\\nexport let uploading = false;\\\\nexport let camera_position = [\\\\n    null,\\\\n    null,\\\\n    null\\\\n];\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nasync function handle_upload({ detail }) {\\\\n    value = detail;\\\\n    await tick();\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    dispatch(\\\\\"load\\\\\", value);\\\\n}\\\\nasync function handle_clear() {\\\\n    value = null;\\\\n    await tick();\\\\n    dispatch(\\\\\"clear\\\\\");\\\\n    dispatch(\\\\\"change\\\\\");\\\\n}\\\\nlet use_3dgs = false;\\\\nlet Canvas3DGSComponent;\\\\nlet Canvas3DComponent;\\\\nasync function loadCanvas3D() {\\\\n    const module = await import(\\\\\"./Canvas3D.svelte\\\\\");\\\\n    return module.default;\\\\n}\\\\nasync function loadCanvas3DGS() {\\\\n    const module = await import(\\\\\"./Canvas3DGS.svelte\\\\\");\\\\n    return module.default;\\\\n}\\\\n$: if (value) {\\\\n    use_3dgs = value.path.endsWith(\\\\\".splat\\\\\") || value.path.endsWith(\\\\\".ply\\\\\");\\\\n    if (use_3dgs) {\\\\n        loadCanvas3DGS().then((component) => {\\\\n            Canvas3DGSComponent = component;\\\\n        });\\\\n    }\\\\n    else {\\\\n        loadCanvas3D().then((component) => {\\\\n            Canvas3DComponent = component;\\\\n        });\\\\n    }\\\\n}\\\\nlet canvas3d;\\\\nasync function handle_undo() {\\\\n    canvas3d?.reset_camera_position();\\\\n}\\\\nconst dispatch = createEventDispatcher();\\\\nlet dragging = false;\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\n<\\/script>\\\\n\\\\n<BlockLabel {show_label} Icon={File} label={label || \\\\\"3D Model\\\\\"} />\\\\n\\\\n{#if value === null}\\\\n\\\\t<Upload\\\\n\\\\t\\\\t{upload}\\\\n\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\ton:load={handle_upload}\\\\n\\\\t\\\\t{root}\\\\n\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\tfiletype={[\\\\\".stl\\\\\", \\\\\".obj\\\\\", \\\\\".gltf\\\\\", \\\\\".glb\\\\\", \\\\\"model/obj\\\\\", \\\\\".splat\\\\\", \\\\\".ply\\\\\"]}\\\\n\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\ton:error\\\\n\\\\t\\\\taria_label={i18n(\\\\\"model3d.drop_to_upload\\\\\")}\\\\n\\\\t>\\\\n\\\\t\\\\t<slot />\\\\n\\\\t</Upload>\\\\n{:else}\\\\n\\\\t<div class=\\\\\"input-model\\\\\">\\\\n\\\\t\\\\t<ModifyUpload\\\\n\\\\t\\\\t\\\\tundoable={!use_3dgs}\\\\n\\\\t\\\\t\\\\ton:clear={handle_clear}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\ton:undo={handle_undo}\\\\n\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t{#if use_3dgs}\\\\n\\\\t\\\\t\\\\t<svelte:component\\\\n\\\\t\\\\t\\\\t\\\\tthis={Canvas3DGSComponent}\\\\n\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t{zoom_speed}\\\\n\\\\t\\\\t\\\\t\\\\t{pan_speed}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t<svelte:component\\\\n\\\\t\\\\t\\\\t\\\\tthis={Canvas3DComponent}\\\\n\\\\t\\\\t\\\\t\\\\tbind:this={canvas3d}\\\\n\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t{display_mode}\\\\n\\\\t\\\\t\\\\t\\\\t{clear_color}\\\\n\\\\t\\\\t\\\\t\\\\t{camera_position}\\\\n\\\\t\\\\t\\\\t\\\\t{zoom_speed}\\\\n\\\\t\\\\t\\\\t\\\\t{pan_speed}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.input-model {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tborder-radius: var(--block-radius);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.input-model :global(canvas) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAoHC,0BAAa,CACZ,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,QAAQ,CAAE,MACX,CAEA,0BAAY,CAAS,MAAQ,CAC5B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OAAO,CACnB,QAAQ,CAAE,MACX\"}'\n};\nasync function loadCanvas3D() {\n  const module = await import(\"./Canvas3D.js\");\n  return module.default;\n}\nasync function loadCanvas3DGS() {\n  const module = await import(\"./Canvas3DGS.js\");\n  return module.default;\n}\nconst Model3DUpload = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { display_mode = \"solid\" } = $$props;\n  let { clear_color = [0, 0, 0, 0] } = $$props;\n  let { label = \"\" } = $$props;\n  let { show_label } = $$props;\n  let { root } = $$props;\n  let { i18n } = $$props;\n  let { zoom_speed = 1 } = $$props;\n  let { pan_speed = 1 } = $$props;\n  let { max_file_size = null } = $$props;\n  let { uploading = false } = $$props;\n  let { camera_position = [null, null, null] } = $$props;\n  let { upload } = $$props;\n  let { stream_handler } = $$props;\n  let use_3dgs = false;\n  let Canvas3DGSComponent;\n  let Canvas3DComponent;\n  let canvas3d;\n  const dispatch = createEventDispatcher();\n  let dragging = false;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.display_mode === void 0 && $$bindings.display_mode && display_mode !== void 0)\n    $$bindings.display_mode(display_mode);\n  if ($$props.clear_color === void 0 && $$bindings.clear_color && clear_color !== void 0)\n    $$bindings.clear_color(clear_color);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.zoom_speed === void 0 && $$bindings.zoom_speed && zoom_speed !== void 0)\n    $$bindings.zoom_speed(zoom_speed);\n  if ($$props.pan_speed === void 0 && $$bindings.pan_speed && pan_speed !== void 0)\n    $$bindings.pan_speed(pan_speed);\n  if ($$props.max_file_size === void 0 && $$bindings.max_file_size && max_file_size !== void 0)\n    $$bindings.max_file_size(max_file_size);\n  if ($$props.uploading === void 0 && $$bindings.uploading && uploading !== void 0)\n    $$bindings.uploading(uploading);\n  if ($$props.camera_position === void 0 && $$bindings.camera_position && camera_position !== void 0)\n    $$bindings.camera_position(camera_position);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      if (value) {\n        use_3dgs = value.path.endsWith(\".splat\") || value.path.endsWith(\".ply\");\n        if (use_3dgs) {\n          loadCanvas3DGS().then((component) => {\n            Canvas3DGSComponent = component;\n          });\n        } else {\n          loadCanvas3D().then((component) => {\n            Canvas3DComponent = component;\n          });\n        }\n      }\n    }\n    {\n      dispatch(\"drag\", dragging);\n    }\n    $$rendered = `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n      $$result,\n      {\n        show_label,\n        Icon: File,\n        label: label || \"3D Model\"\n      },\n      {},\n      {}\n    )} ${value === null ? `${validate_component(Upload, \"Upload\").$$render(\n      $$result,\n      {\n        upload,\n        stream_handler,\n        root,\n        max_file_size,\n        filetype: [\".stl\", \".obj\", \".gltf\", \".glb\", \"model/obj\", \".splat\", \".ply\"],\n        aria_label: i18n(\"model3d.drop_to_upload\"),\n        dragging,\n        uploading\n      },\n      {\n        dragging: ($$value) => {\n          dragging = $$value;\n          $$settled = false;\n        },\n        uploading: ($$value) => {\n          uploading = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${slots.default ? slots.default({}) : ``}`;\n        }\n      }\n    )}` : `<div class=\"input-model svelte-jub4pj\">${validate_component(ModifyUpload, \"ModifyUpload\").$$render($$result, { undoable: !use_3dgs, i18n }, {}, {})} ${use_3dgs ? `${validate_component(Canvas3DGSComponent || missing_component, \"svelte:component\").$$render($$result, { value, zoom_speed, pan_speed }, {}, {})}` : `${validate_component(Canvas3DComponent || missing_component, \"svelte:component\").$$render(\n      $$result,\n      {\n        value,\n        display_mode,\n        clear_color,\n        camera_position,\n        zoom_speed,\n        pan_speed,\n        this: canvas3d\n      },\n      {\n        this: ($$value) => {\n          canvas3d = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}`}</div>`}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst Model3DUpload$1 = Model3DUpload;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = null } = $$props;\n  let { root } = $$props;\n  let { display_mode = \"solid\" } = $$props;\n  let { clear_color } = $$props;\n  let { loading_status } = $$props;\n  let { label } = $$props;\n  let { show_label } = $$props;\n  let { container = true } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { gradio } = $$props;\n  let { height = void 0 } = $$props;\n  let { zoom_speed = 1 } = $$props;\n  let { input_ready } = $$props;\n  let uploading = false;\n  let { has_change_history = false } = $$props;\n  let { camera_position = [null, null, null] } = $$props;\n  let { interactive } = $$props;\n  const is_browser = typeof window !== \"undefined\";\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.display_mode === void 0 && $$bindings.display_mode && display_mode !== void 0)\n    $$bindings.display_mode(display_mode);\n  if ($$props.clear_color === void 0 && $$bindings.clear_color && clear_color !== void 0)\n    $$bindings.clear_color(clear_color);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.container === void 0 && $$bindings.container && container !== void 0)\n    $$bindings.container(container);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.zoom_speed === void 0 && $$bindings.zoom_speed && zoom_speed !== void 0)\n    $$bindings.zoom_speed(zoom_speed);\n  if ($$props.input_ready === void 0 && $$bindings.input_ready && input_ready !== void 0)\n    $$bindings.input_ready(input_ready);\n  if ($$props.has_change_history === void 0 && $$bindings.has_change_history && has_change_history !== void 0)\n    $$bindings.has_change_history(has_change_history);\n  if ($$props.camera_position === void 0 && $$bindings.camera_position && camera_position !== void 0)\n    $$bindings.camera_position(camera_position);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    input_ready = !uploading;\n    $$rendered = `${!interactive ? `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        variant: value === null ? \"dashed\" : \"solid\",\n        border_mode: \"base\",\n        padding: false,\n        elem_id,\n        elem_classes,\n        container,\n        scale,\n        min_width,\n        height\n      },\n      {},\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${value && is_browser ? `${validate_component(Model3D$1, \"Model3D\").$$render(\n            $$result,\n            {\n              value,\n              i18n: gradio.i18n,\n              display_mode,\n              clear_color,\n              label,\n              show_label,\n              camera_position,\n              zoom_speed,\n              has_change_history\n            },\n            {},\n            {}\n          )}` : ` ${validate_component(BlockLabel, \"BlockLabel\").$$render(\n            $$result,\n            {\n              show_label,\n              Icon: File,\n              label: label || \"3D Model\"\n            },\n            {},\n            {}\n          )} ${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true, size: \"large\" }, {}, {\n            default: () => {\n              return `${validate_component(File, \"File\").$$render($$result, {}, {}, {})}`;\n            }\n          })}`}`;\n        }\n      }\n    )}` : `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        variant: value === null ? \"dashed\" : \"solid\",\n        border_mode: \"base\",\n        padding: false,\n        elem_id,\n        elem_classes,\n        container,\n        scale,\n        min_width,\n        height\n      },\n      {},\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${validate_component(Model3DUpload$1, \"Model3DUpload\").$$render(\n            $$result,\n            {\n              label,\n              show_label,\n              root,\n              display_mode,\n              clear_color,\n              value,\n              camera_position,\n              zoom_speed,\n              i18n: gradio.i18n,\n              max_file_size: gradio.max_file_size,\n              upload: (...args) => gradio.client.upload(...args),\n              stream_handler: (...args) => gradio.client.stream(...args),\n              uploading\n            },\n            {\n              uploading: ($$value) => {\n                uploading = $$value;\n                $$settled = false;\n              }\n            },\n            {\n              default: () => {\n                return `${validate_component(UploadText, \"UploadText\").$$render($$result, { i18n: gradio.i18n, type: \"file\" }, {}, {})}`;\n              }\n            }\n          )}`;\n        }\n      }\n    )}`}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  default2 as BaseExample,\n  Model3D$1 as BaseModel3D,\n  Model3DUpload$1 as BaseModel3DUpload,\n  Index as default\n};\n"], "names": ["File"], "mappings": ";;;;;;;;;;;AAKA,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC1C,SAAS,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AAC9B,EAAE,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;AAC3B,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;AACxB,MAAM,OAAO,GAAG,CAAC;AACjB,GAAG;AACH,CAAC;AACD,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE;AAC1B,EAAE,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;AACrB,EAAE,IAAI,GAAG,KAAK,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,WAAW,MAAM,GAAG,CAAC,WAAW,EAAE;AAClE,IAAI,IAAI,IAAI,KAAK,IAAI;AACrB,MAAM,OAAO,GAAG,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;AAC7C,IAAI,IAAI,IAAI,KAAK,MAAM;AACvB,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,QAAQ,EAAE,CAAC;AAC/C,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACxB,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE;AAC7C,QAAQ,OAAO,GAAG,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAClD,UAAU,CAAC;AACX,OAAO;AACP,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE;AACtB,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;AACjC,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,QAAQ,GAAG,GAAG,GAAG,CAAC;AAClB,QAAQ,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC5C,UAAU,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/B,UAAU,IAAI,CAAC,GAAG;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACzB,UAAU,OAAO,KAAK,CAAC;AACvB,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,GAAG,EAAE;AACtB,MAAM,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;AACjC,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,QAAQ,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC5C,UAAU,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/B,UAAU,IAAI,CAAC,GAAG;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAC3C,UAAU,OAAO,KAAK,CAAC;AACvB,SAAS;AACT,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,WAAW,EAAE;AAC9B,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAChC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAChC,KAAK,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,UAAU,MAAM,GAAG,CAAC,UAAU,EAAE;AACrD,QAAQ,OAAO,GAAG,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;AAC7D,UAAU,CAAC;AACX,OAAO;AACP,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AACjC,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,UAAU,MAAM,GAAG,CAAC,UAAU,EAAE;AACrD,QAAQ,OAAO,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC;AAC7C,UAAU,CAAC;AACX,OAAO;AACP,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAC1C,MAAM,GAAG,GAAG,CAAC,CAAC;AACd,MAAM,KAAK,IAAI,IAAI,GAAG,EAAE;AACxB,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;AAChE,UAAU,OAAO,KAAK,CAAC;AACvB,QAAQ,IAAI,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3D,UAAU,OAAO,KAAK,CAAC;AACvB,OAAO;AACP,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC;AAC7C,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC;AACpC,CAAC;AACD,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,2QAA2Q;AACnR,EAAE,GAAG,EAAE,CAAC,y6HAAy6H,CAAC;AACl7H,CAAC,CAAC;AACF,eAAe,cAAc,GAAG;AAChC,EAAE,MAAM,MAAM,GAAG,MAAM,OAAO,wBAAe,CAAC,CAAC;AAC/C,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AACD,eAAe,gBAAgB,GAAG;AAClC,EAAE,MAAM,MAAM,GAAG,MAAM,OAAO,0BAAiB,CAAC,CAAC;AACjD,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AACD,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,YAAY,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,eAAe,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACzD,EAAE,IAAI,EAAE,kBAAkB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,gBAAgB,GAAG,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;AACpE,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,mBAAmB,CAAC;AAC1B,EAAE,IAAI,iBAAiB,CAAC;AACxB,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAChF,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK;AACjD,YAAY,mBAAmB,GAAG,SAAS,CAAC;AAC5C,WAAW,CAAC,CAAC;AACb,SAAS,MAAM;AACf,UAAU,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK;AAC/C,YAAY,iBAAiB,GAAG,SAAS,CAAC;AAC1C,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,eAAe,CAAC,IAAI,gBAAgB,CAAC,UAAU,KAAK,UAAU,IAAI,gBAAgB,CAAC,SAAS,KAAK,SAAS,EAAE;AAClK,UAAU,QAAQ,EAAE,aAAa,CAAC,eAAe,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAC1E,UAAU,gBAAgB,GAAG,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;AACxE,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACzE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAEA,MAAI;AAClB,QAAQ,KAAK,EAAE,KAAK,IAAI,IAAI,CAAC,mBAAmB,CAAC;AACjD,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,oCAAoC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AAC9I,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACvF,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,MAAM;AACzB,YAAY,QAAQ,EAAE,CAAC,kBAAkB;AACzC,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,YAAY,GAAG,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa;AAChJ,UAAU,UAAU;AACpB,UAAU,MAAM,CAAC,YAAY,GAAG,IAAI,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI;AACpE,UAAU,CAAC;AACX,SAAS,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAClE,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC;AAC1C,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,IAAI,CAAC,CAAC;AAChB,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,mBAAmB,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACjH,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK;AACb,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,YAAY;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,YAAY,EAAE,CAAC,OAAO,KAAK;AACnC,UAAU,YAAY,GAAG,OAAO,CAAC;AACjC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACpG,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK;AACb,QAAQ,YAAY;AACpB,QAAQ,WAAW;AACnB,QAAQ,eAAe;AACvB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,YAAY;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,YAAY,EAAE,CAAC,OAAO,KAAK;AACnC,UAAU,YAAY,GAAG,OAAO,CAAC;AACjC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,SAAS,GAAG,QAAQ;AAC1B,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,2TAA2T;AACnU,EAAE,GAAG,EAAE,m8HAAm8H;AAC18H,CAAC,CAAC;AACF,eAAe,YAAY,GAAG;AAC9B,EAAE,MAAM,MAAM,GAAG,MAAM,OAAO,wBAAe,CAAC,CAAC;AAC/C,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AACD,eAAe,cAAc,GAAG;AAChC,EAAE,MAAM,MAAM,GAAG,MAAM,OAAO,0BAAiB,CAAC,CAAC;AACjD,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AACD,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACrF,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,YAAY,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,SAAS,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,eAAe,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACzD,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,mBAAmB,CAAC;AAC1B,EAAE,IAAI,iBAAiB,CAAC;AACxB,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAChF,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK;AAC/C,YAAY,mBAAmB,GAAG,SAAS,CAAC;AAC5C,WAAW,CAAC,CAAC;AACb,SAAS,MAAM;AACf,UAAU,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK;AAC7C,YAAY,iBAAiB,GAAG,SAAS,CAAC;AAC1C,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACzE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAEA,MAAI;AAClB,QAAQ,KAAK,EAAE,KAAK,IAAI,UAAU;AAClC,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC1E,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,IAAI;AACZ,QAAQ,aAAa;AACrB,QAAQ,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC;AAClF,QAAQ,UAAU,EAAE,IAAI,CAAC,wBAAwB,CAAC;AAClD,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,OAAO,KAAK;AAChC,UAAU,SAAS,GAAG,OAAO,CAAC;AAC9B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,uCAAuC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,mBAAmB,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC5Z,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK;AACb,QAAQ,YAAY;AACpB,QAAQ,WAAW;AACnB,QAAQ,eAAe;AACvB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,IAAI,EAAE,QAAQ;AACtB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,eAAe,GAAG,cAAc;AACjC,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,YAAY,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC;AACxB,EAAE,IAAI,EAAE,kBAAkB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,EAAE,eAAe,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACzD,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,WAAW,GAAG,CAAC,SAAS,CAAC;AAC7B,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACjF,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO,EAAE,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG,OAAO;AACpD,QAAQ,WAAW,EAAE,MAAM;AAC3B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,QAAQ;AACrQ,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,KAAK;AACnB,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,YAAY;AAC1B,cAAc,WAAW;AACzB,cAAc,KAAK;AACnB,cAAc,UAAU;AACxB,cAAc,eAAe;AAC7B,cAAc,UAAU;AACxB,cAAc,kBAAkB;AAChC,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACzE,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU;AACxB,cAAc,IAAI,EAAEA,MAAI;AACxB,cAAc,KAAK,EAAE,KAAK,IAAI,UAAU;AACxC,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AAChH,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAACA,MAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1F,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACxD,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO,EAAE,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG,OAAO;AACpD,QAAQ,WAAW,EAAE,MAAM;AAC3B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,QAAQ;AACxP,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,KAAK;AACnB,cAAc,UAAU;AACxB,cAAc,IAAI;AAClB,cAAc,YAAY;AAC1B,cAAc,WAAW;AACzB,cAAc,KAAK;AACnB,cAAc,eAAe;AAC7B,cAAc,UAAU;AACxB,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,aAAa,EAAE,MAAM,CAAC,aAAa;AACjD,cAAc,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAChE,cAAc,cAAc,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACxE,cAAc,SAAS;AACvB,aAAa;AACb,YAAY;AACZ,cAAc,SAAS,EAAE,CAAC,OAAO,KAAK;AACtC,gBAAgB,SAAS,GAAG,OAAO,CAAC;AACpC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,MAAM;AAC7B,gBAAgB,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACzI,eAAe;AACf,aAAa;AACb,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}