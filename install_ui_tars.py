#!/usr/bin/env python3
"""
UI-TARS 1.5-7B 一键安装脚本
自动检测环境并安装所需依赖
"""

import subprocess
import sys
import os
import platform
import torch
from pathlib import Path

def run_command(command, check=True):
    """运行命令并返回结果"""
    print(f"执行: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, 
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_cuda():
    """检查CUDA可用性"""
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            print(f"✅ CUDA可用，设备数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
                print(f"  显存: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
        else:
            print("⚠️ CUDA不可用，将使用CPU运行（速度较慢）")
        return cuda_available
    except ImportError:
        print("⚠️ PyTorch未安装，无法检测CUDA")
        return False

def install_pytorch():
    """安装PyTorch"""
    print("\n📦 安装PyTorch...")
    
    # 检测CUDA版本
    cuda_version = None
    try:
        result = subprocess.run("nvidia-smi", capture_output=True, text=True)
        if result.returncode == 0:
            # 简单的CUDA版本检测
            if "CUDA Version: 12" in result.stdout:
                cuda_version = "cu121"
            elif "CUDA Version: 11" in result.stdout:
                cuda_version = "cu118"
    except:
        pass
    
    if cuda_version:
        command = f"pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/{cuda_version}"
    else:
        command = "pip install torch torchvision torchaudio"
    
    return run_command(command)

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    dependencies = [
        "transformers>=4.36.0",
        "accelerate",
        "bitsandbytes",
        "pillow",
        "requests",
        "gradio",
        "numpy",
        "sentencepiece",
        "protobuf"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        if not run_command(f"pip install {dep}"):
            print(f"❌ 安装 {dep} 失败")
            return False
    
    print("✅ 所有依赖包安装完成")
    return True

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        import torch
        import transformers
        import gradio
        from PIL import Image
        
        print("✅ 所有包导入成功")
        
        # 测试基本功能
        print(f"PyTorch版本: {torch.__version__}")
        print(f"Transformers版本: {transformers.__version__}")
        print(f"Gradio版本: {gradio.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def create_run_script():
    """创建运行脚本"""
    print("\n📝 创建运行脚本...")
    
    # Windows批处理文件
    if platform.system() == "Windows":
        script_content = """@echo off
echo 启动UI-TARS Web界面...
python ui_tars_web_interface.py --host 0.0.0.0 --port 7860
pause
"""
        with open("run_ui_tars.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("✅ 创建了 run_ui_tars.bat")
    
    # Unix shell脚本
    script_content = """#!/bin/bash
echo "启动UI-TARS Web界面..."
python ui_tars_web_interface.py --host 0.0.0.0 --port 7860
"""
    with open("run_ui_tars.sh", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    # 添加执行权限
    if platform.system() != "Windows":
        os.chmod("run_ui_tars.sh", 0o755)
        print("✅ 创建了 run_ui_tars.sh")

def main():
    """主安装流程"""
    print("🚀 UI-TARS 1.5-7B 一键安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查是否在虚拟环境中
    if sys.prefix == sys.base_prefix:
        print("⚠️ 建议在虚拟环境中安装，是否继续？(y/N)")
        if input().lower() != 'y':
            print("请创建虚拟环境后重新运行安装脚本")
            sys.exit(1)
    
    # 升级pip
    print("\n📦 升级pip...")
    run_command("python -m pip install --upgrade pip")
    
    # 安装PyTorch
    if not install_pytorch():
        print("❌ PyTorch安装失败")
        sys.exit(1)
    
    # 检查CUDA
    check_cuda()
    
    # 安装其他依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 测试安装
    if not test_installation():
        print("❌ 安装测试失败")
        sys.exit(1)
    
    # 创建运行脚本
    create_run_script()
    
    print("\n🎉 安装完成！")
    print("\n📋 下一步操作：")
    print("1. 运行 'python ui_tars_web_interface.py' 启动Web界面")
    print("2. 或者运行 'python ui_tars_local_deploy.py' 使用命令行界面")
    print("3. 首次运行时会自动下载模型（约13GB）")
    print("4. 访问 http://localhost:7860 使用Web界面")
    
    print("\n💡 提示：")
    print("- 如果显存不足，模型会自动使用CPU运行")
    print("- 可以通过设置环境变量 HF_ENDPOINT=https://hf-mirror.com 加速下载")
    print("- 更多帮助请查看 UI-TARS_部署指南.md")

if __name__ == "__main__":
    main()
