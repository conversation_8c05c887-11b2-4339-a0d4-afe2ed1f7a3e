{"version": 3, "file": "Index60-Dxc4NW3k.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index60.js"], "sourcesContent": ["import { create_ssr_component, validate_component, each, add_styles, escape, missing_component, add_attribute, null_to_empty } from \"svelte/internal\";\nimport { h as <PERSON><PERSON><PERSON><PERSON><PERSON>, x as <PERSON><PERSON>, I as IconButtonWrapper, aj as <PERSON>, A as Clear, ak as <PERSON><PERSON>, _ as Undo, Z as Edit, u as MarkdownCode, H as File, al as DropdownCircularArrow, M as Music, am as Community, an as Trash, n as Block, S as Static, B as BlockLabel, ao as Chat } from \"./client.js\";\nimport { I as Image } from \"./Image.js\";\nimport \"./ImagePreview.js\";\nimport { createEventDispatcher, onDestroy, onMount } from \"svelte\";\nimport \"svelte/transition\";\nimport { d as dequal } from \"./index7.js\";\nconst redirect_src_url = (src, root) => src.replace('src=\"/file', `src=\"${root}file`);\nfunction get_component_for_mime_type(mime_type, file) {\n  if (!mime_type) {\n    const path = file?.path;\n    if (path) {\n      const lower_path = path.toLowerCase();\n      if (lower_path.endsWith(\".glb\") || lower_path.endsWith(\".gltf\") || lower_path.endsWith(\".obj\") || lower_path.endsWith(\".stl\") || lower_path.endsWith(\".splat\") || lower_path.endsWith(\".ply\")) {\n        return \"model3d\";\n      }\n    }\n    return \"file\";\n  }\n  if (mime_type.includes(\"audio\"))\n    return \"audio\";\n  if (mime_type.includes(\"video\"))\n    return \"video\";\n  if (mime_type.includes(\"image\"))\n    return \"image\";\n  if (mime_type.includes(\"model\"))\n    return \"model3d\";\n  return \"file\";\n}\nfunction convert_file_message_to_component_message(message) {\n  const _file = Array.isArray(message.file) ? message.file[0] : message.file;\n  return {\n    component: get_component_for_mime_type(_file?.mime_type, _file),\n    value: message.file,\n    alt_text: message.alt_text,\n    constructor_args: {},\n    props: {}\n  };\n}\nfunction normalise_messages(messages, root) {\n  if (messages === null)\n    return messages;\n  const thought_map = /* @__PURE__ */ new Map();\n  return messages.map((message, i) => {\n    let normalized = typeof message.content === \"string\" ? {\n      role: message.role,\n      metadata: message.metadata,\n      content: redirect_src_url(message.content, root),\n      type: \"text\",\n      index: i,\n      options: message.options\n    } : \"file\" in message.content ? {\n      content: convert_file_message_to_component_message(\n        message.content\n      ),\n      metadata: message.metadata,\n      role: message.role,\n      type: \"component\",\n      index: i,\n      options: message.options\n    } : { type: \"component\", ...message };\n    const { id, title, parent_id } = message.metadata || {};\n    if (parent_id) {\n      const parent = thought_map.get(String(parent_id));\n      if (parent) {\n        const thought = { ...normalized, children: [] };\n        parent.children.push(thought);\n        if (id && title) {\n          thought_map.set(String(id), thought);\n        }\n        return null;\n      }\n    }\n    if (id && title) {\n      const thought = { ...normalized, children: [] };\n      thought_map.set(String(id), thought);\n      return thought;\n    }\n    return normalized;\n  }).filter((msg) => msg !== null);\n}\nfunction normalise_tuples(messages, root) {\n  if (messages === null)\n    return messages;\n  const msg = messages.flatMap((message_pair, i) => {\n    return message_pair.map((message, index) => {\n      if (message == null)\n        return null;\n      const role = index == 0 ? \"user\" : \"assistant\";\n      if (typeof message === \"string\") {\n        return {\n          role,\n          type: \"text\",\n          content: redirect_src_url(message, root),\n          metadata: { title: null },\n          index: [i, index]\n        };\n      }\n      if (\"file\" in message) {\n        return {\n          content: convert_file_message_to_component_message(message),\n          role,\n          type: \"component\",\n          index: [i, index]\n        };\n      }\n      return {\n        role,\n        content: message,\n        type: \"component\",\n        index: [i, index]\n      };\n    });\n  });\n  return msg.filter((message) => message != null);\n}\nfunction is_component_message(message) {\n  return message.type === \"component\";\n}\nfunction is_last_bot_message(messages, all_messages) {\n  const is_bot = messages[messages.length - 1].role === \"assistant\";\n  const last_index = messages[messages.length - 1].index;\n  const is_last = JSON.stringify(last_index) === JSON.stringify(all_messages[all_messages.length - 1].index);\n  return is_last && is_bot;\n}\nfunction group_messages(messages, msg_format) {\n  const groupedMessages = [];\n  let currentGroup = [];\n  let currentRole = null;\n  for (const message of messages) {\n    if (!(message.role === \"assistant\" || message.role === \"user\")) {\n      continue;\n    }\n    if (message.role === currentRole) {\n      currentGroup.push(message);\n    } else {\n      if (currentGroup.length > 0) {\n        groupedMessages.push(currentGroup);\n      }\n      currentGroup = [message];\n      currentRole = message.role;\n    }\n  }\n  if (currentGroup.length > 0) {\n    groupedMessages.push(currentGroup);\n  }\n  return groupedMessages;\n}\nasync function load_components(component_names, _components, load_component) {\n  let names = [];\n  let components = [];\n  component_names.forEach((component_name) => {\n    if (_components[component_name] || component_name === \"file\") {\n      return;\n    }\n    const variant = component_name === \"dataframe\" || component_name === \"model3d\" ? \"component\" : \"base\";\n    const { name, component } = load_component(component_name, variant);\n    names.push(name);\n    components.push(component);\n  });\n  const resolved_components = await Promise.allSettled(components);\n  const supported_components = resolved_components.map(\n    (result, index) => result.status === \"fulfilled\" ? [index, result.value] : null\n  ).filter((item) => item !== null);\n  supported_components.forEach(([originalIndex, component]) => {\n    _components[names[originalIndex]] = component.default;\n  });\n  return _components;\n}\nfunction get_components_from_messages(messages) {\n  if (!messages)\n    return [];\n  let components = /* @__PURE__ */ new Set();\n  messages.forEach((message) => {\n    if (message.type === \"component\") {\n      components.add(message.content.component);\n    }\n  });\n  return Array.from(components);\n}\nfunction get_thought_content(msg, depth = 0) {\n  let content = \"\";\n  const indent = \"  \".repeat(depth);\n  if (msg.metadata?.title) {\n    content += `${indent}${depth > 0 ? \"- \" : \"\"}${msg.metadata.title}\n`;\n  }\n  if (typeof msg.content === \"string\") {\n    content += `${indent}  ${msg.content}\n`;\n  }\n  const thought = msg;\n  if (thought.children?.length > 0) {\n    content += thought.children.map((child) => get_thought_content(child, depth + 1)).join(\"\");\n  }\n  return content;\n}\nfunction all_text(message) {\n  if (Array.isArray(message)) {\n    return message.map((m) => {\n      if (m.metadata?.title) {\n        return get_thought_content(m);\n      }\n      return m.content;\n    }).join(\"\\n\");\n  }\n  if (message.metadata?.title) {\n    return get_thought_content(message);\n  }\n  return message.content;\n}\nfunction is_all_text(message) {\n  return Array.isArray(message) && message.every((m) => typeof m.content === \"string\") || !Array.isArray(message) && typeof message.content === \"string\";\n}\nconst ThumbDownActive = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst ThumbDownDefault = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst ThumbUpActive = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst ThumbUpDefault = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\" fill=\"currentColor\"></path></svg>`;\n});\nconst Flag = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg id=\"icon\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" fill=\"none\"><path fill=\"currentColor\" d=\"M6,30H4V2H28l-5.8,9L28,20H6ZM6,18H24.33L19.8,11l4.53-7H6Z\"></path></svg>`;\n});\nconst FlagActive = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg id=\"icon\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" fill=\"none\"><path fill=\"currentColor\" d=\"M4,2H28l-5.8,9L28,20H6v10H4V2z\"></path></svg>`;\n});\nconst css$8 = {\n  code: \".extra-feedback.svelte-14rmxes.svelte-14rmxes{display:flex;align-items:center;position:relative}.extra-feedback-options.svelte-14rmxes.svelte-14rmxes{display:none;position:absolute;padding:var(--spacing-md) 0;flex-direction:column;gap:var(--spacing-sm);top:100%}.extra-feedback.svelte-14rmxes:hover .extra-feedback-options.svelte-14rmxes{display:flex}.extra-feedback-option.svelte-14rmxes.svelte-14rmxes{border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);color:var(--block-label-text-color);background-color:var(--block-background-fill);font-size:var(--text-xs);padding:var(--spacing-xxs) var(--spacing-sm);width:max-content}\",\n  map: '{\"version\":3,\"file\":\"LikeDislike.svelte\",\"sources\":[\"LikeDislike.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport ThumbDownActive from \\\\\"./ThumbDownActive.svelte\\\\\";\\\\nimport ThumbDownDefault from \\\\\"./ThumbDownDefault.svelte\\\\\";\\\\nimport ThumbUpActive from \\\\\"./ThumbUpActive.svelte\\\\\";\\\\nimport ThumbUpDefault from \\\\\"./ThumbUpDefault.svelte\\\\\";\\\\nimport Flag from \\\\\"./Flag.svelte\\\\\";\\\\nimport FlagActive from \\\\\"./FlagActive.svelte\\\\\";\\\\nexport let i18n;\\\\nexport let handle_action;\\\\nexport let feedback_options;\\\\nexport let selected = null;\\\\n$: extra_feedback = feedback_options.filter((option) => option !== \\\\\"Like\\\\\" && option !== \\\\\"Dislike\\\\\");\\\\nfunction toggleSelection(newSelection) {\\\\n    selected = selected === newSelection ? null : newSelection;\\\\n    handle_action(selected);\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if feedback_options.includes(\\\\\"Like\\\\\") || feedback_options.includes(\\\\\"Dislike\\\\\")}\\\\n\\\\t{#if feedback_options.includes(\\\\\"Dislike\\\\\")}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={selected === \\\\\"Dislike\\\\\" ? ThumbDownActive : ThumbDownDefault}\\\\n\\\\t\\\\t\\\\tlabel={selected === \\\\\"Dislike\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"clicked dislike\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: i18n(\\\\\"chatbot.dislike\\\\\")}\\\\n\\\\t\\\\t\\\\tcolor={selected === \\\\\"Dislike\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--block-label-text-color)\\\\\"}\\\\n\\\\t\\\\t\\\\ton:click={() => toggleSelection(\\\\\"Dislike\\\\\")}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t{#if feedback_options.includes(\\\\\"Like\\\\\")}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={selected === \\\\\"Like\\\\\" ? ThumbUpActive : ThumbUpDefault}\\\\n\\\\t\\\\t\\\\tlabel={selected === \\\\\"Like\\\\\" ? \\\\\"clicked like\\\\\" : i18n(\\\\\"chatbot.like\\\\\")}\\\\n\\\\t\\\\t\\\\tcolor={selected === \\\\\"Like\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--block-label-text-color)\\\\\"}\\\\n\\\\t\\\\t\\\\ton:click={() => toggleSelection(\\\\\"Like\\\\\")}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n{/if}\\\\n\\\\n{#if extra_feedback.length > 0}\\\\n\\\\t<div class=\\\\\"extra-feedback no-border\\\\\">\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={selected && extra_feedback.includes(selected) ? FlagActive : Flag}\\\\n\\\\t\\\\t\\\\tlabel=\\\\\"Feedback\\\\\"\\\\n\\\\t\\\\t\\\\tcolor={selected && extra_feedback.includes(selected)\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--block-label-text-color)\\\\\"}\\\\n\\\\t\\\\t/>\\\\n\\\\t\\\\t<div class=\\\\\"extra-feedback-options\\\\\">\\\\n\\\\t\\\\t\\\\t{#each extra_feedback as option}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"extra-feedback-option\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstyle:font-weight={selected === option ? \\\\\"bold\\\\\" : \\\\\"normal\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttoggleSelection(option);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_action(selected ? selected : null);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}>{option}</button\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.extra-feedback {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\t.extra-feedback-options {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tpadding: var(--spacing-md) 0;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\ttop: 100%;\\\\n\\\\t}\\\\n\\\\t.extra-feedback:hover .extra-feedback-options {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t}\\\\n\\\\t.extra-feedback-option {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tcolor: var(--block-label-text-color);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tfont-size: var(--text-xs);\\\\n\\\\t\\\\tpadding: var(--spacing-xxs) var(--spacing-sm);\\\\n\\\\t\\\\twidth: max-content;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAoEC,6CAAgB,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QACX,CACA,qDAAwB,CACvB,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,CAAC,CAC5B,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,GAAG,CAAE,IACN,CACA,8BAAe,MAAM,CAAC,sCAAwB,CAC7C,OAAO,CAAE,IACV,CACA,oDAAuB,CACtB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,wBAAwB,CAAC,CACpC,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,OAAO,CAAE,IAAI,aAAa,CAAC,CAAC,IAAI,YAAY,CAAC,CAC7C,KAAK,CAAE,WACR\"}'\n};\nconst LikeDislike = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let extra_feedback;\n  let { i18n } = $$props;\n  let { handle_action } = $$props;\n  let { feedback_options } = $$props;\n  let { selected = null } = $$props;\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.handle_action === void 0 && $$bindings.handle_action && handle_action !== void 0)\n    $$bindings.handle_action(handle_action);\n  if ($$props.feedback_options === void 0 && $$bindings.feedback_options && feedback_options !== void 0)\n    $$bindings.feedback_options(feedback_options);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  $$result.css.add(css$8);\n  extra_feedback = feedback_options.filter((option) => option !== \"Like\" && option !== \"Dislike\");\n  return `${feedback_options.includes(\"Like\") || feedback_options.includes(\"Dislike\") ? `${feedback_options.includes(\"Dislike\") ? `${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: selected === \"Dislike\" ? ThumbDownActive : ThumbDownDefault,\n      label: selected === \"Dislike\" ? \"clicked dislike\" : i18n(\"chatbot.dislike\"),\n      color: selected === \"Dislike\" ? \"var(--color-accent)\" : \"var(--block-label-text-color)\"\n    },\n    {},\n    {}\n  )}` : ``} ${feedback_options.includes(\"Like\") ? `${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: selected === \"Like\" ? ThumbUpActive : ThumbUpDefault,\n      label: selected === \"Like\" ? \"clicked like\" : i18n(\"chatbot.like\"),\n      color: selected === \"Like\" ? \"var(--color-accent)\" : \"var(--block-label-text-color)\"\n    },\n    {},\n    {}\n  )}` : ``}` : ``} ${extra_feedback.length > 0 ? `<div class=\"extra-feedback no-border svelte-14rmxes\">${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: selected && extra_feedback.includes(selected) ? FlagActive : Flag,\n      label: \"Feedback\",\n      color: selected && extra_feedback.includes(selected) ? \"var(--color-accent)\" : \"var(--block-label-text-color)\"\n    },\n    {},\n    {}\n  )} <div class=\"extra-feedback-options svelte-14rmxes\">${each(extra_feedback, (option) => {\n    return `<button class=\"extra-feedback-option svelte-14rmxes\"${add_styles({\n      \"font-weight\": selected === option ? \"bold\" : \"normal\"\n    })}>${escape(option)}</button>`;\n  })}</div></div>` : ``}`;\n});\nconst Copy_1 = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  createEventDispatcher();\n  let { value } = $$props;\n  let { watermark = null } = $$props;\n  onDestroy(() => {\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.watermark === void 0 && $$bindings.watermark && watermark !== void 0)\n    $$bindings.watermark(watermark);\n  return `${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      label: \"Copy message\",\n      Icon: Copy\n    },\n    {},\n    {}\n  )}`;\n});\nconst css$7 = {\n  code: \".bubble.svelte-j7nkv7 .icon-button-wrapper{margin:0px calc(var(--spacing-xl) * 2)}.message-buttons.svelte-j7nkv7{z-index:var(--layer-1)}.message-buttons-left.svelte-j7nkv7{align-self:flex-start}.bubble.message-buttons-right.svelte-j7nkv7{align-self:flex-end}.message-buttons-right.svelte-j7nkv7 .icon-button-wrapper{margin-left:auto}.bubble.with-avatar.svelte-j7nkv7{margin-left:calc(var(--spacing-xl) * 5);margin-right:calc(var(--spacing-xl) * 5)}.panel.svelte-j7nkv7{display:flex;align-self:flex-start;z-index:var(--layer-1)}\",\n  map: `{\"version\":3,\"file\":\"ButtonPanel.svelte\",\"sources\":[\"ButtonPanel.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import LikeDislike from \\\\\"./LikeDislike.svelte\\\\\";\\\\nimport Copy from \\\\\"./Copy.svelte\\\\\";\\\\nimport { Retry, Undo, Edit, Check, Clear } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { all_text, is_all_text } from \\\\\"./utils\\\\\";\\\\nexport let i18n;\\\\nexport let likeable;\\\\nexport let feedback_options;\\\\nexport let show_retry;\\\\nexport let show_undo;\\\\nexport let show_edit;\\\\nexport let in_edit_mode;\\\\nexport let show_copy_button;\\\\nexport let watermark = null;\\\\nexport let message;\\\\nexport let position;\\\\nexport let avatar;\\\\nexport let generating;\\\\nexport let current_feedback;\\\\nexport let handle_action;\\\\nexport let layout;\\\\nexport let dispatch;\\\\n$: message_text = is_all_text(message) ? all_text(message) : \\\\\"\\\\\";\\\\n$: show_copy = show_copy_button && message && is_all_text(message);\\\\n<\\/script>\\\\n\\\\n{#if show_copy || show_retry || show_undo || show_edit || likeable}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"message-buttons-{position} {layout} message-buttons {avatar !==\\\\n\\\\t\\\\t\\\\tnull && 'with-avatar'}\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<IconButtonWrapper top_panel={false}>\\\\n\\\\t\\\\t\\\\t{#if in_edit_mode}\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.submit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Check}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"edit_submit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.cancel\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Clear}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"edit_cancel\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_copy}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Copy\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue={message_text}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:copy={(e) => dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_retry}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Retry}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.retry\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"retry\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_undo}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.undo\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Undo}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"undo\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_edit}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.edit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Edit}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_action(\\\\\"edit\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if likeable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<LikeDislike\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{handle_action}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{feedback_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tselected={current_feedback}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.bubble :global(.icon-button-wrapper) {\\\\n\\\\t\\\\tmargin: 0px calc(var(--spacing-xl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-buttons {\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t}\\\\n\\\\t.message-buttons-left {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.message-buttons-right {\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-buttons-right :global(.icon-button-wrapper) {\\\\n\\\\t\\\\tmargin-left: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.with-avatar {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xl) * 5);\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xl) * 5);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA2FC,qBAAO,CAAS,oBAAsB,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,8BAAiB,CAChB,OAAO,CAAE,IAAI,SAAS,CACvB,CACA,mCAAsB,CACrB,UAAU,CAAE,UACb,CAEA,OAAO,oCAAuB,CAC7B,UAAU,CAAE,QACb,CAEA,oCAAsB,CAAS,oBAAsB,CACpD,WAAW,CAAE,IACd,CAEA,OAAO,0BAAa,CACnB,WAAW,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACxC,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACzC,CAEA,oBAAO,CACN,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,IAAI,SAAS,CACvB\"}`\n};\nconst ButtonPanel = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let message_text;\n  let show_copy;\n  let { i18n } = $$props;\n  let { likeable } = $$props;\n  let { feedback_options } = $$props;\n  let { show_retry } = $$props;\n  let { show_undo } = $$props;\n  let { show_edit } = $$props;\n  let { in_edit_mode } = $$props;\n  let { show_copy_button } = $$props;\n  let { watermark = null } = $$props;\n  let { message } = $$props;\n  let { position } = $$props;\n  let { avatar } = $$props;\n  let { generating } = $$props;\n  let { current_feedback } = $$props;\n  let { handle_action } = $$props;\n  let { layout } = $$props;\n  let { dispatch } = $$props;\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.likeable === void 0 && $$bindings.likeable && likeable !== void 0)\n    $$bindings.likeable(likeable);\n  if ($$props.feedback_options === void 0 && $$bindings.feedback_options && feedback_options !== void 0)\n    $$bindings.feedback_options(feedback_options);\n  if ($$props.show_retry === void 0 && $$bindings.show_retry && show_retry !== void 0)\n    $$bindings.show_retry(show_retry);\n  if ($$props.show_undo === void 0 && $$bindings.show_undo && show_undo !== void 0)\n    $$bindings.show_undo(show_undo);\n  if ($$props.show_edit === void 0 && $$bindings.show_edit && show_edit !== void 0)\n    $$bindings.show_edit(show_edit);\n  if ($$props.in_edit_mode === void 0 && $$bindings.in_edit_mode && in_edit_mode !== void 0)\n    $$bindings.in_edit_mode(in_edit_mode);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.watermark === void 0 && $$bindings.watermark && watermark !== void 0)\n    $$bindings.watermark(watermark);\n  if ($$props.message === void 0 && $$bindings.message && message !== void 0)\n    $$bindings.message(message);\n  if ($$props.position === void 0 && $$bindings.position && position !== void 0)\n    $$bindings.position(position);\n  if ($$props.avatar === void 0 && $$bindings.avatar && avatar !== void 0)\n    $$bindings.avatar(avatar);\n  if ($$props.generating === void 0 && $$bindings.generating && generating !== void 0)\n    $$bindings.generating(generating);\n  if ($$props.current_feedback === void 0 && $$bindings.current_feedback && current_feedback !== void 0)\n    $$bindings.current_feedback(current_feedback);\n  if ($$props.handle_action === void 0 && $$bindings.handle_action && handle_action !== void 0)\n    $$bindings.handle_action(handle_action);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.dispatch === void 0 && $$bindings.dispatch && dispatch !== void 0)\n    $$bindings.dispatch(dispatch);\n  $$result.css.add(css$7);\n  message_text = is_all_text(message) ? all_text(message) : \"\";\n  show_copy = show_copy_button && message && is_all_text(message);\n  return `${show_copy || show_retry || show_undo || show_edit || likeable ? `<div class=\"${\"message-buttons-\" + escape(position, true) + \" \" + escape(layout, true) + \" message-buttons \" + escape(avatar !== null && \"with-avatar\", true) + \" svelte-j7nkv7\"}\">${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, { top_panel: false }, {}, {\n    default: () => {\n      return `${in_edit_mode ? `${validate_component(IconButton, \"IconButton\").$$render(\n        $$result,\n        {\n          label: i18n(\"chatbot.submit\"),\n          Icon: Check,\n          disabled: generating\n        },\n        {},\n        {}\n      )} ${validate_component(IconButton, \"IconButton\").$$render(\n        $$result,\n        {\n          label: i18n(\"chatbot.cancel\"),\n          Icon: Clear,\n          disabled: generating\n        },\n        {},\n        {}\n      )}` : `${show_copy ? `${validate_component(Copy_1, \"Copy\").$$render($$result, { value: message_text, watermark }, {}, {})}` : ``} ${show_retry ? `${validate_component(IconButton, \"IconButton\").$$render(\n        $$result,\n        {\n          Icon: Retry,\n          label: i18n(\"chatbot.retry\"),\n          disabled: generating\n        },\n        {},\n        {}\n      )}` : ``} ${show_undo ? `${validate_component(IconButton, \"IconButton\").$$render(\n        $$result,\n        {\n          label: i18n(\"chatbot.undo\"),\n          Icon: Undo,\n          disabled: generating\n        },\n        {},\n        {}\n      )}` : ``} ${show_edit ? `${validate_component(IconButton, \"IconButton\").$$render(\n        $$result,\n        {\n          label: i18n(\"chatbot.edit\"),\n          Icon: Edit,\n          disabled: generating\n        },\n        {},\n        {}\n      )}` : ``} ${likeable ? `${validate_component(LikeDislike, \"LikeDislike\").$$render(\n        $$result,\n        {\n          handle_action,\n          feedback_options,\n          selected: current_feedback,\n          i18n\n        },\n        {},\n        {}\n      )}` : ``}`}`;\n    }\n  })}</div>` : ``}`;\n});\nconst Component = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { type } = $$props;\n  let { components } = $$props;\n  let { value } = $$props;\n  let { target } = $$props;\n  let { theme_mode } = $$props;\n  let { props } = $$props;\n  let { i18n } = $$props;\n  let { upload } = $$props;\n  let { _fetch } = $$props;\n  let { allow_file_downloads } = $$props;\n  let { display_icon_button_wrapper_top_corner = false } = $$props;\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.components === void 0 && $$bindings.components && components !== void 0)\n    $$bindings.components(components);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.target === void 0 && $$bindings.target && target !== void 0)\n    $$bindings.target(target);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.props === void 0 && $$bindings.props && props !== void 0)\n    $$bindings.props(props);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.allow_file_downloads === void 0 && $$bindings.allow_file_downloads && allow_file_downloads !== void 0)\n    $$bindings.allow_file_downloads(allow_file_downloads);\n  if ($$props.display_icon_button_wrapper_top_corner === void 0 && $$bindings.display_icon_button_wrapper_top_corner && display_icon_button_wrapper_top_corner !== void 0)\n    $$bindings.display_icon_button_wrapper_top_corner(display_icon_button_wrapper_top_corner);\n  return `${type === \"gallery\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      display_icon_button_wrapper_top_corner,\n      show_label: false,\n      i18n,\n      label: \"\",\n      _fetch,\n      allow_preview: false,\n      interactive: false,\n      mode: \"minimal\",\n      fixed_height: 1\n    },\n    {},\n    {}\n  )}` : `${type === \"dataframe\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      i18n,\n      label: \"\",\n      interactive: false,\n      line_breaks: props.line_breaks,\n      wrap: true,\n      root: \"\",\n      gradio: {\n        dispatch: () => {\n        },\n        i18n\n      },\n      datatype: props.datatype,\n      latex_delimiters: props.latex_delimiters,\n      col_count: props.col_count,\n      row_count: props.row_count\n    },\n    {},\n    {}\n  )}` : `${type === \"plot\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      target,\n      theme_mode,\n      bokeh_version: props.bokeh_version,\n      caption: \"\",\n      show_actions_button: true\n    },\n    {},\n    {}\n  )}` : `${type === \"audio\" ? `<div style=\"position: relative;\">${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      show_share_button: true,\n      i18n,\n      label: \"\",\n      waveform_settings: { autoplay: props.autoplay },\n      show_download_button: allow_file_downloads,\n      display_icon_button_wrapper_top_corner\n    },\n    {},\n    {}\n  )}</div>` : `${type === \"video\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      autoplay: props.autoplay,\n      value: value.video || value,\n      show_label: false,\n      show_share_button: true,\n      i18n,\n      upload,\n      display_icon_button_wrapper_top_corner,\n      show_download_button: allow_file_downloads\n    },\n    {},\n    {\n      default: () => {\n        return `<track kind=\"captions\">`;\n      }\n    }\n  )}` : `${type === \"image\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      label: \"chatbot-image\",\n      show_download_button: allow_file_downloads,\n      display_icon_button_wrapper_top_corner,\n      i18n\n    },\n    {},\n    {}\n  )}` : `${type === \"html\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      show_label: false,\n      label: \"chatbot-html\",\n      show_share_button: true,\n      i18n,\n      gradio: {\n        dispatch: () => {\n        }\n      }\n    },\n    {},\n    {}\n  )}` : `${type === \"model3d\" ? `${validate_component(components[type] || missing_component, \"svelte:component\").$$render(\n    $$result,\n    Object.assign({}, { value }, { clear_color: props.clear_color }, { display_mode: props.display_mode }, { zoom_speed: props.zoom_speed }, { pan_speed: props.pan_speed }, props.camera_position !== void 0 && { camera_position: props.camera_position }, { has_change_history: true }, { show_label: false }, { root: \"\" }, { interactive: false }, { label: \"chatbot-model3d\" }, { show_share_button: true }, {\n      gradio: {\n        dispatch: () => {\n        },\n        i18n\n      }\n    }),\n    {},\n    {}\n  )}` : ``}`}`}`}`}`}`}`}`;\n});\nconst css$6 = {\n  code: \".file-container.svelte-ulpe0d{display:flex;align-items:center;gap:var(--spacing-lg);padding:var(--spacing-lg);border-radius:var(--radius-lg);width:fit-content;margin:var(--spacing-sm) 0}.file-icon.svelte-ulpe0d{display:flex;align-items:center;justify-content:center;color:var(--body-text-color)}.file-icon.svelte-ulpe0d svg{width:var(--size-7);height:var(--size-7)}.file-info.svelte-ulpe0d{display:flex;flex-direction:column}.file-link.svelte-ulpe0d{text-decoration:none;color:var(--body-text-color);display:flex;flex-direction:column;gap:var(--spacing-xs)}.file-name.svelte-ulpe0d{font-family:var(--font);font-size:var(--text-md);font-weight:500}.file-type.svelte-ulpe0d{font-family:var(--font);font-size:var(--text-sm);color:var(--body-text-color-subdued);text-transform:uppercase}\",\n  map: '{\"version\":3,\"file\":\"MessageContent.svelte\",\"sources\":[\"MessageContent.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { File } from \\\\\"@gradio/icons\\\\\";\\\\nimport Component from \\\\\"./Component.svelte\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nexport let latex_delimiters;\\\\nexport let sanitize_html;\\\\nexport let _fetch;\\\\nexport let i18n;\\\\nexport let line_breaks;\\\\nexport let upload;\\\\nexport let target;\\\\nexport let root;\\\\nexport let theme_mode;\\\\nexport let _components;\\\\nexport let render_markdown;\\\\nexport let scroll;\\\\nexport let allow_file_downloads;\\\\nexport let display_consecutive_in_same_bubble;\\\\nexport let thought_index;\\\\nexport let allow_tags = false;\\\\nexport let message;\\\\n<\\/script>\\\\n\\\\n{#if message.type === \\\\\"text\\\\\"}\\\\n\\\\t<div class=\\\\\"message-content\\\\\">\\\\n\\\\t\\\\t<Markdown\\\\n\\\\t\\\\t\\\\tmessage={message.content}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\ton:load={scroll}\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n{:else if message.type === \\\\\"component\\\\\" && message.content.component in _components}\\\\n\\\\t<Component\\\\n\\\\t\\\\t{target}\\\\n\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\tprops={message.content.props}\\\\n\\\\t\\\\ttype={message.content.component}\\\\n\\\\t\\\\tcomponents={_components}\\\\n\\\\t\\\\tvalue={message.content.value}\\\\n\\\\t\\\\tdisplay_icon_button_wrapper_top_corner={thought_index > 0 &&\\\\n\\\\t\\\\t\\\\tdisplay_consecutive_in_same_bubble}\\\\n\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t{upload}\\\\n\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\ton:load={() => scroll()}\\\\n\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t/>\\\\n{:else if message.type === \\\\\"component\\\\\" && message.content.component === \\\\\"file\\\\\"}\\\\n\\\\t<div class=\\\\\"file-container\\\\\">\\\\n\\\\t\\\\t<div class=\\\\\"file-icon\\\\\">\\\\n\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t</div>\\\\n\\\\t\\\\t<div class=\\\\\"file-info\\\\\">\\\\n\\\\t\\\\t\\\\t<a\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"chatbot-file\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"file-link\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\thref={message.content.value.url}\\\\n\\\\t\\\\t\\\\t\\\\ttarget=\\\\\"_blank\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tdownload={window.__is_colab__\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t? null\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t: message.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path.split(\\\\\"/\\\\\").pop() ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"file\\\\\"}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"file-name\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t>{message.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path.split(\\\\\"/\\\\\").pop() ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"file\\\\\"}</span\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t</a>\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"file-type\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t>{(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.orig_name ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.value?.path ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.split(\\\\\".\\\\\")\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.pop()\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.toUpperCase()}</span\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.file-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgap: var(--spacing-lg);\\\\n\\\\t\\\\tpadding: var(--spacing-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\tmargin: var(--spacing-sm) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-icon {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-icon :global(svg) {\\\\n\\\\t\\\\twidth: var(--size-7);\\\\n\\\\t\\\\theight: var(--size-7);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-info {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-link {\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--spacing-xs);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-name {\\\\n\\\\t\\\\tfont-family: var(--font);\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tfont-weight: 500;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-type {\\\\n\\\\t\\\\tfont-family: var(--font);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued);\\\\n\\\\t\\\\ttext-transform: uppercase;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA0FC,6BAAgB,CACf,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,WAAW,CAClB,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,CAC3B,CAEA,wBAAW,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB,CAEA,wBAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAEA,wBAAW,CACV,eAAe,CAAE,IAAI,CACrB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,YAAY,CACtB,CAEA,wBAAW,CACV,WAAW,CAAE,IAAI,MAAM,CAAC,CACxB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,GACd,CAEA,wBAAW,CACV,WAAW,CAAE,IAAI,MAAM,CAAC,CACxB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,cAAc,CAAE,SACjB\"}'\n};\nconst MessageContent = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { latex_delimiters } = $$props;\n  let { sanitize_html } = $$props;\n  let { _fetch } = $$props;\n  let { i18n } = $$props;\n  let { line_breaks } = $$props;\n  let { upload } = $$props;\n  let { target } = $$props;\n  let { root } = $$props;\n  let { theme_mode } = $$props;\n  let { _components } = $$props;\n  let { render_markdown } = $$props;\n  let { scroll: scroll2 } = $$props;\n  let { allow_file_downloads } = $$props;\n  let { display_consecutive_in_same_bubble } = $$props;\n  let { thought_index } = $$props;\n  let { allow_tags = false } = $$props;\n  let { message } = $$props;\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.target === void 0 && $$bindings.target && target !== void 0)\n    $$bindings.target(target);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props._components === void 0 && $$bindings._components && _components !== void 0)\n    $$bindings._components(_components);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props.scroll === void 0 && $$bindings.scroll && scroll2 !== void 0)\n    $$bindings.scroll(scroll2);\n  if ($$props.allow_file_downloads === void 0 && $$bindings.allow_file_downloads && allow_file_downloads !== void 0)\n    $$bindings.allow_file_downloads(allow_file_downloads);\n  if ($$props.display_consecutive_in_same_bubble === void 0 && $$bindings.display_consecutive_in_same_bubble && display_consecutive_in_same_bubble !== void 0)\n    $$bindings.display_consecutive_in_same_bubble(display_consecutive_in_same_bubble);\n  if ($$props.thought_index === void 0 && $$bindings.thought_index && thought_index !== void 0)\n    $$bindings.thought_index(thought_index);\n  if ($$props.allow_tags === void 0 && $$bindings.allow_tags && allow_tags !== void 0)\n    $$bindings.allow_tags(allow_tags);\n  if ($$props.message === void 0 && $$bindings.message && message !== void 0)\n    $$bindings.message(message);\n  $$result.css.add(css$6);\n  return `${message.type === \"text\" ? `<div class=\"message-content\">${validate_component(MarkdownCode, \"Markdown\").$$render(\n    $$result,\n    {\n      message: message.content,\n      latex_delimiters,\n      sanitize_html,\n      render_markdown,\n      line_breaks,\n      root,\n      allow_tags,\n      theme_mode\n    },\n    {},\n    {}\n  )}</div>` : `${message.type === \"component\" && message.content.component in _components ? `${validate_component(Component, \"Component\").$$render(\n    $$result,\n    {\n      target,\n      theme_mode,\n      props: message.content.props,\n      type: message.content.component,\n      components: _components,\n      value: message.content.value,\n      display_icon_button_wrapper_top_corner: thought_index > 0 && display_consecutive_in_same_bubble,\n      i18n,\n      upload,\n      _fetch,\n      allow_file_downloads\n    },\n    {},\n    {}\n  )}` : `${message.type === \"component\" && message.content.component === \"file\" ? `<div class=\"file-container svelte-ulpe0d\"><div class=\"file-icon svelte-ulpe0d\">${validate_component(File, \"File\").$$render($$result, {}, {}, {})}</div> <div class=\"file-info svelte-ulpe0d\"><a data-testid=\"chatbot-file\" class=\"file-link svelte-ulpe0d\"${add_attribute(\"href\", message.content.value.url, 0)} target=\"_blank\"${add_attribute(\n    \"download\",\n    window.__is_colab__ ? null : message.content.value?.orig_name || message.content.value?.path.split(\"/\").pop() || \"file\",\n    0\n  )}><span class=\"file-name svelte-ulpe0d\">${escape(message.content.value?.orig_name || message.content.value?.path.split(\"/\").pop() || \"file\")}</span></a> <span class=\"file-type svelte-ulpe0d\">${escape((message.content.value?.orig_name || message.content.value?.path || \"\").split(\".\").pop().toUpperCase())}</span></div></div>` : ``}`}`}`;\n});\nconst css$5 = {\n  code: \".thought-group.svelte-1qn6r4f{background:var(--background-fill-primary);border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);padding:var(--spacing-md);margin:var(--spacing-md) 0;font-size:var(--text-sm)}.children.svelte-1qn6r4f .thought-group{border:none;margin:0;padding-bottom:0}.children.svelte-1qn6r4f{padding-left:var(--spacing-md)}.title.svelte-1qn6r4f{display:flex;align-items:center;color:var(--body-text-color);cursor:pointer;width:100%}.title.svelte-1qn6r4f .md{font-size:var(--text-sm) !important}.content.svelte-1qn6r4f,.content-preview.svelte-1qn6r4f{overflow-wrap:break-word;word-break:break-word;margin-left:var(--spacing-lg);margin-bottom:var(--spacing-sm)}.content-preview.svelte-1qn6r4f{position:relative;max-height:calc(5 * 1.5em);overflow-y:auto;overscroll-behavior:contain;cursor:default}.content.svelte-1qn6r4f *,.content-preview.svelte-1qn6r4f *{font-size:var(--text-sm);color:var(--body-text-color)}.thought-group.svelte-1qn6r4f .thought:not(.nested){border:none;background:none}.duration.svelte-1qn6r4f{color:var(--body-text-color-subdued);font-size:var(--text-sm);margin-left:var(--size-1)}.arrow.svelte-1qn6r4f{opacity:0.8;width:var(--size-8);height:var(--size-8);display:flex;align-items:center;justify-content:center}.arrow.svelte-1qn6r4f button{background-color:transparent}.loading-spinner.svelte-1qn6r4f{display:inline-block;width:12px;height:12px;border:2px solid var(--body-text-color);border-radius:50%;border-top-color:transparent;animation:svelte-1qn6r4f-spin 1s linear infinite;margin:0 var(--size-1) -1px var(--size-2);opacity:0.8}@keyframes svelte-1qn6r4f-spin{to{transform:rotate(360deg)}}.thought-group.svelte-1qn6r4f .message-content{opacity:0.8}\",\n  map: '{\"version\":3,\"file\":\"Thought.svelte\",\"sources\":[\"Thought.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import MessageContent from \\\\\"./MessageContent.svelte\\\\\";\\\\nimport { DropdownCircularArrow } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { slide } from \\\\\"svelte/transition\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nexport let thought;\\\\nexport let rtl = false;\\\\nexport let sanitize_html;\\\\nexport let latex_delimiters;\\\\nexport let render_markdown;\\\\nexport let _components;\\\\nexport let upload;\\\\nexport let thought_index;\\\\nexport let target;\\\\nexport let root;\\\\nexport let theme_mode;\\\\nexport let _fetch;\\\\nexport let scroll;\\\\nexport let allow_file_downloads;\\\\nexport let display_consecutive_in_same_bubble;\\\\nexport let i18n;\\\\nexport let line_breaks;\\\\nexport let allow_tags = false;\\\\nfunction is_thought_node(msg) {\\\\n    return \\\\\"children\\\\\" in msg;\\\\n}\\\\nlet thought_node;\\\\nlet expanded = false;\\\\nlet user_expanded_toggled = false;\\\\nlet content_preview_element;\\\\nlet user_is_scrolling = false;\\\\n$: thought_node = {\\\\n    ...thought,\\\\n    children: is_thought_node(thought) ? thought.children : []\\\\n};\\\\n$: if (!user_expanded_toggled) {\\\\n    expanded = thought_node?.metadata?.status !== \\\\\"done\\\\\";\\\\n}\\\\nfunction toggleExpanded() {\\\\n    expanded = !expanded;\\\\n    user_expanded_toggled = true;\\\\n}\\\\nfunction scrollToBottom() {\\\\n    if (content_preview_element && !user_is_scrolling) {\\\\n        content_preview_element.scrollTop = content_preview_element.scrollHeight;\\\\n    }\\\\n}\\\\nfunction handleScroll() {\\\\n    if (content_preview_element) {\\\\n        const is_at_bottom = content_preview_element.scrollHeight - content_preview_element.scrollTop <= content_preview_element.clientHeight + 10;\\\\n        if (!is_at_bottom) {\\\\n            user_is_scrolling = true;\\\\n        }\\\\n    }\\\\n}\\\\n$: if (thought_node.content && content_preview_element && thought_node.metadata?.status !== \\\\\"done\\\\\") {\\\\n    setTimeout(scrollToBottom, 0);\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"thought-group\\\\\">\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"title\\\\\"\\\\n\\\\t\\\\tclass:expanded\\\\n\\\\t\\\\ton:click|stopPropagation={toggleExpanded}\\\\n\\\\t\\\\taria-busy={thought_node.content === \\\\\"\\\\\" || thought_node.content === null}\\\\n\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\ton:keydown={(e) => e.key === \\\\\"Enter\\\\\" && toggleExpanded()}\\\\n\\\\t>\\\\n\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\tclass=\\\\\"arrow\\\\\"\\\\n\\\\t\\\\t\\\\tstyle:transform={expanded ? \\\\\"rotate(180deg)\\\\\" : \\\\\"rotate(0deg)\\\\\"}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<IconButton Icon={DropdownCircularArrow} />\\\\n\\\\t\\\\t</span>\\\\n\\\\t\\\\t<Markdown\\\\n\\\\t\\\\t\\\\tmessage={thought_node.metadata?.title || \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t/>\\\\n\\\\t\\\\t{#if thought_node.metadata?.status === \\\\\"pending\\\\\"}\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"loading-spinner\\\\\"></span>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{#if thought_node?.metadata?.log || thought_node?.metadata?.duration}\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"duration\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t{#if thought_node.metadata.log}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{thought_node.metadata.log}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if thought_node.metadata.duration !== undefined}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t({#if Number.isInteger(thought_node.metadata.duration)}{thought_node\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t.metadata\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t.duration}s{:else if thought_node.metadata.duration >= 0.1}{thought_node.metadata.duration.toFixed(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t1\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}s{:else}{(thought_node.metadata.duration * 1000).toFixed(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t1\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}ms{/if})\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n\\\\n\\\\t{#if expanded}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass:content={expanded}\\\\n\\\\t\\\\t\\\\tclass:content-preview={!expanded &&\\\\n\\\\t\\\\t\\\\t\\\\tthought_node.metadata?.status !== \\\\\"done\\\\\"}\\\\n\\\\t\\\\t\\\\tbind:this={content_preview_element}\\\\n\\\\t\\\\t\\\\ton:scroll={handleScroll}\\\\n\\\\t\\\\t\\\\ttransition:slide\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<MessageContent\\\\n\\\\t\\\\t\\\\t\\\\tmessage={thought_node}\\\\n\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t{thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t\\\\t{#if thought_node.children?.length > 0}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"children\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each thought_node.children as child, index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<svelte:self\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthought={child}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthought_index={thought_index + 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.thought-group {\\\\n\\\\t\\\\tbackground: var(--background-fill-primary);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tpadding: var(--spacing-md);\\\\n\\\\t\\\\tmargin: var(--spacing-md) 0;\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.children :global(.thought-group) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tpadding-bottom: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.children {\\\\n\\\\t\\\\tpadding-left: var(--spacing-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.title {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.title :global(.md) {\\\\n\\\\t\\\\tfont-size: var(--text-sm) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.content,\\\\n\\\\t.content-preview {\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t\\\\tword-break: break-word;\\\\n\\\\t\\\\tmargin-left: var(--spacing-lg);\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.content-preview {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tmax-height: calc(5 * 1.5em);\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t\\\\toverscroll-behavior: contain;\\\\n\\\\t\\\\tcursor: default;\\\\n\\\\t}\\\\n\\\\n\\\\t.content :global(*),\\\\n\\\\t.content-preview :global(*) {\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.thought-group :global(.thought:not(.nested)) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.duration {\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tmargin-left: var(--size-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.arrow {\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.arrow :global(button) {\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t}\\\\n\\\\n\\\\t.loading-spinner {\\\\n\\\\t\\\\tdisplay: inline-block;\\\\n\\\\t\\\\twidth: 12px;\\\\n\\\\t\\\\theight: 12px;\\\\n\\\\t\\\\tborder: 2px solid var(--body-text-color);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder-top-color: transparent;\\\\n\\\\t\\\\tanimation: spin 1s linear infinite;\\\\n\\\\t\\\\tmargin: 0 var(--size-1) -1px var(--size-2);\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes spin {\\\\n\\\\t\\\\tto {\\\\n\\\\t\\\\t\\\\ttransform: rotate(360deg);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.thought-group :global(.message-content) {\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAoKC,6BAAe,CACd,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,CAAC,CAC3B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,wBAAS,CAAS,cAAgB,CACjC,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,CAAC,CACT,cAAc,CAAE,CACjB,CAEA,wBAAU,CACT,YAAY,CAAE,IAAI,YAAY,CAC/B,CAEA,qBAAO,CACN,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IACR,CAEA,qBAAM,CAAS,GAAK,CACnB,SAAS,CAAE,IAAI,SAAS,CAAC,CAAC,UAC3B,CAEA,uBAAQ,CACR,+BAAiB,CAChB,aAAa,CAAE,UAAU,CACzB,UAAU,CAAE,UAAU,CACtB,WAAW,CAAE,IAAI,YAAY,CAAC,CAC9B,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,+BAAiB,CAChB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAC3B,UAAU,CAAE,IAAI,CAChB,mBAAmB,CAAE,OAAO,CAC5B,MAAM,CAAE,OACT,CAEA,uBAAQ,CAAS,CAAE,CACnB,+BAAgB,CAAS,CAAG,CAC3B,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,6BAAc,CAAS,qBAAuB,CAC7C,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IACb,CAEA,wBAAU,CACT,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,qBAAO,CACN,OAAO,CAAE,GAAG,CACZ,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB,CAEA,qBAAM,CAAS,MAAQ,CACtB,gBAAgB,CAAE,WACnB,CAEA,+BAAiB,CAChB,OAAO,CAAE,YAAY,CACrB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,WAAW,CAC7B,SAAS,CAAE,mBAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAClC,MAAM,CAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAC1C,OAAO,CAAE,GACV,CAEA,WAAW,mBAAK,CACf,EAAG,CACF,SAAS,CAAE,OAAO,MAAM,CACzB,CACD,CAEA,6BAAc,CAAS,gBAAkB,CACxC,OAAO,CAAE,GACV\"}'\n};\nfunction is_thought_node(msg) {\n  return \"children\" in msg;\n}\nconst Thought = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { thought } = $$props;\n  let { rtl = false } = $$props;\n  let { sanitize_html } = $$props;\n  let { latex_delimiters } = $$props;\n  let { render_markdown } = $$props;\n  let { _components } = $$props;\n  let { upload } = $$props;\n  let { thought_index } = $$props;\n  let { target } = $$props;\n  let { root } = $$props;\n  let { theme_mode } = $$props;\n  let { _fetch } = $$props;\n  let { scroll: scroll2 } = $$props;\n  let { allow_file_downloads } = $$props;\n  let { display_consecutive_in_same_bubble } = $$props;\n  let { i18n } = $$props;\n  let { line_breaks } = $$props;\n  let { allow_tags = false } = $$props;\n  let thought_node;\n  let expanded = false;\n  let content_preview_element;\n  function scrollToBottom() {\n  }\n  if ($$props.thought === void 0 && $$bindings.thought && thought !== void 0)\n    $$bindings.thought(thought);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props._components === void 0 && $$bindings._components && _components !== void 0)\n    $$bindings._components(_components);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.thought_index === void 0 && $$bindings.thought_index && thought_index !== void 0)\n    $$bindings.thought_index(thought_index);\n  if ($$props.target === void 0 && $$bindings.target && target !== void 0)\n    $$bindings.target(target);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.scroll === void 0 && $$bindings.scroll && scroll2 !== void 0)\n    $$bindings.scroll(scroll2);\n  if ($$props.allow_file_downloads === void 0 && $$bindings.allow_file_downloads && allow_file_downloads !== void 0)\n    $$bindings.allow_file_downloads(allow_file_downloads);\n  if ($$props.display_consecutive_in_same_bubble === void 0 && $$bindings.display_consecutive_in_same_bubble && display_consecutive_in_same_bubble !== void 0)\n    $$bindings.display_consecutive_in_same_bubble(display_consecutive_in_same_bubble);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.allow_tags === void 0 && $$bindings.allow_tags && allow_tags !== void 0)\n    $$bindings.allow_tags(allow_tags);\n  $$result.css.add(css$5);\n  thought_node = {\n    ...thought,\n    children: is_thought_node(thought) ? thought.children : []\n  };\n  {\n    {\n      expanded = thought_node?.metadata?.status !== \"done\";\n    }\n  }\n  {\n    if (thought_node.content && content_preview_element && thought_node.metadata?.status !== \"done\") {\n      setTimeout(scrollToBottom, 0);\n    }\n  }\n  return `<div class=\"thought-group svelte-1qn6r4f\"><div class=\"${[\"title svelte-1qn6r4f\", expanded ? \"expanded\" : \"\"].join(\" \").trim()}\"${add_attribute(\"aria-busy\", thought_node.content === \"\" || thought_node.content === null, 0)} role=\"button\" tabindex=\"0\"><span class=\"arrow svelte-1qn6r4f\"${add_styles({\n    \"transform\": expanded ? \"rotate(180deg)\" : \"rotate(0deg)\"\n  })}>${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: DropdownCircularArrow }, {}, {})}</span> ${validate_component(MarkdownCode, \"Markdown\").$$render(\n    $$result,\n    {\n      message: thought_node.metadata?.title || \"\",\n      render_markdown,\n      latex_delimiters,\n      sanitize_html,\n      root,\n      allow_tags\n    },\n    {},\n    {}\n  )} ${thought_node.metadata?.status === \"pending\" ? `<span class=\"loading-spinner svelte-1qn6r4f\"></span>` : ``} ${thought_node?.metadata?.log || thought_node?.metadata?.duration ? `<span class=\"duration svelte-1qn6r4f\">${thought_node.metadata.log ? `${escape(thought_node.metadata.log)}` : ``} ${thought_node.metadata.duration !== void 0 ? `(${Number.isInteger(thought_node.metadata.duration) ? `${escape(thought_node.metadata.duration)}s` : `${thought_node.metadata.duration >= 0.1 ? `${escape(thought_node.metadata.duration.toFixed(1))}s` : `${escape((thought_node.metadata.duration * 1e3).toFixed(1))}ms`}`})` : ``}</span>` : ``}</div> ${expanded ? `<div class=\"${[\n    \"svelte-1qn6r4f\",\n    (expanded ? \"content\" : \"\") + \" \" + (!expanded && thought_node.metadata?.status !== \"done\" ? \"content-preview\" : \"\")\n  ].join(\" \").trim()}\"${add_attribute(\"this\", content_preview_element, 0)}>${validate_component(MessageContent, \"MessageContent\").$$render(\n    $$result,\n    {\n      message: thought_node,\n      sanitize_html,\n      allow_tags,\n      latex_delimiters,\n      render_markdown,\n      _components,\n      upload,\n      thought_index,\n      target,\n      root,\n      theme_mode,\n      _fetch,\n      scroll: scroll2,\n      allow_file_downloads,\n      display_consecutive_in_same_bubble,\n      i18n,\n      line_breaks\n    },\n    {},\n    {}\n  )} ${thought_node.children?.length > 0 ? `<div class=\"children svelte-1qn6r4f\">${each(thought_node.children, (child, index) => {\n    return `${validate_component(Thought, \"svelte:self\").$$render(\n      $$result,\n      {\n        thought: child,\n        rtl,\n        sanitize_html,\n        latex_delimiters,\n        render_markdown,\n        _components,\n        upload,\n        thought_index: thought_index + 1,\n        target,\n        root,\n        theme_mode,\n        _fetch,\n        scroll: scroll2,\n        allow_file_downloads,\n        display_consecutive_in_same_bubble,\n        i18n,\n        line_breaks\n      },\n      {},\n      {}\n    )}`;\n  })}</div>` : ``}</div>` : ``} </div>`;\n});\nconst css$4 = {\n  code: \".message.svelte-1csv61q.svelte-1csv61q{position:relative;width:100%;margin-top:var(--spacing-sm)}.message.display_consecutive_in_same_bubble.svelte-1csv61q.svelte-1csv61q{margin-top:0}.avatar-container.svelte-1csv61q.svelte-1csv61q{flex-shrink:0;border-radius:50%;border:1px solid var(--border-color-primary);overflow:hidden}.avatar-container.svelte-1csv61q img{object-fit:cover}.flex-wrap.svelte-1csv61q.svelte-1csv61q{display:flex;flex-direction:column;width:calc(100% - var(--spacing-xxl));max-width:100%;color:var(--body-text-color);font-size:var(--chatbot-text-size);overflow-wrap:break-word;width:100%;height:100%}.component.svelte-1csv61q.svelte-1csv61q{padding:0;border-radius:var(--radius-md);width:fit-content;overflow:hidden}.component.gallery.svelte-1csv61q.svelte-1csv61q{border:none}.bot.svelte-1csv61q.svelte-1csv61q:has(.model3D),.user.svelte-1csv61q.svelte-1csv61q:has(.model3D){border:none;max-width:75%}.message-row.svelte-1csv61q .svelte-1csv61q:not(.avatar-container) img{margin:var(--size-2);max-height:300px}.file-pil.svelte-1csv61q.svelte-1csv61q{display:block;width:fit-content;padding:var(--spacing-sm) var(--spacing-lg);border-radius:var(--radius-md);background:var(--background-fill-secondary);color:var(--body-text-color);text-decoration:none;margin:0;font-family:var(--font-mono);font-size:var(--text-sm)}.file.svelte-1csv61q.svelte-1csv61q{width:auto !important;max-width:fit-content !important}@media(max-width: 600px) or (max-width: 480px){.component.svelte-1csv61q.svelte-1csv61q{width:100%}}.message.svelte-1csv61q .prose{font-size:var(--chatbot-text-size)}.message-bubble-border.svelte-1csv61q.svelte-1csv61q{border-width:1px;border-radius:var(--radius-md)}.panel-full-width.svelte-1csv61q.svelte-1csv61q{width:100%}.message-markdown-disabled.svelte-1csv61q.svelte-1csv61q{white-space:pre-line}.user.svelte-1csv61q.svelte-1csv61q{border-radius:var(--radius-md);align-self:flex-end;border-bottom-right-radius:0;box-shadow:var(--shadow-drop);border:1px solid var(--border-color-accent-subdued);background-color:var(--color-accent-soft);padding:var(--spacing-sm) var(--spacing-xl)}.bot.svelte-1csv61q.svelte-1csv61q{border:1px solid var(--border-color-primary);border-radius:var(--radius-md);border-color:var(--border-color-primary);background-color:var(--background-fill-secondary);box-shadow:var(--shadow-drop);align-self:flex-start;text-align:right;border-bottom-left-radius:0;padding:var(--spacing-sm) var(--spacing-xl)}.bot.svelte-1csv61q.svelte-1csv61q:has(.table-wrap){border:none;box-shadow:none;background:none}.panel.svelte-1csv61q .user.svelte-1csv61q *{text-align:right}.message-row.svelte-1csv61q.svelte-1csv61q{display:flex;position:relative}.bubble.svelte-1csv61q.svelte-1csv61q{margin:calc(var(--spacing-xl) * 2);margin-bottom:var(--spacing-xl)}.bubble.user-row.svelte-1csv61q.svelte-1csv61q{align-self:flex-end;max-width:calc(100% - var(--spacing-xl) * 6)}.bubble.bot-row.svelte-1csv61q.svelte-1csv61q{align-self:flex-start;max-width:calc(100% - var(--spacing-xl) * 6)}.bubble.svelte-1csv61q .user-row.svelte-1csv61q{flex-direction:row;justify-content:flex-end}.bubble.svelte-1csv61q .with_avatar.user-row.svelte-1csv61q{margin-right:calc(var(--spacing-xl) * 2) !important}.bubble.svelte-1csv61q .with_avatar.bot-row.svelte-1csv61q{margin-left:calc(var(--spacing-xl) * 2) !important}.bubble.svelte-1csv61q .with_opposite_avatar.user-row.svelte-1csv61q{margin-left:calc(var(--spacing-xxl) + 35px + var(--spacing-xxl))}.panel.svelte-1csv61q.svelte-1csv61q{margin:0;padding:calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2)}.panel.bot-row.svelte-1csv61q.svelte-1csv61q{background:var(--background-fill-secondary)}.panel.svelte-1csv61q .with_avatar.svelte-1csv61q{padding-left:calc(var(--spacing-xl) * 2) !important;padding-right:calc(var(--spacing-xl) * 2) !important}.panel.svelte-1csv61q .panel-full-width.svelte-1csv61q{width:100%}.panel.svelte-1csv61q .user.svelte-1csv61q *{text-align:right}.flex-wrap.svelte-1csv61q.svelte-1csv61q{display:flex;flex-direction:column;max-width:100%;color:var(--body-text-color);font-size:var(--chatbot-text-size);overflow-wrap:break-word}@media(max-width: 480px){.user-row.bubble.svelte-1csv61q.svelte-1csv61q{align-self:flex-end}.bot-row.bubble.svelte-1csv61q.svelte-1csv61q{align-self:flex-start}.message.svelte-1csv61q.svelte-1csv61q{width:100%}}.avatar-container.svelte-1csv61q.svelte-1csv61q{align-self:flex-start;position:relative;display:flex;justify-content:flex-start;align-items:flex-start;width:35px;height:35px;flex-shrink:0;bottom:0;border-radius:50%;border:1px solid var(--border-color-primary)}.user-row.svelte-1csv61q>.avatar-container.svelte-1csv61q{order:2}.user-row.bubble.svelte-1csv61q>.avatar-container.svelte-1csv61q{margin-left:var(--spacing-xxl)}.bot-row.bubble.svelte-1csv61q>.avatar-container.svelte-1csv61q{margin-left:var(--spacing-xxl)}.panel.user-row.svelte-1csv61q>.avatar-container.svelte-1csv61q{order:0}.bot-row.bubble.svelte-1csv61q>.avatar-container.svelte-1csv61q{margin-right:var(--spacing-xxl);margin-left:0}.avatar-container.svelte-1csv61q:not(.thumbnail-item) img{width:100%;height:100%;object-fit:cover;border-radius:50%;padding:var(--size-1-5)}.selectable.svelte-1csv61q.svelte-1csv61q{cursor:pointer}@keyframes svelte-1csv61q-dot-flashing{0%{opacity:0.8}50%{opacity:0.5}100%{opacity:0.8}}.message.svelte-1csv61q .preview{object-fit:contain;width:95%;max-height:93%}.image-preview.svelte-1csv61q.svelte-1csv61q{position:absolute;z-index:999;left:0;top:0;width:100%;height:100%;overflow:auto;background-color:rgba(0, 0, 0, 0.9);display:flex;justify-content:center;align-items:center}.image-preview.svelte-1csv61q svg{stroke:white}.image-preview-close-button.svelte-1csv61q.svelte-1csv61q{position:absolute;top:10px;right:10px;background:none;border:none;font-size:1.5em;cursor:pointer;height:30px;width:30px;padding:3px;background:var(--bg-color);box-shadow:var(--shadow-drop);border:1px solid var(--button-secondary-border-color);border-radius:var(--radius-lg)}.message.svelte-1csv61q>div.svelte-1csv61q{width:100%}.html.svelte-1csv61q.svelte-1csv61q{padding:0;border:none;background:none}.panel.svelte-1csv61q .bot.svelte-1csv61q,.panel.svelte-1csv61q .user.svelte-1csv61q{border:none;box-shadow:none;background-color:var(--background-fill-secondary)}textarea.svelte-1csv61q.svelte-1csv61q{background:none;border-radius:var(--radius-lg);border:none;display:block;max-width:100%}.user.svelte-1csv61q textarea.svelte-1csv61q{border-bottom-right-radius:0}.bot.svelte-1csv61q textarea.svelte-1csv61q{border-bottom-left-radius:0}.user.svelte-1csv61q textarea.svelte-1csv61q:focus{outline:2px solid var(--border-color-accent)}.bot.svelte-1csv61q textarea.svelte-1csv61q:focus{outline:2px solid var(--border-color-primary)}.panel.user-row.svelte-1csv61q.svelte-1csv61q{background-color:var(--color-accent-soft)}.panel.svelte-1csv61q .user-row.svelte-1csv61q,.panel.svelte-1csv61q .bot-row.svelte-1csv61q{align-self:flex-start}.panel.svelte-1csv61q .user.svelte-1csv61q *,.panel.svelte-1csv61q .bot.svelte-1csv61q *{text-align:left}.panel.svelte-1csv61q .user.svelte-1csv61q{background-color:var(--color-accent-soft)}.panel.svelte-1csv61q .user-row.svelte-1csv61q{background-color:var(--color-accent-soft);align-self:flex-start}.panel.svelte-1csv61q .message.svelte-1csv61q{margin-bottom:var(--spacing-md)}\",\n  map: '{\"version\":3,\"file\":\"Message.svelte\",\"sources\":[\"Message.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { is_component_message } from \\\\\"../shared/utils\\\\\";\\\\nimport { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport ButtonPanel from \\\\\"./ButtonPanel.svelte\\\\\";\\\\nimport MessageContent from \\\\\"./MessageContent.svelte\\\\\";\\\\nimport Thought from \\\\\"./Thought.svelte\\\\\";\\\\nexport let value;\\\\nexport let avatar_img;\\\\nexport let opposite_avatar_img = null;\\\\nexport let role = \\\\\"user\\\\\";\\\\nexport let messages = [];\\\\nexport let layout;\\\\nexport let render_markdown;\\\\nexport let latex_delimiters;\\\\nexport let sanitize_html;\\\\nexport let selectable;\\\\nexport let _fetch;\\\\nexport let rtl;\\\\nexport let dispatch;\\\\nexport let i18n;\\\\nexport let line_breaks;\\\\nexport let upload;\\\\nexport let target;\\\\nexport let root;\\\\nexport let theme_mode;\\\\nexport let _components;\\\\nexport let i;\\\\nexport let show_copy_button;\\\\nexport let generating;\\\\nexport let feedback_options;\\\\nexport let show_like;\\\\nexport let show_edit;\\\\nexport let show_retry;\\\\nexport let show_undo;\\\\nexport let msg_format;\\\\nexport let handle_action;\\\\nexport let scroll;\\\\nexport let allow_file_downloads;\\\\nexport let in_edit_mode;\\\\nexport let edit_messages;\\\\nexport let display_consecutive_in_same_bubble;\\\\nexport let current_feedback = null;\\\\nexport let allow_tags = false;\\\\nexport let watermark = null;\\\\nlet messageElements = [];\\\\nlet previous_edit_mode = false;\\\\nlet message_widths = Array(messages.length).fill(160);\\\\nlet message_heights = Array(messages.length).fill(0);\\\\n$: if (in_edit_mode && !previous_edit_mode) {\\\\n    const offset = messageElements.length - messages.length;\\\\n    for (let idx = offset; idx < messageElements.length; idx++) {\\\\n        if (idx >= 0) {\\\\n            message_widths[idx - offset] = messageElements[idx]?.clientWidth;\\\\n            message_heights[idx - offset] = messageElements[idx]?.clientHeight;\\\\n        }\\\\n    }\\\\n}\\\\nfunction handle_select(i2, message) {\\\\n    dispatch(\\\\\"select\\\\\", {\\\\n        index: message.index,\\\\n        value: message.content\\\\n    });\\\\n}\\\\nfunction get_message_label_data(message) {\\\\n    if (message.type === \\\\\"text\\\\\") {\\\\n        return message.content;\\\\n    }\\\\n    else if (message.type === \\\\\"component\\\\\" && message.content.component === \\\\\"file\\\\\") {\\\\n        if (Array.isArray(message.content.value)) {\\\\n            return `file of extension type: ${message.content.value[0].orig_name?.split(\\\\\".\\\\\").pop()}`;\\\\n        }\\\\n        return `file of extension type: ${message.content.value?.orig_name?.split(\\\\\".\\\\\").pop()}` + (message.content.value?.orig_name ?? \\\\\"\\\\\");\\\\n    }\\\\n    return `a component of type ${message.content.component ?? \\\\\"unknown\\\\\"}`;\\\\n}\\\\nlet button_panel_props;\\\\n$: button_panel_props = {\\\\n    handle_action,\\\\n    likeable: show_like,\\\\n    feedback_options,\\\\n    show_retry,\\\\n    show_undo,\\\\n    show_edit,\\\\n    in_edit_mode,\\\\n    generating,\\\\n    show_copy_button,\\\\n    message: msg_format === \\\\\"tuples\\\\\" ? messages[0] : messages,\\\\n    position: role === \\\\\"user\\\\\" ? \\\\\"right\\\\\" : \\\\\"left\\\\\",\\\\n    avatar: avatar_img,\\\\n    layout,\\\\n    dispatch,\\\\n    current_feedback,\\\\n    watermark\\\\n};\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"message-row {layout} {role}-row\\\\\"\\\\n\\\\tclass:with_avatar={avatar_img !== null}\\\\n\\\\tclass:with_opposite_avatar={opposite_avatar_img !== null}\\\\n>\\\\n\\\\t{#if avatar_img !== null}\\\\n\\\\t\\\\t<div class=\\\\\"avatar-container\\\\\">\\\\n\\\\t\\\\t\\\\t<Image class=\\\\\"avatar-image\\\\\" src={avatar_img?.url} alt=\\\\\"{role} avatar\\\\\" />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass:role\\\\n\\\\t\\\\tclass=\\\\\"flex-wrap\\\\\"\\\\n\\\\t\\\\tclass:component-wrap={messages[0].type === \\\\\"component\\\\\"}\\\\n\\\\t>\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass:message={display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\tclass={display_consecutive_in_same_bubble ? role : \\\\\"\\\\\"}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#each messages as message, thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"message {!display_consecutive_in_same_bubble ? role : \\'\\'}\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:panel-full-width={true}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:message-markdown-disabled={!render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:component={message.type === \\\\\"component\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:html={is_component_message(message) &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessage.content.component === \\\\\"html\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:thought={thought_index > 0}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if in_edit_mode && message.type === \\\\\"text\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-autofocus -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<textarea\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"edit-textarea\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:width={`max(${message_widths[thought_index]}px, 160px)`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:min-height={`${message_heights[thought_index]}px`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tautofocus\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:value={edit_messages[thought_index]}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-static-element-interactions -->\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid={role}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:latest={i === value.length - 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:message-markdown-disabled={!render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:user-select=\\\\\"text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selectable\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:cursor={selectable ? \\\\\"pointer\\\\\" : \\\\\"auto\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle:text-align={rtl ? \\\\\"right\\\\\" : \\\\\"left\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:this={messageElements[thought_index]}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_select(i, message)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (e.key === \\\\\"Enter\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_select(i, message);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdir={rtl ? \\\\\"rtl\\\\\" : \\\\\"ltr\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={role +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"\\'s message: \\\\\" +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tget_message_label_data(message)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if message?.metadata?.title}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Thought\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tthought={message}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<MessageContent\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{message}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{thought_index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{scroll}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t{#if layout === \\\\\"panel\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<ButtonPanel\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{...button_panel_props}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{current_feedback}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:copy={(e) => dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n{#if layout === \\\\\"bubble\\\\\"}\\\\n\\\\t<ButtonPanel {...button_panel_props} {i18n} />\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.message {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tmargin-top: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.message.display_consecutive_in_same_bubble {\\\\n\\\\t\\\\tmargin-top: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t/* avatar styles */\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container :global(img) {\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t/* message wrapper */\\\\n\\\\t.flex-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\twidth: calc(100% - var(--spacing-xxl));\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.component {\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.component.gallery {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.bot:has(.model3D),\\\\n\\\\t.user:has(.model3D) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tmax-width: 75%;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-row :not(.avatar-container) :global(img) {\\\\n\\\\t\\\\tmargin: var(--size-2);\\\\n\\\\t\\\\tmax-height: 300px;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-pil {\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tfont-family: var(--font-mono);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t.file {\\\\n\\\\t\\\\twidth: auto !important;\\\\n\\\\t\\\\tmax-width: fit-content !important;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 600px) or (max-width: 480px) {\\\\n\\\\t\\\\t.component {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.message :global(.prose) {\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-bubble-border {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel-full-width {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\t.message-markdown-disabled {\\\\n\\\\t\\\\twhite-space: pre-line;\\\\n\\\\t}\\\\n\\\\n\\\\t.user {\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\tborder-bottom-right-radius: 0;\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-accent-subdued);\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tborder-color: var(--border-color-primary);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t\\\\tborder-bottom-left-radius: 0;\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot:has(.table-wrap) {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbox-shadow: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*) {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\t/* Colors */\\\\n\\\\n\\\\t.message-row {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t/* bubble mode styles */\\\\n\\\\t.bubble {\\\\n\\\\t\\\\tmargin: calc(var(--spacing-xl) * 2);\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.user-row {\\\\n\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\tmax-width: calc(100% - var(--spacing-xl) * 6);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.bot-row {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tmax-width: calc(100% - var(--spacing-xl) * 6);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .user-row {\\\\n\\\\t\\\\tflex-direction: row;\\\\n\\\\t\\\\tjustify-content: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_avatar.user-row {\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_avatar.bot-row {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble .with_opposite_avatar.user-row {\\\\n\\\\t\\\\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\\\\n\\\\t}\\\\n\\\\n\\\\t/* panel mode styles */\\\\n\\\\t.panel {\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.bot-row {\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .with_avatar {\\\\n\\\\t\\\\tpadding-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t\\\\tpadding-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .panel-full-width {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*) {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\t/* message content */\\\\n\\\\t.flex-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-size: var(--chatbot-text-size);\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 480px) {\\\\n\\\\t\\\\t.user-row.bubble {\\\\n\\\\t\\\\t\\\\talign-self: flex-end;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.bot-row.bubble {\\\\n\\\\t\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t.message {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: flex-start;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\twidth: 35px;\\\\n\\\\t\\\\theight: 35px;\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\t.user-row > .avatar-container {\\\\n\\\\t\\\\torder: 2;\\\\n\\\\t}\\\\n\\\\n\\\\t.user-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-left: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bot-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-left: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.user-row > .avatar-container {\\\\n\\\\t\\\\torder: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.bot-row.bubble > .avatar-container {\\\\n\\\\t\\\\tmargin-right: var(--spacing-xxl);\\\\n\\\\t\\\\tmargin-left: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container:not(.thumbnail-item) :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tpadding: var(--size-1-5);\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes dot-flashing {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\topacity: 0.5;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t/* Image preview */\\\\n\\\\t.message :global(.preview) {\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t\\\\twidth: 95%;\\\\n\\\\t\\\\tmax-height: 93%;\\\\n\\\\t}\\\\n\\\\t.image-preview {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tz-index: 999;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\toverflow: auto;\\\\n\\\\t\\\\tbackground-color: rgba(0, 0, 0, 0.9);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\t.image-preview :global(svg) {\\\\n\\\\t\\\\tstroke: white;\\\\n\\\\t}\\\\n\\\\t.image-preview-close-button {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 10px;\\\\n\\\\t\\\\tright: 10px;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tfont-size: 1.5em;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\theight: 30px;\\\\n\\\\t\\\\twidth: 30px;\\\\n\\\\t\\\\tpadding: 3px;\\\\n\\\\t\\\\tbackground: var(--bg-color);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--button-secondary-border-color);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.message > div {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\t.html {\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .bot,\\\\n\\\\t.panel .user {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbox-shadow: none;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea {\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t}\\\\n\\\\t.user textarea {\\\\n\\\\t\\\\tborder-bottom-right-radius: 0;\\\\n\\\\t}\\\\n\\\\t.bot textarea {\\\\n\\\\t\\\\tborder-bottom-left-radius: 0;\\\\n\\\\t}\\\\n\\\\t.user textarea:focus {\\\\n\\\\t\\\\toutline: 2px solid var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\t.bot textarea:focus {\\\\n\\\\t\\\\toutline: 2px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.user-row {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user-row,\\\\n\\\\t.panel .bot-row {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user :global(*),\\\\n\\\\t.panel .bot :global(*) {\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .user-row {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel .message {\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-md);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA4NC,sCAAS,CACR,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,YAAY,CAC7B,CAEA,QAAQ,iEAAoC,CAC3C,UAAU,CAAE,CACb,CAGA,+CAAkB,CACjB,WAAW,CAAE,CAAC,CACd,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,QAAQ,CAAE,MACX,CAEA,gCAAiB,CAAS,GAAK,CAC9B,UAAU,CAAE,KACb,CAGA,wCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CACtC,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,mBAAmB,CAAC,CACnC,aAAa,CAAE,UAAU,CACzB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,wCAAW,CACV,OAAO,CAAE,CAAC,CACV,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,WAAW,CAClB,QAAQ,CAAE,MACX,CAEA,UAAU,sCAAS,CAClB,MAAM,CAAE,IACT,CAEA,kCAAI,KAAK,QAAQ,CAAC,CAClB,mCAAK,KAAK,QAAQ,CAAE,CACnB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,GACZ,CAEA,2BAAY,gBAAC,KAAK,iBAAiB,CAAC,CAAS,GAAK,CACjD,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,UAAU,CAAE,KACb,CAEA,uCAAU,CACT,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,WAAW,CAClB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,eAAe,CAAE,IAAI,CACrB,MAAM,CAAE,CAAC,CACT,WAAW,CAAE,IAAI,WAAW,CAAC,CAC7B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,mCAAM,CACL,KAAK,CAAE,IAAI,CAAC,UAAU,CACtB,SAAS,CAAE,WAAW,CAAC,UACxB,CAEA,MAAO,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC,YAAY,KAAK,CAAE,CAC/C,wCAAW,CACV,KAAK,CAAE,IACR,CACD,CAEA,uBAAQ,CAAS,MAAQ,CACxB,SAAS,CAAE,IAAI,mBAAmB,CACnC,CAEA,oDAAuB,CACtB,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,+CAAkB,CACjB,KAAK,CAAE,IACR,CACA,wDAA2B,CAC1B,WAAW,CAAE,QACd,CAEA,mCAAM,CACL,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,QAAQ,CACpB,0BAA0B,CAAE,CAAC,CAC7B,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,6BAA6B,CAAC,CACpD,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAC5C,CAEA,kCAAK,CACJ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,YAAY,CAAE,IAAI,sBAAsB,CAAC,CACzC,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CAAE,UAAU,CACtB,UAAU,CAAE,KAAK,CACjB,yBAAyB,CAAE,CAAC,CAC5B,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAC5C,CAEA,kCAAI,KAAK,WAAW,CAAE,CACrB,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IACb,CAEA,qBAAM,CAAC,oBAAK,CAAS,CAAG,CACvB,UAAU,CAAE,KACb,CAIA,0CAAa,CACZ,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QACX,CAGA,qCAAQ,CACP,MAAM,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACnC,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,OAAO,uCAAU,CAChB,UAAU,CAAE,QAAQ,CACpB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,CAEA,OAAO,sCAAS,CACf,UAAU,CAAE,UAAU,CACtB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,CAEA,sBAAO,CAAC,wBAAU,CACjB,cAAc,CAAE,GAAG,CACnB,eAAe,CAAE,QAClB,CAEA,sBAAO,CAAC,YAAY,wBAAU,CAC7B,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC3C,CAEA,sBAAO,CAAC,YAAY,uBAAS,CAC5B,WAAW,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC1C,CAEA,sBAAO,CAAC,qBAAqB,wBAAU,CACtC,WAAW,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CACjE,CAGA,oCAAO,CACN,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,CAEA,MAAM,sCAAS,CACd,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CAEA,qBAAM,CAAC,2BAAa,CACnB,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CACpD,aAAa,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC5C,CAEA,qBAAM,CAAC,gCAAkB,CACxB,KAAK,CAAE,IACR,CAEA,qBAAM,CAAC,oBAAK,CAAS,CAAG,CACvB,UAAU,CAAE,KACb,CAGA,wCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,SAAS,CAAE,IAAI,CACf,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,SAAS,CAAE,IAAI,mBAAmB,CAAC,CACnC,aAAa,CAAE,UAChB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,SAAS,qCAAQ,CAChB,UAAU,CAAE,QACb,CAEA,QAAQ,qCAAQ,CACf,UAAU,CAAE,UACb,CACA,sCAAS,CACR,KAAK,CAAE,IACR,CACD,CAEA,+CAAkB,CACjB,UAAU,CAAE,UAAU,CACtB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,UAAU,CAC3B,WAAW,CAAE,UAAU,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,MAAM,CAAE,CAAC,CACT,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CACA,wBAAS,CAAG,gCAAkB,CAC7B,KAAK,CAAE,CACR,CAEA,SAAS,sBAAO,CAAG,gCAAkB,CACpC,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,QAAQ,sBAAO,CAAG,gCAAkB,CACnC,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,MAAM,wBAAS,CAAG,gCAAkB,CACnC,KAAK,CAAE,CACR,CAEA,QAAQ,sBAAO,CAAG,gCAAkB,CACnC,YAAY,CAAE,IAAI,aAAa,CAAC,CAChC,WAAW,CAAE,CACd,CAEA,gCAAiB,KAAK,eAAe,CAAC,CAAS,GAAK,CACnD,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,GAAG,CAClB,OAAO,CAAE,IAAI,UAAU,CACxB,CAEA,yCAAY,CACX,MAAM,CAAE,OACT,CAEA,WAAW,2BAAa,CACvB,EAAG,CACF,OAAO,CAAE,GACV,CACA,GAAI,CACH,OAAO,CAAE,GACV,CACA,IAAK,CACJ,OAAO,CAAE,GACV,CACD,CAGA,uBAAQ,CAAS,QAAU,CAC1B,UAAU,CAAE,OAAO,CACnB,KAAK,CAAE,GAAG,CACV,UAAU,CAAE,GACb,CACA,4CAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,GAAG,CACZ,IAAI,CAAE,CAAC,CACP,GAAG,CAAE,CAAC,CACN,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,IAAI,CACd,gBAAgB,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACpC,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd,CACA,6BAAc,CAAS,GAAK,CAC3B,MAAM,CAAE,KACT,CACA,yDAA4B,CAC3B,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,IAAI,CACT,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,OAAO,CACf,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,GAAG,CACZ,UAAU,CAAE,IAAI,UAAU,CAAC,CAC3B,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,+BAA+B,CAAC,CACtD,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,uBAAQ,CAAG,kBAAI,CACd,KAAK,CAAE,IACR,CACA,mCAAM,CACL,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IACb,CAEA,qBAAM,CAAC,mBAAI,CACX,qBAAM,CAAC,oBAAM,CACZ,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,gBAAgB,CAAE,IAAI,2BAA2B,CAClD,CAEA,sCAAS,CACR,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,IACZ,CACA,oBAAK,CAAC,uBAAS,CACd,0BAA0B,CAAE,CAC7B,CACA,mBAAI,CAAC,uBAAS,CACb,yBAAyB,CAAE,CAC5B,CACA,oBAAK,CAAC,uBAAQ,MAAO,CACpB,OAAO,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAC7C,CACA,mBAAI,CAAC,uBAAQ,MAAO,CACnB,OAAO,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC9C,CAEA,MAAM,uCAAU,CACf,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,qBAAM,CAAC,wBAAS,CAChB,qBAAM,CAAC,uBAAS,CACf,UAAU,CAAE,UACb,CAEA,qBAAM,CAAC,oBAAK,CAAS,CAAE,CACvB,qBAAM,CAAC,mBAAI,CAAS,CAAG,CACtB,UAAU,CAAE,IACb,CAEA,qBAAM,CAAC,oBAAM,CACZ,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,qBAAM,CAAC,wBAAU,CAChB,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,UAAU,CAAE,UACb,CAEA,qBAAM,CAAC,uBAAS,CACf,aAAa,CAAE,IAAI,YAAY,CAChC\"}'\n};\nlet previous_edit_mode = false;\nfunction get_message_label_data(message) {\n  if (message.type === \"text\") {\n    return message.content;\n  } else if (message.type === \"component\" && message.content.component === \"file\") {\n    if (Array.isArray(message.content.value)) {\n      return `file of extension type: ${message.content.value[0].orig_name?.split(\".\").pop()}`;\n    }\n    return `file of extension type: ${message.content.value?.orig_name?.split(\".\").pop()}` + (message.content.value?.orig_name ?? \"\");\n  }\n  return `a component of type ${message.content.component ?? \"unknown\"}`;\n}\nconst Message = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { avatar_img } = $$props;\n  let { opposite_avatar_img = null } = $$props;\n  let { role = \"user\" } = $$props;\n  let { messages = [] } = $$props;\n  let { layout } = $$props;\n  let { render_markdown } = $$props;\n  let { latex_delimiters } = $$props;\n  let { sanitize_html } = $$props;\n  let { selectable } = $$props;\n  let { _fetch } = $$props;\n  let { rtl } = $$props;\n  let { dispatch } = $$props;\n  let { i18n } = $$props;\n  let { line_breaks } = $$props;\n  let { upload } = $$props;\n  let { target } = $$props;\n  let { root } = $$props;\n  let { theme_mode } = $$props;\n  let { _components } = $$props;\n  let { i } = $$props;\n  let { show_copy_button } = $$props;\n  let { generating } = $$props;\n  let { feedback_options } = $$props;\n  let { show_like } = $$props;\n  let { show_edit } = $$props;\n  let { show_retry } = $$props;\n  let { show_undo } = $$props;\n  let { msg_format } = $$props;\n  let { handle_action } = $$props;\n  let { scroll: scroll2 } = $$props;\n  let { allow_file_downloads } = $$props;\n  let { in_edit_mode } = $$props;\n  let { edit_messages } = $$props;\n  let { display_consecutive_in_same_bubble } = $$props;\n  let { current_feedback = null } = $$props;\n  let { allow_tags = false } = $$props;\n  let { watermark = null } = $$props;\n  let messageElements = [];\n  let message_widths = Array(messages.length).fill(160);\n  let message_heights = Array(messages.length).fill(0);\n  let button_panel_props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.avatar_img === void 0 && $$bindings.avatar_img && avatar_img !== void 0)\n    $$bindings.avatar_img(avatar_img);\n  if ($$props.opposite_avatar_img === void 0 && $$bindings.opposite_avatar_img && opposite_avatar_img !== void 0)\n    $$bindings.opposite_avatar_img(opposite_avatar_img);\n  if ($$props.role === void 0 && $$bindings.role && role !== void 0)\n    $$bindings.role(role);\n  if ($$props.messages === void 0 && $$bindings.messages && messages !== void 0)\n    $$bindings.messages(messages);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.dispatch === void 0 && $$bindings.dispatch && dispatch !== void 0)\n    $$bindings.dispatch(dispatch);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.target === void 0 && $$bindings.target && target !== void 0)\n    $$bindings.target(target);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props._components === void 0 && $$bindings._components && _components !== void 0)\n    $$bindings._components(_components);\n  if ($$props.i === void 0 && $$bindings.i && i !== void 0)\n    $$bindings.i(i);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.generating === void 0 && $$bindings.generating && generating !== void 0)\n    $$bindings.generating(generating);\n  if ($$props.feedback_options === void 0 && $$bindings.feedback_options && feedback_options !== void 0)\n    $$bindings.feedback_options(feedback_options);\n  if ($$props.show_like === void 0 && $$bindings.show_like && show_like !== void 0)\n    $$bindings.show_like(show_like);\n  if ($$props.show_edit === void 0 && $$bindings.show_edit && show_edit !== void 0)\n    $$bindings.show_edit(show_edit);\n  if ($$props.show_retry === void 0 && $$bindings.show_retry && show_retry !== void 0)\n    $$bindings.show_retry(show_retry);\n  if ($$props.show_undo === void 0 && $$bindings.show_undo && show_undo !== void 0)\n    $$bindings.show_undo(show_undo);\n  if ($$props.msg_format === void 0 && $$bindings.msg_format && msg_format !== void 0)\n    $$bindings.msg_format(msg_format);\n  if ($$props.handle_action === void 0 && $$bindings.handle_action && handle_action !== void 0)\n    $$bindings.handle_action(handle_action);\n  if ($$props.scroll === void 0 && $$bindings.scroll && scroll2 !== void 0)\n    $$bindings.scroll(scroll2);\n  if ($$props.allow_file_downloads === void 0 && $$bindings.allow_file_downloads && allow_file_downloads !== void 0)\n    $$bindings.allow_file_downloads(allow_file_downloads);\n  if ($$props.in_edit_mode === void 0 && $$bindings.in_edit_mode && in_edit_mode !== void 0)\n    $$bindings.in_edit_mode(in_edit_mode);\n  if ($$props.edit_messages === void 0 && $$bindings.edit_messages && edit_messages !== void 0)\n    $$bindings.edit_messages(edit_messages);\n  if ($$props.display_consecutive_in_same_bubble === void 0 && $$bindings.display_consecutive_in_same_bubble && display_consecutive_in_same_bubble !== void 0)\n    $$bindings.display_consecutive_in_same_bubble(display_consecutive_in_same_bubble);\n  if ($$props.current_feedback === void 0 && $$bindings.current_feedback && current_feedback !== void 0)\n    $$bindings.current_feedback(current_feedback);\n  if ($$props.allow_tags === void 0 && $$bindings.allow_tags && allow_tags !== void 0)\n    $$bindings.allow_tags(allow_tags);\n  if ($$props.watermark === void 0 && $$bindings.watermark && watermark !== void 0)\n    $$bindings.watermark(watermark);\n  $$result.css.add(css$4);\n  {\n    if (in_edit_mode && !previous_edit_mode) {\n      const offset = messageElements.length - messages.length;\n      for (let idx = offset; idx < messageElements.length; idx++) {\n        if (idx >= 0) {\n          message_widths[idx - offset] = messageElements[idx]?.clientWidth;\n          message_heights[idx - offset] = messageElements[idx]?.clientHeight;\n        }\n      }\n    }\n  }\n  button_panel_props = {\n    handle_action,\n    likeable: show_like,\n    feedback_options,\n    show_retry,\n    show_undo,\n    show_edit,\n    in_edit_mode,\n    generating,\n    show_copy_button,\n    message: msg_format === \"tuples\" ? messages[0] : messages,\n    position: role === \"user\" ? \"right\" : \"left\",\n    avatar: avatar_img,\n    layout,\n    dispatch,\n    current_feedback,\n    watermark\n  };\n  return `<div class=\"${[\n    \"message-row \" + escape(layout, true) + \" \" + escape(role, true) + \"-row svelte-1csv61q\",\n    (avatar_img !== null ? \"with_avatar\" : \"\") + \" \" + (opposite_avatar_img !== null ? \"with_opposite_avatar\" : \"\")\n  ].join(\" \").trim()}\">${avatar_img !== null ? `<div class=\"avatar-container svelte-1csv61q\">${validate_component(Image, \"Image\").$$render(\n    $$result,\n    {\n      class: \"avatar-image\",\n      src: avatar_img?.url,\n      alt: role + \" avatar\"\n    },\n    {},\n    {}\n  )}</div>` : ``} <div class=\"${[\n    \"flex-wrap svelte-1csv61q\",\n    (role ? \"role\" : \"\") + \" \" + (messages[0].type === \"component\" ? \"component-wrap\" : \"\")\n  ].join(\" \").trim()}\"><div class=\"${[\n    escape(null_to_empty(display_consecutive_in_same_bubble ? role : \"\"), true) + \" svelte-1csv61q\",\n    display_consecutive_in_same_bubble ? \"message\" : \"\"\n  ].join(\" \").trim()}\">${each(messages, (message, thought_index) => {\n    return `<div class=\"${[\n      \"message \" + escape(!display_consecutive_in_same_bubble ? role : \"\", true) + \" svelte-1csv61q\",\n      \"panel-full-width \" + (!render_markdown ? \"message-markdown-disabled\" : \"\") + \" \" + (message.type === \"component\" ? \"component\" : \"\") + \" \" + (is_component_message(message) && message.content.component === \"html\" ? \"html\" : \"\") + \" \" + (thought_index > 0 ? \"thought\" : \"\")\n    ].join(\" \").trim()}\">${in_edit_mode && message.type === \"text\" ? ` <textarea class=\"edit-textarea svelte-1csv61q\" autofocus${add_styles({\n      \"width\": `max(${message_widths[thought_index]}px, 160px)`,\n      \"min-height\": `${message_heights[thought_index]}px`\n    })}>${escape(edit_messages[thought_index] || \"\")}</textarea>` : ` <div${add_attribute(\"data-testid\", role, 0)}${add_attribute(\"dir\", rtl ? \"rtl\" : \"ltr\", 0)}${add_attribute(\"aria-label\", role + \"'s message: \" + get_message_label_data(message), 0)} class=\"${[\n      \"svelte-1csv61q\",\n      (i === value.length - 1 ? \"latest\" : \"\") + \" \" + (!render_markdown ? \"message-markdown-disabled\" : \"\") + \" \" + (selectable ? \"selectable\" : \"\")\n    ].join(\" \").trim()}\"${add_styles({\n      \"user-select\": `text`,\n      \"cursor\": selectable ? \"pointer\" : \"auto\",\n      \"text-align\": rtl ? \"right\" : \"left\"\n    })}${add_attribute(\"this\", messageElements[thought_index], 0)}>${message?.metadata?.title ? `${validate_component(Thought, \"Thought\").$$render(\n      $$result,\n      {\n        thought: message,\n        rtl,\n        sanitize_html,\n        allow_tags,\n        latex_delimiters,\n        render_markdown,\n        _components,\n        upload,\n        thought_index,\n        target,\n        root,\n        theme_mode,\n        _fetch,\n        scroll: scroll2,\n        allow_file_downloads,\n        display_consecutive_in_same_bubble,\n        i18n,\n        line_breaks\n      },\n      {},\n      {}\n    )}` : `${validate_component(MessageContent, \"MessageContent\").$$render(\n      $$result,\n      {\n        message,\n        sanitize_html,\n        allow_tags,\n        latex_delimiters,\n        render_markdown,\n        _components,\n        upload,\n        thought_index,\n        target,\n        root,\n        theme_mode,\n        _fetch,\n        scroll: scroll2,\n        allow_file_downloads,\n        display_consecutive_in_same_bubble,\n        i18n,\n        line_breaks\n      },\n      {},\n      {}\n    )}`} </div>`}</div> ${layout === \"panel\" ? `${validate_component(ButtonPanel, \"ButtonPanel\").$$render($$result, Object.assign({}, button_panel_props, { current_feedback }, { watermark }, { i18n }), {}, {})}` : ``}`;\n  })}</div></div></div> ${layout === \"bubble\" ? `${validate_component(ButtonPanel, \"ButtonPanel\").$$render($$result, Object.assign({}, button_panel_props, { i18n }), {}, {})}` : ``}`;\n});\nconst css$3 = {\n  code: \".container.svelte-134ihlx{display:flex;margin:calc(var(--spacing-xl) * 2)}.bubble.pending.svelte-134ihlx{border-width:1px;border-radius:var(--radius-lg);border-bottom-left-radius:0;border-color:var(--border-color-primary);background-color:var(--background-fill-secondary);box-shadow:var(--shadow-drop);align-self:flex-start;width:fit-content;margin-bottom:var(--spacing-xl)}.bubble.with_opposite_avatar.svelte-134ihlx{margin-right:calc(var(--spacing-xxl) + 35px + var(--spacing-xxl))}.panel.pending.svelte-134ihlx{margin:0;padding:calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);width:100%;border:none;background:none;box-shadow:none;border-radius:0}.panel.with_avatar.svelte-134ihlx{padding-left:calc(var(--spacing-xl) * 2) !important;padding-right:calc(var(--spacing-xl) * 2) !important}.avatar-container.svelte-134ihlx{align-self:flex-start;position:relative;display:flex;justify-content:flex-start;align-items:flex-start;width:35px;height:35px;flex-shrink:0;bottom:0;border-radius:50%;border:1px solid var(--border-color-primary);margin-right:var(--spacing-xxl)}.avatar-container.svelte-134ihlx:not(.thumbnail-item) img{width:100%;height:100%;object-fit:cover;border-radius:50%;padding:var(--size-1-5)}.message-content.svelte-134ihlx{padding:var(--spacing-sm) var(--spacing-xl);min-height:var(--size-8);display:flex;align-items:center}.dots.svelte-134ihlx{display:flex;gap:var(--spacing-xs);align-items:center}.dot.svelte-134ihlx{width:var(--size-1-5);height:var(--size-1-5);margin-right:var(--spacing-xs);border-radius:50%;background-color:var(--body-text-color);opacity:0.5;animation:svelte-134ihlx-pulse 1.5s infinite}.dot.svelte-134ihlx:nth-child(2){animation-delay:0.2s}.dot.svelte-134ihlx:nth-child(3){animation-delay:0.4s}@keyframes svelte-134ihlx-pulse{0%,100%{opacity:0.4;transform:scale(1)}50%{opacity:1;transform:scale(1.1)}}\",\n  map: '{\"version\":3,\"file\":\"Pending.svelte\",\"sources\":[\"Pending.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let avatar_images = [null, null];\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"container\\\\\">\\\\n\\\\t{#if avatar_images[1] !== null}\\\\n\\\\t\\\\t<div class=\\\\\"avatar-container\\\\\">\\\\n\\\\t\\\\t\\\\t<Image class=\\\\\"avatar-image\\\\\" src={avatar_images[1].url} alt=\\\\\"bot avatar\\\\\" />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"message bot pending {layout}\\\\\"\\\\n\\\\t\\\\tclass:with_avatar={avatar_images[1] !== null}\\\\n\\\\t\\\\tclass:with_opposite_avatar={avatar_images[0] !== null}\\\\n\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\taria-label=\\\\\"Loading response\\\\\"\\\\n\\\\t\\\\taria-live=\\\\\"polite\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<div class=\\\\\"message-content\\\\\">\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"sr-only\\\\\">Loading content</span>\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"dots\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tmargin: calc(var(--spacing-xl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.pending {\\\\n\\\\t\\\\tborder-width: 1px;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tborder-bottom-left-radius: 0;\\\\n\\\\t\\\\tborder-color: var(--border-color-primary);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble.with_opposite_avatar {\\\\n\\\\t\\\\tmargin-right: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.pending {\\\\n\\\\t\\\\tmargin: 0;\\\\n\\\\t\\\\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tbackground: none;\\\\n\\\\t\\\\tbox-shadow: none;\\\\n\\\\t\\\\tborder-radius: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.panel.with_avatar {\\\\n\\\\t\\\\tpadding-left: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t\\\\tpadding-right: calc(var(--spacing-xl) * 2) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container {\\\\n\\\\t\\\\talign-self: flex-start;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: flex-start;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\twidth: 35px;\\\\n\\\\t\\\\theight: 35px;\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tmargin-right: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.avatar-container:not(.thumbnail-item) :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tpadding: var(--size-1-5);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-content {\\\\n\\\\t\\\\tpadding: var(--spacing-sm) var(--spacing-xl);\\\\n\\\\t\\\\tmin-height: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.dots {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tgap: var(--spacing-xs);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.dot {\\\\n\\\\t\\\\twidth: var(--size-1-5);\\\\n\\\\t\\\\theight: var(--size-1-5);\\\\n\\\\t\\\\tmargin-right: var(--spacing-xs);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tbackground-color: var(--body-text-color);\\\\n\\\\t\\\\topacity: 0.5;\\\\n\\\\t\\\\tanimation: pulse 1.5s infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t.dot:nth-child(2) {\\\\n\\\\t\\\\tanimation-delay: 0.2s;\\\\n\\\\t}\\\\n\\\\n\\\\t.dot:nth-child(3) {\\\\n\\\\t\\\\tanimation-delay: 0.4s;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes pulse {\\\\n\\\\t\\\\t0%,\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\topacity: 0.4;\\\\n\\\\t\\\\t\\\\ttransform: scale(1);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\topacity: 1;\\\\n\\\\t\\\\t\\\\ttransform: scale(1.1);\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAgCC,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CACnC,CAEA,OAAO,uBAAS,CACf,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,yBAAyB,CAAE,CAAC,CAC5B,YAAY,CAAE,IAAI,sBAAsB,CAAC,CACzC,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CAAE,UAAU,CACtB,KAAK,CAAE,WAAW,CAClB,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,OAAO,oCAAsB,CAC5B,YAAY,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAClE,CAEA,MAAM,uBAAS,CACd,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAChE,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,CAChB,CAEA,MAAM,2BAAa,CAClB,YAAY,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CACpD,aAAa,CAAE,KAAK,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAC5C,CAEA,gCAAkB,CACjB,UAAU,CAAE,UAAU,CACtB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,UAAU,CAC3B,WAAW,CAAE,UAAU,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,MAAM,CAAE,CAAC,CACT,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,YAAY,CAAE,IAAI,aAAa,CAChC,CAEA,gCAAiB,KAAK,eAAe,CAAC,CAAS,GAAK,CACnD,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,GAAG,CAClB,OAAO,CAAE,IAAI,UAAU,CACxB,CAEA,+BAAiB,CAChB,OAAO,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAC5C,UAAU,CAAE,IAAI,QAAQ,CAAC,CACzB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MACd,CAEA,oBAAM,CACL,OAAO,CAAE,IAAI,CACb,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,WAAW,CAAE,MACd,CAEA,mBAAK,CACJ,KAAK,CAAE,IAAI,UAAU,CAAC,CACtB,MAAM,CAAE,IAAI,UAAU,CAAC,CACvB,YAAY,CAAE,IAAI,YAAY,CAAC,CAC/B,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,OAAO,CAAE,GAAG,CACZ,SAAS,CAAE,oBAAK,CAAC,IAAI,CAAC,QACvB,CAEA,mBAAI,WAAW,CAAC,CAAE,CACjB,eAAe,CAAE,IAClB,CAEA,mBAAI,WAAW,CAAC,CAAE,CACjB,eAAe,CAAE,IAClB,CAEA,WAAW,oBAAM,CAChB,EAAE,CACF,IAAK,CACJ,OAAO,CAAE,GAAG,CACZ,SAAS,CAAE,MAAM,CAAC,CACnB,CACA,GAAI,CACH,OAAO,CAAE,CAAC,CACV,SAAS,CAAE,MAAM,GAAG,CACrB,CACD\"}'\n};\nconst Pending = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { layout = \"bubble\" } = $$props;\n  let { avatar_images = [null, null] } = $$props;\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.avatar_images === void 0 && $$bindings.avatar_images && avatar_images !== void 0)\n    $$bindings.avatar_images(avatar_images);\n  $$result.css.add(css$3);\n  return `<div class=\"container svelte-134ihlx\">${avatar_images[1] !== null ? `<div class=\"avatar-container svelte-134ihlx\">${validate_component(Image, \"Image\").$$render(\n    $$result,\n    {\n      class: \"avatar-image\",\n      src: avatar_images[1].url,\n      alt: \"bot avatar\"\n    },\n    {},\n    {}\n  )}</div>` : ``} <div class=\"${[\n    \"message bot pending \" + escape(layout, true) + \" svelte-134ihlx\",\n    (avatar_images[1] !== null ? \"with_avatar\" : \"\") + \" \" + (avatar_images[0] !== null ? \"with_opposite_avatar\" : \"\")\n  ].join(\" \").trim()}\" role=\"status\" aria-label=\"Loading response\" aria-live=\"polite\"><div class=\"message-content svelte-134ihlx\" data-svelte-h=\"svelte-1vfby8\"><span class=\"sr-only\">Loading content</span> <div class=\"dots svelte-134ihlx\"><div class=\"dot svelte-134ihlx\"></div> <div class=\"dot svelte-134ihlx\"></div> <div class=\"dot svelte-134ihlx\"></div></div></div></div> </div>`;\n});\nconst css$2 = {\n  code: \".placeholder-content.svelte-9pi8y1{display:flex;flex-direction:column;height:100%}.placeholder.svelte-9pi8y1{align-items:center;display:flex;justify-content:center;height:100%;flex-grow:1}.examples.svelte-9pi8y1 img{pointer-events:none}.examples.svelte-9pi8y1{margin:auto;padding:var(--spacing-xxl);display:grid;grid-template-columns:repeat(auto-fit, minmax(240px, 1fr));gap:var(--spacing-xl);max-width:calc(min(4 * 240px + 5 * var(--spacing-xxl), 100%))}.example.svelte-9pi8y1{display:flex;flex-direction:column;align-items:flex-start;padding:var(--spacing-xxl);border:none;border-radius:var(--radius-lg);background-color:var(--block-background-fill);cursor:pointer;transition:all 150ms ease-in-out;width:100%;gap:var(--spacing-sm);border:var(--block-border-width) solid var(--block-border-color);transform:translateY(0px)}.example.svelte-9pi8y1:hover{transform:translateY(-2px);background-color:var(--color-accent-soft)}.example-content.svelte-9pi8y1{display:flex;flex-direction:column;align-items:flex-start;width:100%;height:100%}.example-text-content.svelte-9pi8y1{margin-top:auto;text-align:left}.example-text.svelte-9pi8y1{font-size:var(--text-md);text-align:left;overflow:hidden;text-overflow:ellipsis}.example-icons-grid.svelte-9pi8y1{display:flex;gap:var(--spacing-sm);margin-bottom:var(--spacing-lg);width:100%}.example-icon.svelte-9pi8y1{flex-shrink:0;width:var(--size-8);height:var(--size-8);display:flex;align-items:center;justify-content:center;border-radius:var(--radius-lg);border:var(--block-border-width) solid var(--block-border-color);background-color:var(--block-background-fill);position:relative}.example-icon.svelte-9pi8y1 svg{width:var(--size-4);height:var(--size-4);color:var(--color-text-secondary)}.text-icon-aa.svelte-9pi8y1{font-size:var(--text-sm);font-weight:var(--weight-semibold);color:var(--color-text-secondary);line-height:1}.example-image-container.svelte-9pi8y1{width:var(--size-8);height:var(--size-8);border-radius:var(--radius-lg);overflow:hidden;position:relative;margin-bottom:var(--spacing-lg)}.example-image-container.svelte-9pi8y1 img{width:100%;height:100%;object-fit:cover}.image-overlay.svelte-9pi8y1{position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0, 0, 0, 0.6);color:white;display:flex;align-items:center;justify-content:center;font-size:var(--text-lg);font-weight:var(--weight-semibold);border-radius:var(--radius-lg)}.file-overlay.svelte-9pi8y1{position:absolute;inset:0;background:rgba(0, 0, 0, 0.6);color:white;display:flex;align-items:center;justify-content:center;font-size:var(--text-sm);font-weight:var(--weight-semibold);border-radius:var(--radius-lg)}\",\n  map: '{\"version\":3,\"file\":\"Examples.svelte\",\"sources\":[\"Examples.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport { MarkdownCode as Markdown } from \\\\\"@gradio/markdown-code\\\\\";\\\\nimport { File, Music, Video } from \\\\\"@gradio/icons\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nexport let examples = null;\\\\nexport let placeholder = null;\\\\nexport let latex_delimiters;\\\\nexport let root;\\\\nconst dispatch = createEventDispatcher();\\\\nfunction handle_example_select(i, example) {\\\\n    const example_obj = typeof example === \\\\\"string\\\\\" ? { text: example } : example;\\\\n    dispatch(\\\\\"example_select\\\\\", {\\\\n        index: i,\\\\n        value: { text: example_obj.text, files: example_obj.files }\\\\n    });\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"placeholder-content\\\\\" role=\\\\\"complementary\\\\\">\\\\n\\\\t{#if placeholder !== null}\\\\n\\\\t\\\\t<div class=\\\\\"placeholder\\\\\">\\\\n\\\\t\\\\t\\\\t<Markdown message={placeholder} {latex_delimiters} {root} />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t{#if examples !== null}\\\\n\\\\t\\\\t<div class=\\\\\"examples\\\\\" role=\\\\\"list\\\\\">\\\\n\\\\t\\\\t\\\\t{#each examples as example, i}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_example_select(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ti,\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttypeof example === \\\\\"string\\\\\" ? { text: example } : example\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`Select example ${i + 1}: ${example.display_text || example.text}`}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-content\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example?.icon?.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.icon.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"Example icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example?.icon?.mime_type === \\\\\"text\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-icon\\\\\" aria-hidden=\\\\\"true\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"text-icon-aa\\\\\">Aa</span>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files !== undefined && example.files.length > 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example.files.length > 1}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icons-grid\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"group\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"Example attachments\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#each example.files.slice(0, 4) as file, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if file.mime_type?.includes(\\\\\"image\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={file.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt={file.orig_name || `Example image ${i + 1}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if i === 3 && example.files.length > 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"image-overlay\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`${example.files.length - 4} more files`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t+{example.files.length - 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if file.mime_type?.includes(\\\\\"video\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<video\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={file.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-hidden=\\\\\"true\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if i === 3 && example.files.length > 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"image-overlay\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`${example.files.length - 4} more files`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t+{example.files.length - 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`File: ${file.orig_name}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if file.mime_type?.includes(\\\\\"audio\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Music />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if example.files.length > 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"file-overlay\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"status\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`${example.files.length - 4} more files`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t+{example.files.length - 4}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files[0].mime_type?.includes(\\\\\"image\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.files[0].url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt={example.files[0].orig_name || \\\\\"Example image\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files[0].mime_type?.includes(\\\\\"video\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-image-container\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<video\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={example.files[0].url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-hidden=\\\\\"true\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if example.files[0].mime_type?.includes(\\\\\"audio\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`File: ${example.files[0].orig_name}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Music />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"example-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={`File: ${example.files[0].orig_name}`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"example-text-content\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"example-text\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>{example.display_text || example.text}</span\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.placeholder-content {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.placeholder {\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.examples :global(img) {\\\\n\\\\t\\\\tpointer-events: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.examples {\\\\n\\\\t\\\\tmargin: auto;\\\\n\\\\t\\\\tpadding: var(--spacing-xxl);\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tgrid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\\\\n\\\\t\\\\tgap: var(--spacing-xl);\\\\n\\\\t\\\\tmax-width: calc(min(4 * 240px + 5 * var(--spacing-xxl), 100%));\\\\n\\\\t}\\\\n\\\\n\\\\t.example {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\tpadding: var(--spacing-xxl);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\ttransition: all 150ms ease-in-out;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tborder: var(--block-border-width) solid var(--block-border-color);\\\\n\\\\t\\\\ttransform: translateY(0px);\\\\n\\\\t}\\\\n\\\\n\\\\t.example:hover {\\\\n\\\\t\\\\ttransform: translateY(-2px);\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-content {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-text-content {\\\\n\\\\t\\\\tmargin-top: auto;\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-text {\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-icons-grid {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-lg);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-icon {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tborder: var(--block-border-width) solid var(--block-border-color);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-icon :global(svg) {\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\tcolor: var(--color-text-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.text-icon-aa {\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t\\\\tcolor: var(--color-text-secondary);\\\\n\\\\t\\\\tline-height: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.example-image-container {\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.example-image-container :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.image-overlay {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tright: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tbackground: rgba(0, 0, 0, 0.6);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.file-overlay {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tinset: 0;\\\\n\\\\t\\\\tbackground: rgba(0, 0, 0, 0.6);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAkKC,kCAAqB,CACpB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,MAAM,CAAE,IACT,CAEA,0BAAa,CACZ,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,CACZ,CAEA,uBAAS,CAAS,GAAK,CACtB,cAAc,CAAE,IACjB,CAEA,uBAAU,CACT,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9D,CAEA,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,UAAU,CACvB,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CACjC,KAAK,CAAE,IAAI,CACX,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CACjE,SAAS,CAAE,WAAW,GAAG,CAC1B,CAEA,sBAAQ,MAAO,CACd,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,gBAAgB,CAAE,IAAI,mBAAmB,CAC1C,CAEA,8BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,UAAU,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,mCAAsB,CACrB,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IACb,CAEA,2BAAc,CACb,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CAAE,IAAI,CAChB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,iCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,aAAa,CAAE,IAAI,YAAY,CAAC,CAChC,KAAK,CAAE,IACR,CAEA,2BAAc,CACb,WAAW,CAAE,CAAC,CACd,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CACjE,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,QAAQ,CAAE,QACX,CAEA,2BAAa,CAAS,GAAK,CAC1B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,sBAAsB,CAClC,CAEA,2BAAc,CACb,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,KAAK,CAAE,IAAI,sBAAsB,CAAC,CAClC,WAAW,CAAE,CACd,CAEA,sCAAyB,CACxB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,QAAQ,CAClB,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,sCAAwB,CAAS,GAAK,CACrC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,KACb,CAEA,4BAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,CAAC,CACT,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAC9B,KAAK,CAAE,KAAK,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,2BAAc,CACb,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,CAAC,CACR,UAAU,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAC9B,KAAK,CAAE,KAAK,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,aAAa,CAAE,IAAI,WAAW,CAC/B\"}'\n};\nconst Examples = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { examples = null } = $$props;\n  let { placeholder = null } = $$props;\n  let { latex_delimiters } = $$props;\n  let { root } = $$props;\n  createEventDispatcher();\n  if ($$props.examples === void 0 && $$bindings.examples && examples !== void 0)\n    $$bindings.examples(examples);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  $$result.css.add(css$2);\n  return `<div class=\"placeholder-content svelte-9pi8y1\" role=\"complementary\">${placeholder !== null ? `<div class=\"placeholder svelte-9pi8y1\">${validate_component(MarkdownCode, \"Markdown\").$$render(\n    $$result,\n    {\n      message: placeholder,\n      latex_delimiters,\n      root\n    },\n    {},\n    {}\n  )}</div>` : ``} ${examples !== null ? `<div class=\"examples svelte-9pi8y1\" role=\"list\">${each(examples, (example, i) => {\n    return `<button class=\"example svelte-9pi8y1\"${add_attribute(\"aria-label\", `Select example ${i + 1}: ${example.display_text || example.text}`, 0)}><div class=\"example-content svelte-9pi8y1\">${example?.icon?.url ? `<div class=\"example-image-container svelte-9pi8y1\">${validate_component(Image, \"Image\").$$render(\n      $$result,\n      {\n        class: \"example-image\",\n        src: example.icon.url,\n        alt: \"Example icon\"\n      },\n      {},\n      {}\n    )} </div>` : `${example?.icon?.mime_type === \"text\" ? `<div class=\"example-icon svelte-9pi8y1\" aria-hidden=\"true\" data-svelte-h=\"svelte-15cq9iz\"><span class=\"text-icon-aa svelte-9pi8y1\">Aa</span> </div>` : `${example.files !== void 0 && example.files.length > 0 ? `${example.files.length > 1 ? `<div class=\"example-icons-grid svelte-9pi8y1\" role=\"group\" aria-label=\"Example attachments\">${each(example.files.slice(0, 4), (file, i2) => {\n      return `${file.mime_type?.includes(\"image\") ? `<div class=\"example-image-container svelte-9pi8y1\">${validate_component(Image, \"Image\").$$render(\n        $$result,\n        {\n          class: \"example-image\",\n          src: file.url,\n          alt: file.orig_name || `Example image ${i2 + 1}`\n        },\n        {},\n        {}\n      )} ${i2 === 3 && example.files.length > 4 ? `<div class=\"image-overlay svelte-9pi8y1\" role=\"status\"${add_attribute(\"aria-label\", `${example.files.length - 4} more files`, 0)}>+${escape(example.files.length - 4)} </div>` : ``} </div>` : `${file.mime_type?.includes(\"video\") ? `<div class=\"example-image-container svelte-9pi8y1\"><video class=\"example-image\"${add_attribute(\"src\", file.url, 0)} aria-hidden=\"true\"></video> ${i2 === 3 && example.files.length > 4 ? `<div class=\"image-overlay svelte-9pi8y1\" role=\"status\"${add_attribute(\"aria-label\", `${example.files.length - 4} more files`, 0)}>+${escape(example.files.length - 4)} </div>` : ``} </div>` : `<div class=\"example-icon svelte-9pi8y1\"${add_attribute(\"aria-label\", `File: ${file.orig_name}`, 0)}>${file.mime_type?.includes(\"audio\") ? `${validate_component(Music, \"Music\").$$render($$result, {}, {}, {})}` : `${validate_component(File, \"File\").$$render($$result, {}, {}, {})}`} </div>`}`}`;\n    })} ${example.files.length > 4 ? `<div class=\"example-icon svelte-9pi8y1\"><div class=\"file-overlay svelte-9pi8y1\" role=\"status\"${add_attribute(\"aria-label\", `${example.files.length - 4} more files`, 0)}>+${escape(example.files.length - 4)}</div> </div>` : ``} </div>` : `${example.files[0].mime_type?.includes(\"image\") ? `<div class=\"example-image-container svelte-9pi8y1\">${validate_component(Image, \"Image\").$$render(\n      $$result,\n      {\n        class: \"example-image\",\n        src: example.files[0].url,\n        alt: example.files[0].orig_name || \"Example image\"\n      },\n      {},\n      {}\n    )} </div>` : `${example.files[0].mime_type?.includes(\"video\") ? `<div class=\"example-image-container svelte-9pi8y1\"><video class=\"example-image\"${add_attribute(\"src\", example.files[0].url, 0)} aria-hidden=\"true\"></video> </div>` : `${example.files[0].mime_type?.includes(\"audio\") ? `<div class=\"example-icon svelte-9pi8y1\"${add_attribute(\"aria-label\", `File: ${example.files[0].orig_name}`, 0)}>${validate_component(Music, \"Music\").$$render($$result, {}, {}, {})} </div>` : `<div class=\"example-icon svelte-9pi8y1\"${add_attribute(\"aria-label\", `File: ${example.files[0].orig_name}`, 0)}>${validate_component(File, \"File\").$$render($$result, {}, {}, {})} </div>`}`}`}`}` : ``}`}`} <div class=\"example-text-content svelte-9pi8y1\"><span class=\"example-text svelte-9pi8y1\">${escape(example.display_text || example.text)}</span> </div></div> </button>`;\n  })}</div>` : ``} </div>`;\n});\nconst CopyAll = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { watermark = null } = $$props;\n  onDestroy(() => {\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.watermark === void 0 && $$bindings.watermark && watermark !== void 0)\n    $$bindings.watermark(watermark);\n  return `${validate_component(IconButton, \"IconButton\").$$render(\n    $$result,\n    {\n      Icon: Copy,\n      label: \"Copy conversation\"\n    },\n    {},\n    {}\n  )}`;\n});\nconst css$1 = {\n  code: \".panel-wrap.svelte-gjtrl6.svelte-gjtrl6{width:100%;overflow-y:auto}.bubble-wrap.svelte-gjtrl6.svelte-gjtrl6{width:100%;overflow-y:auto;height:100%;padding-top:var(--spacing-xxl)}@media(prefers-color-scheme: dark){.bubble-wrap.svelte-gjtrl6.svelte-gjtrl6{background:var(--background-fill-secondary)}}.message-wrap.svelte-gjtrl6 .prose.chatbot.md{opacity:0.8;overflow-wrap:break-word}.message-wrap.svelte-gjtrl6 .message-row .md img{border-radius:var(--radius-xl);margin:var(--size-2);width:400px;max-width:30vw;max-height:30vw}.message-wrap.svelte-gjtrl6 .message a{color:var(--color-text-link);text-decoration:underline}.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) table,.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) tr,.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) td,.message-wrap.svelte-gjtrl6 .bot:not(:has(.table-wrap)) th{border:1px solid var(--border-color-primary)}.message-wrap.svelte-gjtrl6 .user table,.message-wrap.svelte-gjtrl6 .user tr,.message-wrap.svelte-gjtrl6 .user td,.message-wrap.svelte-gjtrl6 .user th{border:1px solid var(--border-color-accent)}.message-wrap.svelte-gjtrl6 span.katex{font-size:var(--text-lg);direction:ltr}.message-wrap.svelte-gjtrl6 span.katex-display{margin-top:0}.message-wrap.svelte-gjtrl6 pre{position:relative}.message-wrap.svelte-gjtrl6 .grid-wrap{max-height:80% !important;max-width:600px;object-fit:contain}.message-wrap.svelte-gjtrl6>div.svelte-gjtrl6 p:not(:first-child){margin-top:var(--spacing-xxl)}.message-wrap.svelte-gjtrl6.svelte-gjtrl6{display:flex;flex-direction:column;justify-content:space-between;margin-bottom:var(--spacing-xxl)}.panel-wrap.svelte-gjtrl6 .message-row:first-child{padding-top:calc(var(--spacing-xxl) * 2)}.scroll-down-button-container.svelte-gjtrl6.svelte-gjtrl6{position:absolute;bottom:10px;left:50%;transform:translateX(-50%);z-index:var(--layer-top)}.scroll-down-button-container.svelte-gjtrl6 button{border-radius:50%;box-shadow:var(--shadow-drop);transition:box-shadow 0.2s ease-in-out,\\n\t\t\ttransform 0.2s ease-in-out}.scroll-down-button-container.svelte-gjtrl6 button:hover{box-shadow:var(--shadow-drop),\\n\t\t\t0 2px 2px rgba(0, 0, 0, 0.05);transform:translateY(-2px)}.options.svelte-gjtrl6.svelte-gjtrl6{margin-left:auto;padding:var(--spacing-xxl);display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:var(--spacing-xxl);max-width:calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));justify-content:end}.option.svelte-gjtrl6.svelte-gjtrl6{display:flex;flex-direction:column;align-items:center;padding:var(--spacing-xl);border:1px dashed var(--border-color-primary);border-radius:var(--radius-md);background-color:var(--background-fill-secondary);cursor:pointer;transition:var(--button-transition);max-width:var(--size-56);width:100%;justify-content:center}.option.svelte-gjtrl6.svelte-gjtrl6:hover{background-color:var(--color-accent-soft);border-color:var(--border-color-accent)}\",\n  map: '{\"version\":3,\"file\":\"ChatBot.svelte\",\"sources\":[\"ChatBot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { format_chat_for_sharing, is_last_bot_message, group_messages, load_components, get_components_from_messages } from \\\\\"./utils\\\\\";\\\\nimport { copy } from \\\\\"@gradio/utils\\\\\";\\\\nimport Message from \\\\\"./Message.svelte\\\\\";\\\\nimport { dequal } from \\\\\"dequal/lite\\\\\";\\\\nimport { createEventDispatcher, tick, onMount } from \\\\\"svelte\\\\\";\\\\nimport { Trash, Community, ScrollDownArrow } from \\\\\"@gradio/icons\\\\\";\\\\nimport { IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport Pending from \\\\\"./Pending.svelte\\\\\";\\\\nimport { ShareError } from \\\\\"@gradio/utils\\\\\";\\\\nimport { Gradio } from \\\\\"@gradio/utils\\\\\";\\\\nimport Examples from \\\\\"./Examples.svelte\\\\\";\\\\nexport let value = [];\\\\nlet old_value = null;\\\\nimport CopyAll from \\\\\"./CopyAll.svelte\\\\\";\\\\nexport let _fetch;\\\\nexport let load_component;\\\\nexport let allow_file_downloads;\\\\nexport let display_consecutive_in_same_bubble;\\\\nlet _components = {};\\\\nconst is_browser = typeof window !== \\\\\"undefined\\\\\";\\\\nasync function update_components() {\\\\n    _components = await load_components(get_components_from_messages(value), _components, load_component);\\\\n}\\\\n$: value, update_components();\\\\nexport let latex_delimiters;\\\\nexport let pending_message = false;\\\\nexport let generating = false;\\\\nexport let selectable = false;\\\\nexport let likeable = false;\\\\nexport let feedback_options;\\\\nexport let feedback_value = null;\\\\nexport let editable = null;\\\\nexport let show_share_button = false;\\\\nexport let show_copy_all_button = false;\\\\nexport let rtl = false;\\\\nexport let show_copy_button = false;\\\\nexport let avatar_images = [null, null];\\\\nexport let sanitize_html = true;\\\\nexport let render_markdown = true;\\\\nexport let line_breaks = true;\\\\nexport let autoscroll = true;\\\\nexport let theme_mode;\\\\nexport let i18n;\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let placeholder = null;\\\\nexport let upload;\\\\nexport let msg_format = \\\\\"tuples\\\\\";\\\\nexport let examples = null;\\\\nexport let _retryable = false;\\\\nexport let _undoable = false;\\\\nexport let like_user_message = false;\\\\nexport let root;\\\\nexport let allow_tags = false;\\\\nexport let watermark = null;\\\\nexport let show_progress = \\\\\"full\\\\\";\\\\nlet target = null;\\\\nlet edit_index = null;\\\\nlet edit_messages = [];\\\\nonMount(() => {\\\\n    target = document.querySelector(\\\\\"div.gradio-container\\\\\");\\\\n});\\\\nlet div;\\\\nlet show_scroll_button = false;\\\\nconst dispatch = createEventDispatcher();\\\\nfunction is_at_bottom() {\\\\n    return div && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\\\\n}\\\\nfunction scroll_to_bottom() {\\\\n    if (!div)\\\\n        return;\\\\n    div.scrollTo(0, div.scrollHeight);\\\\n    show_scroll_button = false;\\\\n}\\\\nlet scroll_after_component_load = false;\\\\nasync function scroll_on_value_update() {\\\\n    if (!autoscroll)\\\\n        return;\\\\n    if (is_at_bottom()) {\\\\n        scroll_after_component_load = true;\\\\n        await tick();\\\\n        await new Promise((resolve) => setTimeout(resolve, 300));\\\\n        scroll_to_bottom();\\\\n    }\\\\n}\\\\nonMount(() => {\\\\n    if (autoscroll) {\\\\n        scroll_to_bottom();\\\\n    }\\\\n    scroll_on_value_update();\\\\n});\\\\n$: if (value || pending_message || _components) {\\\\n    scroll_on_value_update();\\\\n}\\\\nonMount(() => {\\\\n    function handle_scroll() {\\\\n        if (is_at_bottom()) {\\\\n            show_scroll_button = false;\\\\n        }\\\\n        else {\\\\n            scroll_after_component_load = false;\\\\n            show_scroll_button = true;\\\\n        }\\\\n    }\\\\n    div?.addEventListener(\\\\\"scroll\\\\\", handle_scroll);\\\\n    return () => {\\\\n        div?.removeEventListener(\\\\\"scroll\\\\\", handle_scroll);\\\\n    };\\\\n});\\\\n$: {\\\\n    if (!dequal(value, old_value)) {\\\\n        old_value = value;\\\\n        dispatch(\\\\\"change\\\\\");\\\\n    }\\\\n}\\\\n$: groupedMessages = value && group_messages(value, msg_format);\\\\n$: options = value && get_last_bot_options();\\\\nfunction handle_action(i, message, selected) {\\\\n    if (selected === \\\\\"undo\\\\\" || selected === \\\\\"retry\\\\\") {\\\\n        const val_ = value;\\\\n        let last_index = val_.length - 1;\\\\n        while (val_[last_index].role === \\\\\"assistant\\\\\") {\\\\n            last_index--;\\\\n        }\\\\n        dispatch(selected, {\\\\n            index: val_[last_index].index,\\\\n            value: val_[last_index].content\\\\n        });\\\\n    }\\\\n    else if (selected == \\\\\"edit\\\\\") {\\\\n        edit_index = i;\\\\n        edit_messages.push(message.content);\\\\n    }\\\\n    else if (selected == \\\\\"edit_cancel\\\\\") {\\\\n        edit_index = null;\\\\n    }\\\\n    else if (selected == \\\\\"edit_submit\\\\\") {\\\\n        edit_index = null;\\\\n        dispatch(\\\\\"edit\\\\\", {\\\\n            index: message.index,\\\\n            value: edit_messages[i].slice(),\\\\n            previous_value: message.content\\\\n        });\\\\n    }\\\\n    else {\\\\n        let feedback = selected === \\\\\"Like\\\\\" ? true : selected === \\\\\"Dislike\\\\\" ? false : selected || \\\\\"\\\\\";\\\\n        if (msg_format === \\\\\"tuples\\\\\") {\\\\n            dispatch(\\\\\"like\\\\\", {\\\\n                index: message.index,\\\\n                value: message.content,\\\\n                liked: feedback\\\\n            });\\\\n        }\\\\n        else {\\\\n            if (!groupedMessages)\\\\n                return;\\\\n            const message_group = groupedMessages[i];\\\\n            const [first, last] = [\\\\n                message_group[0],\\\\n                message_group[message_group.length - 1]\\\\n            ];\\\\n            dispatch(\\\\\"like\\\\\", {\\\\n                index: first.index,\\\\n                value: message_group.map((m) => m.content),\\\\n                liked: feedback\\\\n            });\\\\n        }\\\\n    }\\\\n}\\\\nfunction get_last_bot_options() {\\\\n    if (!value || !groupedMessages || groupedMessages.length === 0)\\\\n        return void 0;\\\\n    const last_group = groupedMessages[groupedMessages.length - 1];\\\\n    if (last_group[0].role !== \\\\\"assistant\\\\\")\\\\n        return void 0;\\\\n    return last_group[last_group.length - 1].options;\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if value !== null && value.length > 0}\\\\n\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t{#if show_share_button}\\\\n\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Community}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={async () => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ttry {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t// @ts-ignore\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconst formatted = await format_chat_for_sharing(value);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"share\\\\\", {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdescription: formatted\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t});\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t} catch (e) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconsole.error(e);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlet message = e instanceof ShareError ? e.message : \\\\\"Share failed.\\\\\";\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"error\\\\\", message);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={Trash}\\\\n\\\\t\\\\t\\\\ton:click={() => dispatch(\\\\\"clear\\\\\")}\\\\n\\\\t\\\\t\\\\tlabel={i18n(\\\\\"chatbot.clear\\\\\")}\\\\n\\\\t\\\\t></IconButton>\\\\n\\\\t\\\\t{#if show_copy_all_button}\\\\n\\\\t\\\\t\\\\t<CopyAll {value} {watermark} />\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</IconButtonWrapper>\\\\n{/if}\\\\n\\\\n<div\\\\n\\\\tclass={layout === \\\\\"bubble\\\\\" ? \\\\\"bubble-wrap\\\\\" : \\\\\"panel-wrap\\\\\"}\\\\n\\\\tbind:this={div}\\\\n\\\\trole=\\\\\"log\\\\\"\\\\n\\\\taria-label=\\\\\"chatbot conversation\\\\\"\\\\n\\\\taria-live=\\\\\"polite\\\\\"\\\\n>\\\\n\\\\t{#if value !== null && value.length > 0 && groupedMessages !== null}\\\\n\\\\t\\\\t<div class=\\\\\"message-wrap\\\\\" use:copy>\\\\n\\\\t\\\\t\\\\t{#each groupedMessages as messages, i}\\\\n\\\\t\\\\t\\\\t\\\\t{@const role = messages[0].role === \\\\\"user\\\\\" ? \\\\\"user\\\\\" : \\\\\"bot\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t{@const avatar_img = avatar_images[role === \\\\\"user\\\\\" ? 0 : 1]}\\\\n\\\\t\\\\t\\\\t\\\\t{@const opposite_avatar_img = avatar_images[role === \\\\\"user\\\\\" ? 0 : 1]}\\\\n\\\\t\\\\t\\\\t\\\\t{@const feedback_index = groupedMessages\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.slice(0, i)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t.filter((m) => m[0].role === \\\\\"assistant\\\\\").length}\\\\n\\\\t\\\\t\\\\t\\\\t{@const current_feedback =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trole === \\\\\"bot\\\\\" && feedback_value && feedback_value[feedback_index]\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? feedback_value[feedback_index]\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: null}\\\\n\\\\t\\\\t\\\\t\\\\t<Message\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{messages}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{display_consecutive_in_same_bubble}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{opposite_avatar_img}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{avatar_img}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{role}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{layout}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{dispatch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{_fetch}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{target}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{selectable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{_components}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{generating}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{msg_format}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{feedback_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{current_feedback}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_like={role === \\\\\"user\\\\\" ? likeable && like_user_message : likeable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_retry={_retryable && is_last_bot_message(messages, value)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_undo={_undoable && is_last_bot_message(messages, value)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tshow_edit={editable === \\\\\"all\\\\\" ||\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(editable == \\\\\"user\\\\\" &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole === \\\\\"user\\\\\" &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessages.length > 0 &&\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessages[messages.length - 1].type == \\\\\"text\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tin_edit_mode={edit_index === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tbind:edit_messages\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{show_copy_button}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\thandle_action={(selected) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (selected == \\\\\"edit\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tedit_messages.splice(0, edit_messages.length);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (selected === \\\\\"edit\\\\\" || selected === \\\\\"edit_submit\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmessages.forEach((msg, index) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_action(selected === \\\\\"edit\\\\\" ? i : index, msg, selected);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t});\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t} else {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_action(i, messages[0], selected);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tscroll={is_browser ? scroll : () => {}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:copy={(e) => dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{#if show_progress !== \\\\\"hidden\\\\\" && generating && messages[messages.length - 1].role === \\\\\"assistant\\\\\" && messages[messages.length - 1].metadata?.status === \\\\\"done\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Pending {layout} {avatar_images} />\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{#if show_progress !== \\\\\"hidden\\\\\" && pending_message}\\\\n\\\\t\\\\t\\\\t\\\\t<Pending {layout} {avatar_images} />\\\\n\\\\t\\\\t\\\\t{:else if options}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"options\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each options as option, index}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"option\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"option_select\\\\\", {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tindex: index,\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue: option.value\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t})}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{option.label || option.value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<Examples\\\\n\\\\t\\\\t\\\\t{examples}\\\\n\\\\t\\\\t\\\\t{placeholder}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\ton:example_select={(e) => dispatch(\\\\\"example_select\\\\\", e.detail)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n{#if show_scroll_button}\\\\n\\\\t<div class=\\\\\"scroll-down-button-container\\\\\">\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={ScrollDownArrow}\\\\n\\\\t\\\\t\\\\tlabel=\\\\\"Scroll down\\\\\"\\\\n\\\\t\\\\t\\\\tsize=\\\\\"large\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={scroll_to_bottom}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.panel-wrap {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.bubble-wrap {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tpadding-top: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (prefers-color-scheme: dark) {\\\\n\\\\t\\\\t.bubble-wrap {\\\\n\\\\t\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.prose.chatbot.md) {\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t\\\\toverflow-wrap: break-word;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.message-row .md img) {\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tmargin: var(--size-2);\\\\n\\\\t\\\\twidth: 400px;\\\\n\\\\t\\\\tmax-width: 30vw;\\\\n\\\\t\\\\tmax-height: 30vw;\\\\n\\\\t}\\\\n\\\\n\\\\t/* link styles */\\\\n\\\\t.message-wrap :global(.message a) {\\\\n\\\\t\\\\tcolor: var(--color-text-link);\\\\n\\\\t\\\\ttext-decoration: underline;\\\\n\\\\t}\\\\n\\\\n\\\\t/* table styles */\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) table),\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) tr),\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) td),\\\\n\\\\t.message-wrap :global(.bot:not(:has(.table-wrap)) th) {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.user table),\\\\n\\\\t.message-wrap :global(.user tr),\\\\n\\\\t.message-wrap :global(.user td),\\\\n\\\\t.message-wrap :global(.user th) {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t/* KaTeX */\\\\n\\\\t.message-wrap :global(span.katex) {\\\\n\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\tdirection: ltr;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(span.katex-display) {\\\\n\\\\t\\\\tmargin-top: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(pre) {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap :global(.grid-wrap) {\\\\n\\\\t\\\\tmax-height: 80% !important;\\\\n\\\\t\\\\tmax-width: 600px;\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap > div :global(p:not(:first-child)) {\\\\n\\\\t\\\\tmargin-top: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.message-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-xxl);\\\\n\\\\t}\\\\n\\\\n\\\\t.panel-wrap :global(.message-row:first-child) {\\\\n\\\\t\\\\tpadding-top: calc(var(--spacing-xxl) * 2);\\\\n\\\\t}\\\\n\\\\n\\\\t.scroll-down-button-container {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 10px;\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\ttransform: translateX(-50%);\\\\n\\\\t\\\\tz-index: var(--layer-top);\\\\n\\\\t}\\\\n\\\\t.scroll-down-button-container :global(button) {\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop);\\\\n\\\\t\\\\ttransition:\\\\n\\\\t\\\\t\\\\tbox-shadow 0.2s ease-in-out,\\\\n\\\\t\\\\t\\\\ttransform 0.2s ease-in-out;\\\\n\\\\t}\\\\n\\\\t.scroll-down-button-container :global(button:hover) {\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\tvar(--shadow-drop),\\\\n\\\\t\\\\t\\\\t0 2px 2px rgba(0, 0, 0, 0.05);\\\\n\\\\t\\\\ttransform: translateY(-2px);\\\\n\\\\t}\\\\n\\\\n\\\\t.options {\\\\n\\\\t\\\\tmargin-left: auto;\\\\n\\\\t\\\\tpadding: var(--spacing-xxl);\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tgrid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\\\n\\\\t\\\\tgap: var(--spacing-xxl);\\\\n\\\\t\\\\tmax-width: calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));\\\\n\\\\t\\\\tjustify-content: end;\\\\n\\\\t}\\\\n\\\\n\\\\t.option {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tpadding: var(--spacing-xl);\\\\n\\\\t\\\\tborder: 1px dashed var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\ttransition: var(--button-transition);\\\\n\\\\t\\\\tmax-width: var(--size-56);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.option:hover {\\\\n\\\\t\\\\tbackground-color: var(--color-accent-soft);\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA2UC,uCAAY,CACX,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IACb,CAEA,wCAAa,CACZ,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,IAAI,aAAa,CAC/B,CAEA,MAAO,uBAAuB,IAAI,CAAE,CACnC,wCAAa,CACZ,UAAU,CAAE,IAAI,2BAA2B,CAC5C,CACD,CAEA,2BAAa,CAAS,iBAAmB,CACxC,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,UAChB,CAEA,2BAAa,CAAS,oBAAsB,CAC3C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,IACb,CAGA,2BAAa,CAAS,UAAY,CACjC,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,eAAe,CAAE,SAClB,CAGA,2BAAa,CAAS,iCAAkC,CACxD,2BAAa,CAAS,8BAA+B,CACrD,2BAAa,CAAS,8BAA+B,CACrD,2BAAa,CAAS,8BAAgC,CACrD,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CAEA,2BAAa,CAAS,WAAY,CAClC,2BAAa,CAAS,QAAS,CAC/B,2BAAa,CAAS,QAAS,CAC/B,2BAAa,CAAS,QAAU,CAC/B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAC5C,CAGA,2BAAa,CAAS,UAAY,CACjC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,SAAS,CAAE,GACZ,CAEA,2BAAa,CAAS,kBAAoB,CACzC,UAAU,CAAE,CACb,CAEA,2BAAa,CAAS,GAAK,CAC1B,QAAQ,CAAE,QACX,CAEA,2BAAa,CAAS,UAAY,CACjC,UAAU,CAAE,GAAG,CAAC,UAAU,CAC1B,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,OACb,CAEA,2BAAa,CAAG,iBAAG,CAAS,mBAAqB,CAChD,UAAU,CAAE,IAAI,aAAa,CAC9B,CAEA,yCAAc,CACb,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,aAAa,CAC9B,aAAa,CAAE,IAAI,aAAa,CACjC,CAEA,yBAAW,CAAS,wBAA0B,CAC7C,WAAW,CAAE,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CACzC,CAEA,yDAA8B,CAC7B,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,OAAO,CAAE,IAAI,WAAW,CACzB,CACA,2CAA6B,CAAS,MAAQ,CAC7C,aAAa,CAAE,GAAG,CAClB,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,UAAU,CACT,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC;AAC/B,GAAG,SAAS,CAAC,IAAI,CAAC,WACjB,CACA,2CAA6B,CAAS,YAAc,CACnD,UAAU,CACT,IAAI,aAAa,CAAC,CAAC;AACtB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9B,SAAS,CAAE,WAAW,IAAI,CAC3B,CAEA,oCAAS,CACR,WAAW,CAAE,IAAI,CACjB,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,aAAa,CAAC,CACvB,SAAS,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAC9D,eAAe,CAAE,GAClB,CAEA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,MAAM,CAAE,GAAG,CAAC,MAAM,CAAC,IAAI,sBAAsB,CAAC,CAC9C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,CACX,eAAe,CAAE,MAClB,CAEA,mCAAO,MAAO,CACb,gBAAgB,CAAE,IAAI,mBAAmB,CAAC,CAC1C,YAAY,CAAE,IAAI,qBAAqB,CACxC\"}'\n};\nconst ChatBot = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let groupedMessages;\n  let options;\n  let { value = [] } = $$props;\n  let old_value = null;\n  let { _fetch } = $$props;\n  let { load_component } = $$props;\n  let { allow_file_downloads } = $$props;\n  let { display_consecutive_in_same_bubble } = $$props;\n  let _components = {};\n  const is_browser = typeof window !== \"undefined\";\n  async function update_components() {\n    _components = await load_components(get_components_from_messages(value), _components, load_component);\n  }\n  let { latex_delimiters } = $$props;\n  let { pending_message = false } = $$props;\n  let { generating = false } = $$props;\n  let { selectable = false } = $$props;\n  let { likeable = false } = $$props;\n  let { feedback_options } = $$props;\n  let { feedback_value = null } = $$props;\n  let { editable = null } = $$props;\n  let { show_share_button = false } = $$props;\n  let { show_copy_all_button = false } = $$props;\n  let { rtl = false } = $$props;\n  let { show_copy_button = false } = $$props;\n  let { avatar_images = [null, null] } = $$props;\n  let { sanitize_html = true } = $$props;\n  let { render_markdown = true } = $$props;\n  let { line_breaks = true } = $$props;\n  let { autoscroll = true } = $$props;\n  let { theme_mode } = $$props;\n  let { i18n } = $$props;\n  let { layout = \"bubble\" } = $$props;\n  let { placeholder = null } = $$props;\n  let { upload } = $$props;\n  let { msg_format = \"tuples\" } = $$props;\n  let { examples = null } = $$props;\n  let { _retryable = false } = $$props;\n  let { _undoable = false } = $$props;\n  let { like_user_message = false } = $$props;\n  let { root } = $$props;\n  let { allow_tags = false } = $$props;\n  let { watermark = null } = $$props;\n  let { show_progress = \"full\" } = $$props;\n  let target = null;\n  let edit_index = null;\n  let edit_messages = [];\n  onMount(() => {\n    target = document.querySelector(\"div.gradio-container\");\n  });\n  let div;\n  const dispatch = createEventDispatcher();\n  async function scroll_on_value_update() {\n    if (!autoscroll)\n      return;\n  }\n  onMount(() => {\n    scroll_on_value_update();\n  });\n  onMount(() => {\n    return () => {\n    };\n  });\n  function handle_action(i, message, selected) {\n    if (selected === \"undo\" || selected === \"retry\") {\n      const val_ = value;\n      let last_index = val_.length - 1;\n      while (val_[last_index].role === \"assistant\") {\n        last_index--;\n      }\n      dispatch(selected, {\n        index: val_[last_index].index,\n        value: val_[last_index].content\n      });\n    } else if (selected == \"edit\") {\n      edit_index = i;\n      edit_messages.push(message.content);\n    } else if (selected == \"edit_cancel\") {\n      edit_index = null;\n    } else if (selected == \"edit_submit\") {\n      edit_index = null;\n      dispatch(\"edit\", {\n        index: message.index,\n        value: edit_messages[i].slice(),\n        previous_value: message.content\n      });\n    } else {\n      let feedback = selected === \"Like\" ? true : selected === \"Dislike\" ? false : selected || \"\";\n      if (msg_format === \"tuples\") {\n        dispatch(\"like\", {\n          index: message.index,\n          value: message.content,\n          liked: feedback\n        });\n      } else {\n        if (!groupedMessages)\n          return;\n        const message_group = groupedMessages[i];\n        const [first, last] = [message_group[0], message_group[message_group.length - 1]];\n        dispatch(\"like\", {\n          index: first.index,\n          value: message_group.map((m) => m.content),\n          liked: feedback\n        });\n      }\n    }\n  }\n  function get_last_bot_options() {\n    if (!value || !groupedMessages || groupedMessages.length === 0)\n      return void 0;\n    const last_group = groupedMessages[groupedMessages.length - 1];\n    if (last_group[0].role !== \"assistant\")\n      return void 0;\n    return last_group[last_group.length - 1].options;\n  }\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.load_component === void 0 && $$bindings.load_component && load_component !== void 0)\n    $$bindings.load_component(load_component);\n  if ($$props.allow_file_downloads === void 0 && $$bindings.allow_file_downloads && allow_file_downloads !== void 0)\n    $$bindings.allow_file_downloads(allow_file_downloads);\n  if ($$props.display_consecutive_in_same_bubble === void 0 && $$bindings.display_consecutive_in_same_bubble && display_consecutive_in_same_bubble !== void 0)\n    $$bindings.display_consecutive_in_same_bubble(display_consecutive_in_same_bubble);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.pending_message === void 0 && $$bindings.pending_message && pending_message !== void 0)\n    $$bindings.pending_message(pending_message);\n  if ($$props.generating === void 0 && $$bindings.generating && generating !== void 0)\n    $$bindings.generating(generating);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  if ($$props.likeable === void 0 && $$bindings.likeable && likeable !== void 0)\n    $$bindings.likeable(likeable);\n  if ($$props.feedback_options === void 0 && $$bindings.feedback_options && feedback_options !== void 0)\n    $$bindings.feedback_options(feedback_options);\n  if ($$props.feedback_value === void 0 && $$bindings.feedback_value && feedback_value !== void 0)\n    $$bindings.feedback_value(feedback_value);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.show_copy_all_button === void 0 && $$bindings.show_copy_all_button && show_copy_all_button !== void 0)\n    $$bindings.show_copy_all_button(show_copy_all_button);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.avatar_images === void 0 && $$bindings.avatar_images && avatar_images !== void 0)\n    $$bindings.avatar_images(avatar_images);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.autoscroll === void 0 && $$bindings.autoscroll && autoscroll !== void 0)\n    $$bindings.autoscroll(autoscroll);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.msg_format === void 0 && $$bindings.msg_format && msg_format !== void 0)\n    $$bindings.msg_format(msg_format);\n  if ($$props.examples === void 0 && $$bindings.examples && examples !== void 0)\n    $$bindings.examples(examples);\n  if ($$props._retryable === void 0 && $$bindings._retryable && _retryable !== void 0)\n    $$bindings._retryable(_retryable);\n  if ($$props._undoable === void 0 && $$bindings._undoable && _undoable !== void 0)\n    $$bindings._undoable(_undoable);\n  if ($$props.like_user_message === void 0 && $$bindings.like_user_message && like_user_message !== void 0)\n    $$bindings.like_user_message(like_user_message);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.allow_tags === void 0 && $$bindings.allow_tags && allow_tags !== void 0)\n    $$bindings.allow_tags(allow_tags);\n  if ($$props.watermark === void 0 && $$bindings.watermark && watermark !== void 0)\n    $$bindings.watermark(watermark);\n  if ($$props.show_progress === void 0 && $$bindings.show_progress && show_progress !== void 0)\n    $$bindings.show_progress(show_progress);\n  $$result.css.add(css$1);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      update_components();\n    }\n    {\n      if (value || pending_message || _components) {\n        scroll_on_value_update();\n      }\n    }\n    {\n      {\n        if (!dequal(value, old_value)) {\n          old_value = value;\n          dispatch(\"change\");\n        }\n      }\n    }\n    groupedMessages = value && group_messages(value);\n    options = value && get_last_bot_options();\n    $$rendered = `${value !== null && value.length > 0 ? `${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, {}, {}, {\n      default: () => {\n        return `${show_share_button ? `${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Community }, {}, {})}` : ``} ${validate_component(IconButton, \"IconButton\").$$render(\n          $$result,\n          {\n            Icon: Trash,\n            label: i18n(\"chatbot.clear\")\n          },\n          {},\n          {}\n        )} ${show_copy_all_button ? `${validate_component(CopyAll, \"CopyAll\").$$render($$result, { value, watermark }, {}, {})}` : ``}`;\n      }\n    })}` : ``} <div class=\"${escape(null_to_empty(layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"), true) + \" svelte-gjtrl6\"}\" role=\"log\" aria-label=\"chatbot conversation\" aria-live=\"polite\"${add_attribute(\"this\", div, 0)}>${value !== null && value.length > 0 && groupedMessages !== null ? `<div class=\"message-wrap svelte-gjtrl6\">${each(groupedMessages, (messages, i) => {\n      let role = messages[0].role === \"user\" ? \"user\" : \"bot\", avatar_img = avatar_images[role === \"user\" ? 0 : 1], opposite_avatar_img = avatar_images[role === \"user\" ? 0 : 1], feedback_index = groupedMessages.slice(0, i).filter((m) => m[0].role === \"assistant\").length, current_feedback = role === \"bot\" && feedback_value && feedback_value[feedback_index] ? feedback_value[feedback_index] : null;\n      return `     ${validate_component(Message, \"Message\").$$render(\n        $$result,\n        {\n          messages,\n          display_consecutive_in_same_bubble,\n          opposite_avatar_img,\n          avatar_img,\n          role,\n          layout,\n          dispatch,\n          i18n,\n          _fetch,\n          line_breaks,\n          theme_mode,\n          target,\n          root,\n          upload,\n          selectable,\n          sanitize_html,\n          render_markdown,\n          rtl,\n          i,\n          value,\n          latex_delimiters,\n          _components,\n          generating,\n          msg_format,\n          feedback_options,\n          current_feedback,\n          allow_tags,\n          watermark,\n          show_like: role === \"user\" ? likeable && like_user_message : likeable,\n          show_retry: _retryable && is_last_bot_message(messages, value),\n          show_undo: _undoable && is_last_bot_message(messages, value),\n          show_edit: editable === \"all\" || editable == \"user\" && role === \"user\" && messages.length > 0 && messages[messages.length - 1].type == \"text\",\n          in_edit_mode: edit_index === i,\n          show_copy_button,\n          handle_action: (selected) => {\n            if (selected == \"edit\") {\n              edit_messages.splice(0, edit_messages.length);\n            }\n            if (selected === \"edit\" || selected === \"edit_submit\") {\n              messages.forEach((msg, index) => {\n                handle_action(selected === \"edit\" ? i : index, msg, selected);\n              });\n            } else {\n              handle_action(i, messages[0], selected);\n            }\n          },\n          scroll: is_browser ? scroll : () => {\n          },\n          allow_file_downloads,\n          edit_messages\n        },\n        {\n          edit_messages: ($$value) => {\n            edit_messages = $$value;\n            $$settled = false;\n          }\n        },\n        {}\n      )} ${show_progress !== \"hidden\" && generating && messages[messages.length - 1].role === \"assistant\" && messages[messages.length - 1].metadata?.status === \"done\" ? `${validate_component(Pending, \"Pending\").$$render($$result, { layout, avatar_images }, {}, {})}` : ``}`;\n    })} ${show_progress !== \"hidden\" && pending_message ? `${validate_component(Pending, \"Pending\").$$render($$result, { layout, avatar_images }, {}, {})}` : `${options ? `<div class=\"options svelte-gjtrl6\">${each(options, (option, index) => {\n      return `<button class=\"option svelte-gjtrl6\">${escape(option.label || option.value)} </button>`;\n    })}</div>` : ``}`}</div>` : `${validate_component(Examples, \"Examples\").$$render(\n      $$result,\n      {\n        examples,\n        placeholder,\n        latex_delimiters,\n        root\n      },\n      {},\n      {}\n    )}`}</div> ${``}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst ChatBot$1 = ChatBot;\nconst css = {\n  code: \".wrapper.svelte-g3p8na{display:flex;position:relative;flex-direction:column;align-items:start;width:100%;height:100%;flex-grow:1}.progress-text{right:auto}\",\n  map: '{\"version\":3,\"file\":\"Index.svelte\",\"sources\":[\"Index.svelte\"],\"sourcesContent\":[\"<script context=\\\\\"module\\\\\" lang=\\\\\"ts\\\\\">export { default as BaseChatBot } from \\\\\"./shared/ChatBot.svelte\\\\\";\\\\n<\\/script>\\\\n\\\\n<script lang=\\\\\"ts\\\\\">import ChatBot from \\\\\"./shared/ChatBot.svelte\\\\\";\\\\nimport { Block, BlockLabel } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Chat } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StatusTracker } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport { normalise_tuples, normalise_messages } from \\\\\"./shared/utils\\\\\";\\\\nexport let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let visible = true;\\\\nexport let value = [];\\\\nexport let scale = null;\\\\nexport let min_width = void 0;\\\\nexport let label;\\\\nexport let show_label = true;\\\\nexport let root;\\\\nexport let _selectable = false;\\\\nexport let likeable = false;\\\\nexport let feedback_options = [\\\\\"Like\\\\\", \\\\\"Dislike\\\\\"];\\\\nexport let feedback_value = null;\\\\nexport let show_share_button = false;\\\\nexport let rtl = false;\\\\nexport let show_copy_button = true;\\\\nexport let show_copy_all_button = false;\\\\nexport let sanitize_html = true;\\\\nexport let layout = \\\\\"bubble\\\\\";\\\\nexport let type = \\\\\"tuples\\\\\";\\\\nexport let render_markdown = true;\\\\nexport let line_breaks = true;\\\\nexport let autoscroll = true;\\\\nexport let _retryable = false;\\\\nexport let _undoable = false;\\\\nexport let group_consecutive_messages = true;\\\\nexport let allow_tags = false;\\\\nexport let latex_delimiters;\\\\nexport let gradio;\\\\nlet _value = [];\\\\n$: _value = type === \\\\\"tuples\\\\\" ? normalise_tuples(value, root) : normalise_messages(value, root);\\\\nexport let avatar_images = [null, null];\\\\nexport let like_user_message = false;\\\\nexport let loading_status = void 0;\\\\nexport let height;\\\\nexport let resizable;\\\\nexport let min_height;\\\\nexport let max_height;\\\\nexport let editable = null;\\\\nexport let placeholder = null;\\\\nexport let examples = null;\\\\nexport let theme_mode;\\\\nexport let allow_file_downloads = true;\\\\nexport let watermark = null;\\\\n<\\/script>\\\\n\\\\n<Block\\\\n\\\\t{elem_id}\\\\n\\\\t{elem_classes}\\\\n\\\\t{visible}\\\\n\\\\tpadding={false}\\\\n\\\\t{scale}\\\\n\\\\t{min_width}\\\\n\\\\t{height}\\\\n\\\\t{resizable}\\\\n\\\\t{min_height}\\\\n\\\\t{max_height}\\\\n\\\\tallow_overflow={true}\\\\n\\\\tflex={true}\\\\n\\\\toverflow_behavior=\\\\\"auto\\\\\"\\\\n>\\\\n\\\\t{#if loading_status}\\\\n\\\\t\\\\t<StatusTracker\\\\n\\\\t\\\\t\\\\tautoscroll={gradio.autoscroll}\\\\n\\\\t\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t\\\\t{...loading_status}\\\\n\\\\t\\\\t\\\\tshow_progress={loading_status.show_progress === \\\\\"hidden\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"hidden\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"minimal\\\\\"}\\\\n\\\\t\\\\t\\\\ton:clear_status={() => gradio.dispatch(\\\\\"clear_status\\\\\", loading_status)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<div class=\\\\\"wrapper\\\\\">\\\\n\\\\t\\\\t{#if show_label}\\\\n\\\\t\\\\t\\\\t<BlockLabel\\\\n\\\\t\\\\t\\\\t\\\\t{show_label}\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Chat}\\\\n\\\\t\\\\t\\\\t\\\\tfloat={true}\\\\n\\\\t\\\\t\\\\t\\\\tlabel={label || \\\\\"Chatbot\\\\\"}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<ChatBot\\\\n\\\\t\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t\\\\tselectable={_selectable}\\\\n\\\\t\\\\t\\\\t{likeable}\\\\n\\\\t\\\\t\\\\t{feedback_options}\\\\n\\\\t\\\\t\\\\t{feedback_value}\\\\n\\\\t\\\\t\\\\t{show_share_button}\\\\n\\\\t\\\\t\\\\t{show_copy_all_button}\\\\n\\\\t\\\\t\\\\tvalue={_value}\\\\n\\\\t\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t\\\\tdisplay_consecutive_in_same_bubble={group_consecutive_messages}\\\\n\\\\t\\\\t\\\\t{render_markdown}\\\\n\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\tpending_message={loading_status?.status === \\\\\"pending\\\\\"}\\\\n\\\\t\\\\t\\\\tgenerating={loading_status?.status === \\\\\"generating\\\\\"}\\\\n\\\\t\\\\t\\\\t{rtl}\\\\n\\\\t\\\\t\\\\t{show_copy_button}\\\\n\\\\t\\\\t\\\\t{like_user_message}\\\\n\\\\t\\\\t\\\\tshow_progress={loading_status?.show_progress || \\\\\"full\\\\\"}\\\\n\\\\t\\\\t\\\\ton:change={() => gradio.dispatch(\\\\\"change\\\\\", value)}\\\\n\\\\t\\\\t\\\\ton:select={(e) => gradio.dispatch(\\\\\"select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:like={(e) => gradio.dispatch(\\\\\"like\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:share={(e) => gradio.dispatch(\\\\\"share\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:error={(e) => gradio.dispatch(\\\\\"error\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:example_select={(e) => gradio.dispatch(\\\\\"example_select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:option_select={(e) => gradio.dispatch(\\\\\"option_select\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:retry={(e) => gradio.dispatch(\\\\\"retry\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:undo={(e) => gradio.dispatch(\\\\\"undo\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:clear={() => {\\\\n\\\\t\\\\t\\\\t\\\\tvalue = [];\\\\n\\\\t\\\\t\\\\t\\\\tgradio.dispatch(\\\\\"clear\\\\\");\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\ton:copy={(e) => gradio.dispatch(\\\\\"copy\\\\\", e.detail)}\\\\n\\\\t\\\\t\\\\ton:edit={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\tif (value === null || value.length === 0) return;\\\\n\\\\t\\\\t\\\\t\\\\tif (type === \\\\\"messages\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t//@ts-ignore\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tvalue[e.detail.index].content = e.detail.value;\\\\n\\\\t\\\\t\\\\t\\\\t} else {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t//@ts-ignore\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tvalue[e.detail.index[0]][e.detail.index[1]] = e.detail.value;\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\tvalue = value;\\\\n\\\\t\\\\t\\\\t\\\\tgradio.dispatch(\\\\\"edit\\\\\", e.detail);\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t{avatar_images}\\\\n\\\\t\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\t\\\\t{autoscroll}\\\\n\\\\t\\\\t\\\\t{layout}\\\\n\\\\t\\\\t\\\\t{placeholder}\\\\n\\\\t\\\\t\\\\t{examples}\\\\n\\\\t\\\\t\\\\t{_retryable}\\\\n\\\\t\\\\t\\\\t{_undoable}\\\\n\\\\t\\\\t\\\\tupload={(...args) => gradio.client.upload(...args)}\\\\n\\\\t\\\\t\\\\t_fetch={(...args) => gradio.client.fetch(...args)}\\\\n\\\\t\\\\t\\\\tload_component={gradio.load_component}\\\\n\\\\t\\\\t\\\\tmsg_format={type}\\\\n\\\\t\\\\t\\\\troot={gradio.root}\\\\n\\\\t\\\\t\\\\t{allow_file_downloads}\\\\n\\\\t\\\\t\\\\t{allow_tags}\\\\n\\\\t\\\\t\\\\t{watermark}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n</Block>\\\\n\\\\n<style>\\\\n\\\\t.wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: start;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.progress-text) {\\\\n\\\\t\\\\tright: auto;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA6JC,sBAAS,CACR,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,KAAK,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,CACZ,CAEQ,cAAgB,CACvB,KAAK,CAAE,IACR\"}'\n};\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = [] } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { label } = $$props;\n  let { show_label = true } = $$props;\n  let { root } = $$props;\n  let { _selectable = false } = $$props;\n  let { likeable = false } = $$props;\n  let { feedback_options = [\"Like\", \"Dislike\"] } = $$props;\n  let { feedback_value = null } = $$props;\n  let { show_share_button = false } = $$props;\n  let { rtl = false } = $$props;\n  let { show_copy_button = true } = $$props;\n  let { show_copy_all_button = false } = $$props;\n  let { sanitize_html = true } = $$props;\n  let { layout = \"bubble\" } = $$props;\n  let { type = \"tuples\" } = $$props;\n  let { render_markdown = true } = $$props;\n  let { line_breaks = true } = $$props;\n  let { autoscroll = true } = $$props;\n  let { _retryable = false } = $$props;\n  let { _undoable = false } = $$props;\n  let { group_consecutive_messages = true } = $$props;\n  let { allow_tags = false } = $$props;\n  let { latex_delimiters } = $$props;\n  let { gradio } = $$props;\n  let _value = [];\n  let { avatar_images = [null, null] } = $$props;\n  let { like_user_message = false } = $$props;\n  let { loading_status = void 0 } = $$props;\n  let { height } = $$props;\n  let { resizable } = $$props;\n  let { min_height } = $$props;\n  let { max_height } = $$props;\n  let { editable = null } = $$props;\n  let { placeholder = null } = $$props;\n  let { examples = null } = $$props;\n  let { theme_mode } = $$props;\n  let { allow_file_downloads = true } = $$props;\n  let { watermark = null } = $$props;\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  if ($$props.likeable === void 0 && $$bindings.likeable && likeable !== void 0)\n    $$bindings.likeable(likeable);\n  if ($$props.feedback_options === void 0 && $$bindings.feedback_options && feedback_options !== void 0)\n    $$bindings.feedback_options(feedback_options);\n  if ($$props.feedback_value === void 0 && $$bindings.feedback_value && feedback_value !== void 0)\n    $$bindings.feedback_value(feedback_value);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.show_copy_button === void 0 && $$bindings.show_copy_button && show_copy_button !== void 0)\n    $$bindings.show_copy_button(show_copy_button);\n  if ($$props.show_copy_all_button === void 0 && $$bindings.show_copy_all_button && show_copy_all_button !== void 0)\n    $$bindings.show_copy_all_button(show_copy_all_button);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.layout === void 0 && $$bindings.layout && layout !== void 0)\n    $$bindings.layout(layout);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.render_markdown === void 0 && $$bindings.render_markdown && render_markdown !== void 0)\n    $$bindings.render_markdown(render_markdown);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.autoscroll === void 0 && $$bindings.autoscroll && autoscroll !== void 0)\n    $$bindings.autoscroll(autoscroll);\n  if ($$props._retryable === void 0 && $$bindings._retryable && _retryable !== void 0)\n    $$bindings._retryable(_retryable);\n  if ($$props._undoable === void 0 && $$bindings._undoable && _undoable !== void 0)\n    $$bindings._undoable(_undoable);\n  if ($$props.group_consecutive_messages === void 0 && $$bindings.group_consecutive_messages && group_consecutive_messages !== void 0)\n    $$bindings.group_consecutive_messages(group_consecutive_messages);\n  if ($$props.allow_tags === void 0 && $$bindings.allow_tags && allow_tags !== void 0)\n    $$bindings.allow_tags(allow_tags);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.avatar_images === void 0 && $$bindings.avatar_images && avatar_images !== void 0)\n    $$bindings.avatar_images(avatar_images);\n  if ($$props.like_user_message === void 0 && $$bindings.like_user_message && like_user_message !== void 0)\n    $$bindings.like_user_message(like_user_message);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.resizable === void 0 && $$bindings.resizable && resizable !== void 0)\n    $$bindings.resizable(resizable);\n  if ($$props.min_height === void 0 && $$bindings.min_height && min_height !== void 0)\n    $$bindings.min_height(min_height);\n  if ($$props.max_height === void 0 && $$bindings.max_height && max_height !== void 0)\n    $$bindings.max_height(max_height);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.examples === void 0 && $$bindings.examples && examples !== void 0)\n    $$bindings.examples(examples);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.allow_file_downloads === void 0 && $$bindings.allow_file_downloads && allow_file_downloads !== void 0)\n    $$bindings.allow_file_downloads(allow_file_downloads);\n  if ($$props.watermark === void 0 && $$bindings.watermark && watermark !== void 0)\n    $$bindings.watermark(watermark);\n  $$result.css.add(css);\n  _value = type === \"tuples\" ? normalise_tuples(value, root) : normalise_messages(value, root);\n  return `${validate_component(Block, \"Block\").$$render(\n    $$result,\n    {\n      elem_id,\n      elem_classes,\n      visible,\n      padding: false,\n      scale,\n      min_width,\n      height,\n      resizable,\n      min_height,\n      max_height,\n      allow_overflow: true,\n      flex: true,\n      overflow_behavior: \"auto\"\n    },\n    {},\n    {\n      default: () => {\n        return `${loading_status ? `${validate_component(Static, \"StatusTracker\").$$render(\n          $$result,\n          Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status, {\n            show_progress: loading_status.show_progress === \"hidden\" ? \"hidden\" : \"minimal\"\n          }),\n          {},\n          {}\n        )}` : ``} <div class=\"wrapper svelte-g3p8na\">${show_label ? `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n          $$result,\n          {\n            show_label,\n            Icon: Chat,\n            float: true,\n            label: label || \"Chatbot\"\n          },\n          {},\n          {}\n        )}` : ``} ${validate_component(ChatBot$1, \"ChatBot\").$$render(\n          $$result,\n          {\n            i18n: gradio.i18n,\n            selectable: _selectable,\n            likeable,\n            feedback_options,\n            feedback_value,\n            show_share_button,\n            show_copy_all_button,\n            value: _value,\n            latex_delimiters,\n            display_consecutive_in_same_bubble: group_consecutive_messages,\n            render_markdown,\n            theme_mode,\n            editable,\n            pending_message: loading_status?.status === \"pending\",\n            generating: loading_status?.status === \"generating\",\n            rtl,\n            show_copy_button,\n            like_user_message,\n            show_progress: loading_status?.show_progress || \"full\",\n            avatar_images,\n            sanitize_html,\n            line_breaks,\n            autoscroll,\n            layout,\n            placeholder,\n            examples,\n            _retryable,\n            _undoable,\n            upload: (...args) => gradio.client.upload(...args),\n            _fetch: (...args) => gradio.client.fetch(...args),\n            load_component: gradio.load_component,\n            msg_format: type,\n            root: gradio.root,\n            allow_file_downloads,\n            allow_tags,\n            watermark\n          },\n          {},\n          {}\n        )}</div>`;\n      }\n    }\n  )}`;\n});\nexport {\n  ChatBot$1 as BaseChatBot,\n  Index as default\n};\n"], "names": ["File"], "mappings": ";;;;;;;;;;AAOA,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtF,SAAS,2BAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;AACtD,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,MAAM,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC;AAC5B,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC5C,MAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACrM,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,SAAS,CAAC;AACrB,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,SAAS,yCAAyC,CAAC,OAAO,EAAE;AAC5D,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7E,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,2BAA2B,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC;AACnE,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI;AACvB,IAAI,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAC9B,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,KAAK,EAAE,EAAE;AACb,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,kBAAkB,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC5C,EAAE,IAAI,QAAQ,KAAK,IAAI;AACvB,IAAI,OAAO,QAAQ,CAAC;AACpB,EAAE,MAAM,WAAW,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAChD,EAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK;AACtC,IAAI,IAAI,UAAU,GAAG,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,GAAG;AAC3D,MAAM,IAAI,EAAE,OAAO,CAAC,IAAI;AACxB,MAAM,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAChC,MAAM,OAAO,EAAE,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;AACtD,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,OAAO,EAAE,OAAO,CAAC,OAAO;AAC9B,KAAK,GAAG,MAAM,IAAI,OAAO,CAAC,OAAO,GAAG;AACpC,MAAM,OAAO,EAAE,yCAAyC;AACxD,QAAQ,OAAO,CAAC,OAAO;AACvB,OAAO;AACP,MAAM,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAChC,MAAM,IAAI,EAAE,OAAO,CAAC,IAAI;AACxB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,OAAO,EAAE,OAAO,CAAC,OAAO;AAC9B,KAAK,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,EAAE,CAAC;AAC1C,IAAI,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;AAC5D,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;AACxD,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,MAAM,OAAO,GAAG,EAAE,GAAG,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AACxD,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtC,QAAQ,IAAI,EAAE,IAAI,KAAK,EAAE;AACzB,UAAU,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAC/C,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,EAAE,IAAI,KAAK,EAAE;AACrB,MAAM,MAAM,OAAO,GAAG,EAAE,GAAG,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AACtD,MAAM,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3C,MAAM,OAAO,OAAO,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;AACnC,CAAC;AACD,SAAS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC1C,EAAE,IAAI,QAAQ,KAAK,IAAI;AACvB,IAAI,OAAO,QAAQ,CAAC;AACpB,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AACpD,IAAI,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK;AAChD,MAAM,IAAI,OAAO,IAAI,IAAI;AACzB,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,MAAM,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,WAAW,CAAC;AACrD,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO;AACf,UAAU,IAAI;AACd,UAAU,IAAI,EAAE,MAAM;AACtB,UAAU,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC;AAClD,UAAU,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE;AACnC,UAAU,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3B,SAAS,CAAC;AACV,OAAO;AACP,MAAM,IAAI,MAAM,IAAI,OAAO,EAAE;AAC7B,QAAQ,OAAO;AACf,UAAU,OAAO,EAAE,yCAAyC,CAAC,OAAO,CAAC;AACrE,UAAU,IAAI;AACd,UAAU,IAAI,EAAE,WAAW;AAC3B,UAAU,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3B,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO;AACb,QAAQ,IAAI;AACZ,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,WAAW;AACzB,QAAQ,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;AACzB,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,CAAC;AAClD,CAAC;AACD,SAAS,oBAAoB,CAAC,OAAO,EAAE;AACvC,EAAE,OAAO,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC;AACtC,CAAC;AACD,SAAS,mBAAmB,CAAC,QAAQ,EAAE,YAAY,EAAE;AACrD,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC;AACpE,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AACzD,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7G,EAAE,OAAO,OAAO,IAAI,MAAM,CAAC;AAC3B,CAAC;AACD,SAAS,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE;AAC9C,EAAE,MAAM,eAAe,GAAG,EAAE,CAAC;AAC7B,EAAE,IAAI,YAAY,GAAG,EAAE,CAAC;AACxB,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC;AACzB,EAAE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAClC,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE;AACpE,MAAM,SAAS;AACf,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE;AACtC,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjC,KAAK,MAAM;AACX,MAAM,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,QAAQ,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3C,OAAO;AACP,MAAM,YAAY,GAAG,CAAC,OAAO,CAAC,CAAC;AAC/B,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,OAAO,eAAe,CAAC;AACzB,CAAC;AACD,eAAe,eAAe,CAAC,eAAe,EAAE,WAAW,EAAE,cAAc,EAAE;AAC7E,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AACtB,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,KAAK;AAC9C,IAAI,IAAI,WAAW,CAAC,cAAc,CAAC,IAAI,cAAc,KAAK,MAAM,EAAE;AAClE,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,cAAc,KAAK,WAAW,IAAI,cAAc,KAAK,SAAS,GAAG,WAAW,GAAG,MAAM,CAAC;AAC1G,IAAI,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AACxE,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrB,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACnE,EAAE,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,GAAG;AACtD,IAAI,CAAC,MAAM,EAAE,KAAK,KAAK,MAAM,CAAC,MAAM,KAAK,WAAW,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI;AACnF,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AACpC,EAAE,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK;AAC/D,IAAI,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;AAC1D,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC;AACD,SAAS,4BAA4B,CAAC,QAAQ,EAAE;AAChD,EAAE,IAAI,CAAC,QAAQ;AACf,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,IAAI,UAAU,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC7C,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AAChC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE;AACtC,MAAM,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAChD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,CAAC;AACD,SAAS,mBAAmB,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE;AAC7C,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;AACnB,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpC,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;AACtE,CAAC,CAAC;AACF,GAAG;AACH,EAAE,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,EAAE;AACvC,IAAI,OAAO,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC;AACzC,CAAC,CAAC;AACF,GAAG;AACH,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC;AACtB,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE;AACpC,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC/F,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD,SAAS,QAAQ,CAAC,OAAO,EAAE;AAC3B,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC9B,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AAC9B,MAAM,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE;AAC7B,QAAQ,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;AACtC,OAAO;AACP,MAAM,OAAO,CAAC,CAAC,OAAO,CAAC;AACvB,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE;AAC/B,IAAI,OAAO,mBAAmB,CAAC,OAAO,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC;AACzB,CAAC;AACD,SAAS,WAAW,CAAC,OAAO,EAAE;AAC9B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC;AACzJ,CAAC;AACD,MAAM,eAAe,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACvF,EAAE,OAAO,CAAC,2oBAA2oB,CAAC,CAAC;AACvpB,CAAC,CAAC,CAAC;AACH,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACxF,EAAE,OAAO,CAAC,2+BAA2+B,CAAC,CAAC;AACv/B,CAAC,CAAC,CAAC;AACH,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACrF,EAAE,OAAO,CAAC,qoBAAqoB,CAAC,CAAC;AACjpB,CAAC,CAAC,CAAC;AACH,MAAM,cAAc,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACtF,EAAE,OAAO,CAAC,u+BAAu+B,CAAC,CAAC;AACn/B,CAAC,CAAC,CAAC;AACH,MAAM,IAAI,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC5E,EAAE,OAAO,CAAC,uLAAuL,CAAC,CAAC;AACnM,CAAC,CAAC,CAAC;AACH,MAAM,UAAU,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAClF,EAAE,OAAO,CAAC,4JAA4J,CAAC,CAAC;AACxK,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,4oBAA4oB;AACppB,EAAE,GAAG,EAAE,ylIAAylI;AAChmI,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACnF,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,cAAc,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC;AAClG,EAAE,OAAO,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC1L,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,QAAQ,KAAK,SAAS,GAAG,eAAe,GAAG,gBAAgB;AACvE,MAAM,KAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACjF,MAAM,KAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,qBAAqB,GAAG,+BAA+B;AAC7F,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC1G,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,QAAQ,KAAK,MAAM,GAAG,aAAa,GAAG,cAAc;AAChE,MAAM,KAAK,EAAE,QAAQ,KAAK,MAAM,GAAG,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AACxE,MAAM,KAAK,EAAE,QAAQ,KAAK,MAAM,GAAG,qBAAqB,GAAG,+BAA+B;AAC1F,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,qDAAqD,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC9J,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,QAAQ,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,UAAU,GAAG,IAAI;AAC7E,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,KAAK,EAAE,QAAQ,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,qBAAqB,GAAG,+BAA+B;AACpH,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,oDAAoD,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK;AAC3F,IAAI,OAAO,CAAC,oDAAoD,EAAE,UAAU,CAAC;AAC7E,MAAM,aAAa,EAAE,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAG,QAAQ;AAC5D,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;AACpC,GAAG,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC;AACH,MAAM,MAAM,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC9E,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,SAAS,CAAC,MAAM;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,IAAI,EAAE,IAAI;AAChB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,ihBAAihB;AACzhB,EAAE,GAAG,EAAE,CAAC,swIAAswI,CAAC;AAC/wI,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACnF,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AAC/D,EAAE,SAAS,GAAG,gBAAgB,IAAI,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AAClE,EAAE,OAAO,CAAC,EAAE,SAAS,IAAI,UAAU,IAAI,SAAS,IAAI,SAAS,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE,kBAAkB,GAAG,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,mBAAmB,GAAG,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,aAAa,EAAE,IAAI,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;AAC1W,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,YAAY,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACvF,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC;AACvC,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,QAAQ,EAAE,UAAU;AAC9B,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAChE,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC;AACvC,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,QAAQ,EAAE,UAAU;AAC9B,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC/M,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC;AACtC,UAAU,QAAQ,EAAE,UAAU;AAC9B,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACtF,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC;AACrC,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,QAAQ,EAAE,UAAU;AAC9B,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACtF,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC;AACrC,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,QAAQ,EAAE,UAAU;AAC9B,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AACvF,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,aAAa;AACvB,UAAU,gBAAgB;AAC1B,UAAU,QAAQ,EAAE,gBAAgB;AACpC,UAAU,IAAI;AACd,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,KAAK;AACL,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,CAAC,CAAC,CAAC;AACH,MAAM,SAAS,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACjF,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,sCAAsC,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACnE,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,sCAAsC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sCAAsC,IAAI,sCAAsC,KAAK,KAAK,CAAC;AACzK,IAAI,UAAU,CAAC,sCAAsC,CAAC,sCAAsC,CAAC,CAAC;AAC9F,EAAE,OAAO,CAAC,EAAE,IAAI,KAAK,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC1H,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,sCAAsC;AAC5C,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,MAAM;AACZ,MAAM,aAAa,EAAE,KAAK;AAC1B,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,YAAY,EAAE,CAAC;AACrB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC3H,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,WAAW,EAAE,KAAK,CAAC,WAAW;AACpC,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,MAAM;AACxB,SAAS;AACT,QAAQ,IAAI;AACZ,OAAO;AACP,MAAM,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAC9B,MAAM,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;AAC9C,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS;AAChC,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS;AAChC,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,MAAM,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACtH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,aAAa,EAAE,KAAK,CAAC,aAAa;AACxC,MAAM,OAAO,EAAE,EAAE;AACjB,MAAM,mBAAmB,EAAE,IAAI;AAC/B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,OAAO,GAAG,CAAC,iCAAiC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACxJ,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,IAAI;AACV,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,iBAAiB,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE;AACrD,MAAM,oBAAoB,EAAE,oBAAoB;AAChD,MAAM,sCAAsC;AAC5C,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC7H,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAC9B,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK;AACjC,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,sCAAsC;AAC5C,MAAM,oBAAoB,EAAE,oBAAoB;AAChD,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACzC,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACvH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,oBAAoB,EAAE,oBAAoB;AAChD,MAAM,sCAAsC;AAC5C,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,MAAM,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACtH,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,UAAU,EAAE,KAAK;AACvB,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,IAAI;AACV,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,MAAM;AACxB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACzH,IAAI,QAAQ;AACZ,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,EAAE,YAAY,EAAE,KAAK,CAAC,YAAY,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,EAAE,eAAe,EAAE,KAAK,CAAC,eAAe,EAAE,EAAE,EAAE,kBAAkB,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,EAAE;AACnZ,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,MAAM;AACxB,SAAS;AACT,QAAQ,IAAI;AACZ,OAAO;AACP,KAAK,CAAC;AACN,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,ixBAAixB;AACzxB,EAAE,GAAG,EAAE,itJAAitJ;AACxtJ,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACtF,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,kCAAkC,EAAE,GAAG,OAAO,CAAC;AACvD,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,CAAC;AAC1E,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,kCAAkC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kCAAkC,IAAI,kCAAkC,KAAK,KAAK,CAAC;AAC7J,IAAI,UAAU,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC;AACtF,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ;AAC3H,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO,EAAE,OAAO,CAAC,OAAO;AAC9B,MAAM,gBAAgB;AACtB,MAAM,aAAa;AACnB,MAAM,eAAe;AACrB,MAAM,WAAW;AACjB,MAAM,IAAI;AACV,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,QAAQ;AAClJ,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;AAClC,MAAM,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS;AACrC,MAAM,UAAU,EAAE,WAAW;AAC7B,MAAM,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;AAClC,MAAM,sCAAsC,EAAE,aAAa,GAAG,CAAC,IAAI,kCAAkC;AACrG,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,oBAAoB;AAC1B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,CAAC,+EAA+E,EAAE,kBAAkB,CAACA,MAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,yGAAyG,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,aAAa;AACla,IAAI,UAAU;AACd,IAAI,MAAM,CAAC,YAAY,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,MAAM;AAC3H,IAAI,CAAC;AACL,GAAG,CAAC,uCAAuC,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,CAAC,kDAAkD,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnV,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,krDAAkrD;AAC1rD,EAAE,GAAG,EAAE,g4SAAg4S;AACv4S,CAAC,CAAC;AACF,SAAS,eAAe,CAAC,GAAG,EAAE;AAC9B,EAAE,OAAO,UAAU,IAAI,GAAG,CAAC;AAC3B,CAAC;AACD,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,kCAAkC,EAAE,GAAG,OAAO,CAAC;AACvD,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,uBAAuB,CAAC;AAC9B,EAAE,SAAS,cAAc,GAAG;AAC5B,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,CAAC;AAC1E,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,kCAAkC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kCAAkC,IAAI,kCAAkC,KAAK,KAAK,CAAC;AAC7J,IAAI,UAAU,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC;AACtF,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,YAAY,GAAG;AACjB,IAAI,GAAG,OAAO;AACd,IAAI,QAAQ,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,GAAG,EAAE;AAC9D,GAAG,CAAC;AACJ,EAAE;AACF,IAAI;AACJ,MAAM,QAAQ,GAAG,YAAY,EAAE,QAAQ,EAAE,MAAM,KAAK,MAAM,CAAC;AAC3D,KAAK;AACL,GAAG;AACH,EAAE;AACF,IAAI,IAAI,YAAY,CAAC,OAAO,IAAI,uBAAuB,IAAI,YAAY,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,EAAE;AACrG,MAAM,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,sDAAsD,EAAE,CAAC,sBAAsB,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,OAAO,KAAK,EAAE,IAAI,YAAY,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC,8DAA8D,EAAE,UAAU,CAAC;AAClT,IAAI,WAAW,EAAE,QAAQ,GAAG,gBAAgB,GAAG,cAAc;AAC7D,GAAG,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ;AAChL,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO,EAAE,YAAY,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;AACjD,MAAM,eAAe;AACrB,MAAM,gBAAgB;AACtB,MAAM,aAAa;AACnB,MAAM,IAAI;AACV,MAAM,UAAU;AAChB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,QAAQ,EAAE,MAAM,KAAK,SAAS,GAAG,CAAC,oDAAoD,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,IAAI,YAAY,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC,sCAAsC,EAAE,YAAY,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE;AAC7pB,IAAI,gBAAgB;AACpB,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,GAAG,iBAAiB,GAAG,EAAE,CAAC;AACxH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,uBAAuB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,QAAQ;AAC1I,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,aAAa;AACnB,MAAM,UAAU;AAChB,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,WAAW;AACjB,MAAM,MAAM;AACZ,MAAM,aAAa;AACnB,MAAM,MAAM;AACZ,MAAM,IAAI;AACV,MAAM,UAAU;AAChB,MAAM,MAAM;AACZ,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,oBAAoB;AAC1B,MAAM,kCAAkC;AACxC,MAAM,IAAI;AACV,MAAM,WAAW;AACjB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK;AACjI,IAAI,OAAO,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,QAAQ;AACjE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,GAAG;AACX,QAAQ,aAAa;AACrB,QAAQ,gBAAgB;AACxB,QAAQ,eAAe;AACvB,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,QAAQ,aAAa,EAAE,aAAa,GAAG,CAAC;AACxC,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,oBAAoB;AAC5B,QAAQ,kCAAkC;AAC1C,QAAQ,IAAI;AACZ,QAAQ,WAAW;AACnB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,wqOAAwqO;AAChrO,EAAE,GAAG,EAAE,k2tBAAk2tB;AACz2tB,CAAC,CAAC;AACF,IAAI,kBAAkB,GAAG,KAAK,CAAC;AAC/B,SAAS,sBAAsB,CAAC,OAAO,EAAE;AACzC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;AAC/B,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC;AAC3B,GAAG,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE;AACnF,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC9C,MAAM,OAAO,CAAC,wBAAwB,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/F,KAAK;AACL,IAAI,OAAO,CAAC,wBAAwB,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,IAAI,EAAE,CAAC,CAAC;AACtI,GAAG;AACH,EAAE,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC;AACzE,CAAC;AACD,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,mBAAmB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;AACxB,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;AACtB,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,kCAAkC,EAAE,GAAG,OAAO,CAAC;AACvD,EAAE,IAAI,EAAE,gBAAgB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,eAAe,GAAG,EAAE,CAAC;AAC3B,EAAE,IAAI,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxD,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvD,EAAE,IAAI,kBAAkB,CAAC;AACzB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,mBAAmB,IAAI,mBAAmB,KAAK,KAAK,CAAC;AAChH,IAAI,UAAU,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AACxD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;AAC1D,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,KAAK,KAAK,CAAC;AAC1E,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/B,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,kCAAkC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kCAAkC,IAAI,kCAAkC,KAAK,KAAK,CAAC;AAC7J,IAAI,UAAU,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC;AACtF,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE;AACF,IAAI,IAAI,YAAY,IAAI,CAAC,kBAAkB,EAAE;AAC7C,MAAM,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC9D,MAAM,KAAK,IAAI,GAAG,GAAG,MAAM,EAAE,GAAG,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AAClE,QAAQ,IAAI,GAAG,IAAI,CAAC,EAAE;AACtB,UAAU,cAAc,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;AAC3E,UAAU,eAAe,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC;AAC7E,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,kBAAkB,GAAG;AACvB,IAAI,aAAa;AACjB,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,gBAAgB;AACpB,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,gBAAgB;AACpB,IAAI,OAAO,EAAE,UAAU,KAAK,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ;AAC7D,IAAI,QAAQ,EAAE,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;AAChD,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,qBAAqB;AAC5F,IAAI,CAAC,UAAU,KAAK,IAAI,GAAG,aAAa,GAAG,EAAE,IAAI,GAAG,IAAI,mBAAmB,KAAK,IAAI,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACnH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,UAAU,KAAK,IAAI,GAAG,CAAC,6CAA6C,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC1I,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,GAAG,EAAE,UAAU,EAAE,GAAG;AAC1B,MAAM,GAAG,EAAE,IAAI,GAAG,SAAS;AAC3B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE;AAChC,IAAI,0BAA0B;AAC9B,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,EAAE,IAAI,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,GAAG,gBAAgB,GAAG,EAAE,CAAC;AAC3F,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE;AACrC,IAAI,MAAM,CAAC,aAAa,CAAC,kCAAkC,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,iBAAiB;AACnG,IAAI,kCAAkC,GAAG,SAAS,GAAG,EAAE;AACvD,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,aAAa,KAAK;AACpE,IAAI,OAAO,CAAC,YAAY,EAAE;AAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,kCAAkC,GAAG,IAAI,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,iBAAiB;AACpG,MAAM,mBAAmB,IAAI,CAAC,eAAe,GAAG,2BAA2B,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,GAAG,WAAW,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,oBAAoB,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC;AACtR,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,YAAY,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,yDAAyD,EAAE,UAAU,CAAC;AAC5I,MAAM,OAAO,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC;AAC/D,MAAM,YAAY,EAAE,CAAC,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;AACzD,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,YAAY,EAAE,IAAI,GAAG,cAAc,GAAG,sBAAsB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;AACrQ,MAAM,gBAAgB;AACtB,MAAM,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,eAAe,GAAG,2BAA2B,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,UAAU,GAAG,YAAY,GAAG,EAAE,CAAC;AACrJ,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACrC,MAAM,aAAa,EAAE,CAAC,IAAI,CAAC;AAC3B,MAAM,QAAQ,EAAE,UAAU,GAAG,SAAS,GAAG,MAAM;AAC/C,MAAM,YAAY,EAAE,GAAG,GAAG,OAAO,GAAG,MAAM;AAC1C,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ;AAClJ,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,GAAG;AACX,QAAQ,aAAa;AACrB,QAAQ,UAAU;AAClB,QAAQ,gBAAgB;AACxB,QAAQ,eAAe;AACvB,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,QAAQ,aAAa;AACrB,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,oBAAoB;AAC5B,QAAQ,kCAAkC;AAC1C,QAAQ,IAAI;AACZ,QAAQ,WAAW;AACnB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,QAAQ;AAC1E,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,aAAa;AACrB,QAAQ,UAAU;AAClB,QAAQ,gBAAgB;AACxB,QAAQ,eAAe;AACvB,QAAQ,WAAW;AACnB,QAAQ,MAAM;AACd,QAAQ,aAAa;AACrB,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,oBAAoB;AAC5B,QAAQ,kCAAkC;AAC1C,QAAQ,IAAI;AACZ,QAAQ,WAAW;AACnB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3N,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,KAAK,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,kBAAkB,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvL,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,0zDAA0zD;AACl0D,EAAE,GAAG,EAAE,4gLAA4gL;AACnhL,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,sCAAsC,EAAE,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,6CAA6C,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACzK,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG;AAC/B,MAAM,GAAG,EAAE,YAAY;AACvB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE;AAChC,IAAI,sBAAsB,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,iBAAiB;AACrE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,aAAa,GAAG,EAAE,IAAI,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACtH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,sWAAsW,CAAC,CAAC;AAC7X,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,wkFAAwkF;AAChlF,EAAE,GAAG,EAAE,qrcAAqrc;AAC5rc,CAAC,CAAC;AACF,MAAM,QAAQ,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAChF,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,oEAAoE,EAAE,WAAW,KAAK,IAAI,GAAG,CAAC,uCAAuC,EAAE,kBAAkB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ;AACtM,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,gBAAgB;AACtB,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,KAAK,IAAI,GAAG,CAAC,gDAAgD,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK;AAC1H,IAAI,OAAO,CAAC,qCAAqC,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,4CAA4C,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,mDAAmD,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC1T,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG;AAC7B,QAAQ,GAAG,EAAE,cAAc;AAC3B,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,KAAK,MAAM,GAAG,CAAC,mJAAmJ,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,4FAA4F,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK;AACvb,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mDAAmD,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACrJ,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,KAAK,EAAE,eAAe;AAChC,UAAU,GAAG,EAAE,IAAI,CAAC,GAAG;AACvB,UAAU,GAAG,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1D,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,sDAAsD,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,+EAA+E,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,6BAA6B,EAAE,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,sDAAsD,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAACA,MAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACz7B,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,6FAA6F,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mDAAmD,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACta,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;AACjC,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,eAAe;AAC1D,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,+EAA+E,EAAE,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,mCAAmC,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAACA,MAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,0FAA0F,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,8BAA8B,CAAC,CAAC;AACp1B,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC3B,CAAC,CAAC,CAAC;AACH,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,SAAS,CAAC,MAAM;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,w2FAAw2F;AACh3F,EAAE,GAAG,EAAE,yyjBAAyyjB;AAChzjB,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,kCAAkC,EAAE,GAAG,OAAO,CAAC;AACvD,EAAE,IAAI,WAAW,GAAG,EAAE,CAAC;AACvB,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,eAAe,iBAAiB,GAAG;AACrC,IAAI,WAAW,GAAG,MAAM,eAAe,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AAC1G,GAAG;AACH,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,gBAAgB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,UAAU,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,aAAa,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;AACxB,EAAE,IAAI,aAAa,GAAG,EAAE,CAAC;AAIzB,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,eAAe,sBAAsB,GAAG;AAC1C,IAAI,IAAI,CAAC,UAAU;AACnB,MAAM,OAAO;AACb,GAAG;AAQH,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC/C,IAAI,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;AACrD,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC;AACzB,MAAM,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,KAAK,WAAW,EAAE;AACpD,QAAQ,UAAU,EAAE,CAAC;AACrB,OAAO;AACP,MAAM,QAAQ,CAAC,QAAQ,EAAE;AACzB,QAAQ,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK;AACrC,QAAQ,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO;AACvC,OAAO,CAAC,CAAC;AACT,KAAK,MAAM,IAAI,QAAQ,IAAI,MAAM,EAAE;AACnC,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1C,KAAK,MAAM,IAAI,QAAQ,IAAI,aAAa,EAAE;AAC1C,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,KAAK,MAAM,IAAI,QAAQ,IAAI,aAAa,EAAE;AAC1C,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,QAAQ,CAAC,MAAM,EAAE;AACvB,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK;AAC5B,QAAQ,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;AACvC,QAAQ,cAAc,EAAE,OAAO,CAAC,OAAO;AACvC,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,IAAI,QAAQ,GAAG,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAG,QAAQ,KAAK,SAAS,GAAG,KAAK,GAAG,QAAQ,IAAI,EAAE,CAAC;AAClG,MAAM,IAAI,UAAU,KAAK,QAAQ,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,EAAE;AACzB,UAAU,KAAK,EAAE,OAAO,CAAC,KAAK;AAC9B,UAAU,KAAK,EAAE,OAAO,CAAC,OAAO;AAChC,UAAU,KAAK,EAAE,QAAQ;AACzB,SAAS,CAAC,CAAC;AACX,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,eAAe;AAC5B,UAAU,OAAO;AACjB,QAAQ,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AACjD,QAAQ,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1F,QAAQ,QAAQ,CAAC,MAAM,EAAE;AACzB,UAAU,KAAK,EAAE,KAAK,CAAC,KAAK;AAC5B,UAAU,KAAK,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AACpD,UAAU,KAAK,EAAE,QAAQ;AACzB,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,SAAS,oBAAoB,GAAG;AAClC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC;AAClE,MAAM,OAAO,KAAK,CAAC,CAAC;AACpB,IAAI,MAAM,UAAU,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACnE,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW;AAC1C,MAAM,OAAO,KAAK,CAAC,CAAC;AACpB,IAAI,OAAO,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;AACrD,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,kCAAkC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kCAAkC,IAAI,kCAAkC,KAAK,KAAK,CAAC;AAC7J,IAAI,UAAU,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC;AACtF,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,iBAAiB,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,KAAK,IAAI,eAAe,IAAI,WAAW,EAAE;AACnD,QAAQ,sBAAsB,EAAE,CAAC;AACjC,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;AACvC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,UAAU,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7B,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,eAAe,GAAG,KAAK,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AACrD,IAAI,OAAO,GAAG,KAAK,IAAI,oBAAoB,EAAE,CAAC;AAC9C,IAAI,UAAU,GAAG,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AAClJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,iBAAiB,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACrM,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,KAAK;AACvB,YAAY,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC;AACxC,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,EAAE,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACxI,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,MAAM,KAAK,QAAQ,GAAG,aAAa,GAAG,YAAY,CAAC,EAAE,IAAI,CAAC,GAAG,gBAAgB,CAAC,iEAAiE,EAAE,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,KAAK,IAAI,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK;AACvX,MAAM,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,EAAE,UAAU,GAAG,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,mBAAmB,GAAG,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,MAAM,EAAE,gBAAgB,GAAG,IAAI,KAAK,KAAK,IAAI,cAAc,IAAI,cAAc,CAAC,cAAc,CAAC,GAAG,cAAc,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;AAC9Y,MAAM,OAAO,CAAC,KAAK,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ;AACpE,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,QAAQ;AAClB,UAAU,kCAAkC;AAC5C,UAAU,mBAAmB;AAC7B,UAAU,UAAU;AACpB,UAAU,IAAI;AACd,UAAU,MAAM;AAChB,UAAU,QAAQ;AAClB,UAAU,IAAI;AACd,UAAU,MAAM;AAChB,UAAU,WAAW;AACrB,UAAU,UAAU;AACpB,UAAU,MAAM;AAChB,UAAU,IAAI;AACd,UAAU,MAAM;AAChB,UAAU,UAAU;AACpB,UAAU,aAAa;AACvB,UAAU,eAAe;AACzB,UAAU,GAAG;AACb,UAAU,CAAC;AACX,UAAU,KAAK;AACf,UAAU,gBAAgB;AAC1B,UAAU,WAAW;AACrB,UAAU,UAAU;AACpB,UAAU,UAAU;AACpB,UAAU,gBAAgB;AAC1B,UAAU,gBAAgB;AAC1B,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,UAAU,SAAS,EAAE,IAAI,KAAK,MAAM,GAAG,QAAQ,IAAI,iBAAiB,GAAG,QAAQ;AAC/E,UAAU,UAAU,EAAE,UAAU,IAAI,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC;AACxE,UAAU,SAAS,EAAE,SAAS,IAAI,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC;AACtE,UAAU,SAAS,EAAE,QAAQ,KAAK,KAAK,IAAI,QAAQ,IAAI,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,MAAM;AACvJ,UAAU,YAAY,EAAE,UAAU,KAAK,CAAC;AACxC,UAAU,gBAAgB;AAC1B,UAAU,aAAa,EAAE,CAAC,QAAQ,KAAK;AACvC,YAAY,IAAI,QAAQ,IAAI,MAAM,EAAE;AACpC,cAAc,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;AAC5D,aAAa;AACb,YAAY,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,aAAa,EAAE;AACnE,cAAc,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AAC/C,gBAAgB,aAAa,CAAC,QAAQ,KAAK,MAAM,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC9E,eAAe,CAAC,CAAC;AACjB,aAAa,MAAM;AACnB,cAAc,aAAa,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AACtD,aAAa;AACb,WAAW;AACX,UAAU,MAAM,EAAE,UAAU,GAAG,MAAM,GAAG,MAAM;AAC9C,WAAW;AACX,UAAU,oBAAoB;AAC9B,UAAU,aAAa;AACvB,SAAS;AACT,QAAQ;AACR,UAAU,aAAa,EAAE,CAAC,OAAO,KAAK;AACtC,YAAY,aAAa,GAAG,OAAO,CAAC;AACpC,YAAY,SAAS,GAAG,KAAK,CAAC;AAC9B,WAAW;AACX,SAAS;AACT,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,EAAE,aAAa,KAAK,QAAQ,IAAI,UAAU,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClR,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa,KAAK,QAAQ,IAAI,eAAe,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,KAAK;AAClP,MAAM,OAAO,CAAC,qCAAqC,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;AACtG,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ;AACpF,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,QAAQ;AAChB,QAAQ,WAAW;AACnB,QAAQ,gBAAgB;AACxB,QAAQ,IAAI;AACZ,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,SAAS,GAAG,QAAQ;AAC1B,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,6JAA6J;AACrK,EAAE,GAAG,EAAE,g3LAAg3L;AACv3L,CAAC,CAAC;AACG,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,gBAAgB,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,GAAG,OAAO,CAAC;AAC3D,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,gBAAgB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,0BAA0B,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtD,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,IAAI,EAAE,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,cAAc,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,oBAAoB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChD,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,0BAA0B,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,0BAA0B,IAAI,0BAA0B,KAAK,KAAK,CAAC;AACrI,IAAI,UAAU,CAAC,0BAA0B,CAAC,0BAA0B,CAAC,CAAC;AACtE,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,MAAM,GAAG,IAAI,KAAK,QAAQ,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/F,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACvD,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,cAAc,EAAE,IAAI;AAC1B,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,iBAAiB,EAAE,MAAM;AAC/B,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,cAAc,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ;AAC1F,UAAU,QAAQ;AAClB,UAAU,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,EAAE;AACtG,YAAY,aAAa,EAAE,cAAc,CAAC,aAAa,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;AAC3F,WAAW,CAAC;AACZ,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,oCAAoC,EAAE,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC5H,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,UAAU;AACtB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,KAAK,EAAE,KAAK,IAAI,SAAS;AACrC,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,QAAQ;AACrE,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7B,YAAY,UAAU,EAAE,WAAW;AACnC,YAAY,QAAQ;AACpB,YAAY,gBAAgB;AAC5B,YAAY,cAAc;AAC1B,YAAY,iBAAiB;AAC7B,YAAY,oBAAoB;AAChC,YAAY,KAAK,EAAE,MAAM;AACzB,YAAY,gBAAgB;AAC5B,YAAY,kCAAkC,EAAE,0BAA0B;AAC1E,YAAY,eAAe;AAC3B,YAAY,UAAU;AACtB,YAAY,QAAQ;AACpB,YAAY,eAAe,EAAE,cAAc,EAAE,MAAM,KAAK,SAAS;AACjE,YAAY,UAAU,EAAE,cAAc,EAAE,MAAM,KAAK,YAAY;AAC/D,YAAY,GAAG;AACf,YAAY,gBAAgB;AAC5B,YAAY,iBAAiB;AAC7B,YAAY,aAAa,EAAE,cAAc,EAAE,aAAa,IAAI,MAAM;AAClE,YAAY,aAAa;AACzB,YAAY,aAAa;AACzB,YAAY,WAAW;AACvB,YAAY,UAAU;AACtB,YAAY,MAAM;AAClB,YAAY,WAAW;AACvB,YAAY,QAAQ;AACpB,YAAY,UAAU;AACtB,YAAY,SAAS;AACrB,YAAY,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC9D,YAAY,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC7D,YAAY,cAAc,EAAE,MAAM,CAAC,cAAc;AACjD,YAAY,UAAU,EAAE,IAAI;AAC5B,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7B,YAAY,oBAAoB;AAChC,YAAY,UAAU;AACtB,YAAY,SAAS;AACrB,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,MAAM,CAAC,CAAC;AAClB,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN,CAAC;;;;"}