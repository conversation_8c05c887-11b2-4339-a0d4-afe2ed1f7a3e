const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-CJ3DTY-J.js')).default;
const imports = ["_app/immutable/nodes/1.CeodhroC.js","_app/immutable/chunks/stores.BEyCbaV1.js","_app/immutable/chunks/client.BU6TeQdu.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-NOyM39Tl.js.map
