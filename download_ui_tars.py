#!/usr/bin/env python3
"""
UI-TARS 1.5-7B 模型下载脚本
"""

import os
import sys
from transformers import AutoTokenizer, AutoModel, AutoProcessor

def download_model():
    """下载UI-TARS模型"""
    model_name = "ByteDance-Seed/UI-TARS-1.5-7B"
    
    print("🚀 开始下载UI-TARS 1.5-7B模型...")
    print(f"模型: {model_name}")
    print("这可能需要一些时间，请耐心等待...")
    
    try:
        # 下载tokenizer
        print("\n📥 下载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            cache_dir="./models"
        )
        print("✅ Tokenizer下载完成")
        
        # 下载processor
        print("\n📥 下载processor...")
        processor = AutoProcessor.from_pretrained(
            model_name,
            trust_remote_code=True,
            cache_dir="./models"
        )
        print("✅ Processor下载完成")

        # 下载模型
        print("\n📥 下载模型（约13GB）...")
        model = AutoModel.from_pretrained(
            model_name,
            trust_remote_code=True,
            torch_dtype="auto",
            device_map="cpu",  # 先下载到CPU
            cache_dir="./models"
        )
        print("✅ 模型下载完成")
        
        # 测试模型
        print("\n🧪 测试模型...")
        test_input = "请分析这个界面"

        # 使用processor处理输入
        inputs = processor(
            text=test_input,
            return_tensors="pt"
        )

        print(f"测试输入: {test_input}")
        print("✅ 模型可以正常处理输入")
        
        print("\n🎉 UI-TARS模型下载和测试完成！")
        print("现在您可以运行 python ui_tars_web_interface.py 启动Web界面")
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print("\n💡 建议:")
        print("1. 检查网络连接")
        print("2. 确保有足够的磁盘空间（至少50GB）")
        print("3. 如果网络较慢，可以设置镜像源")
        return False
    
    return True

if __name__ == "__main__":
    # 设置镜像源（可选）
    # os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
    
    import torch
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    download_model()
