{"version": 3, "file": "ImagePreview-zm3C7s_3.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/ImagePreview.js"], "sourcesContent": ["import { create_ssr_component, validate_component, add_attribute } from \"svelte/internal\";\nimport { createEventDispatcher } from \"svelte\";\nimport { B as BlockLabel, q as Image, k as Empty, I as IconButtonWrapper, F as FullscreenButton, h as IconButton, D as Download, j as ShareButton } from \"./client.js\";\nimport { u as uploadToHuggingFace } from \"./utils.js\";\nimport { I as Image$1 } from \"./Image.js\";\nimport { D as DownloadLink } from \"./DownloadLink.js\";\nconst css = {\n  code: \".image-container.svelte-zxsjoa.svelte-zxsjoa{height:100%;position:relative;min-width:var(--size-20)}.image-container.svelte-zxsjoa button.svelte-zxsjoa{width:var(--size-full);height:var(--size-full);border-radius:var(--radius-lg);display:flex;align-items:center;justify-content:center}.image-frame.svelte-zxsjoa img{width:var(--size-full);height:var(--size-full);object-fit:scale-down}.selectable.svelte-zxsjoa.svelte-zxsjoa{cursor:crosshair}.fullscreen-controls svg{position:relative;top:0px}.image-container:fullscreen{background-color:black;display:flex;justify-content:center;align-items:center}.image-container:fullscreen img{max-width:90vw;max-height:90vh;object-fit:scale-down}.image-frame.svelte-zxsjoa.svelte-zxsjoa{width:auto;height:100%;display:flex;align-items:center;justify-content:center}\",\n  map: '{\"version\":3,\"file\":\"ImagePreview.svelte\",\"sources\":[\"ImagePreview.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, onMount } from \\\\\"svelte\\\\\";\\\\nimport { uploadToHuggingFace } from \\\\\"@gradio/utils\\\\\";\\\\nimport { BlockLabel, Empty, IconButton, ShareButton, IconButtonWrapper, FullscreenButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Download, Image as ImageIcon } from \\\\\"@gradio/icons\\\\\";\\\\nimport { get_coordinates_of_clicked_image } from \\\\\"./utils\\\\\";\\\\nimport Image from \\\\\"./Image.svelte\\\\\";\\\\nimport { DownloadLink } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nexport let value;\\\\nexport let label = void 0;\\\\nexport let show_label;\\\\nexport let show_download_button = true;\\\\nexport let selectable = false;\\\\nexport let show_share_button = false;\\\\nexport let i18n;\\\\nexport let show_fullscreen_button = true;\\\\nexport let display_icon_button_wrapper_top_corner = false;\\\\nexport let fullscreen = false;\\\\nconst dispatch = createEventDispatcher();\\\\nconst handle_click = (evt) => {\\\\n    let coordinates = get_coordinates_of_clicked_image(evt);\\\\n    if (coordinates) {\\\\n        dispatch(\\\\\"select\\\\\", { index: coordinates, value: null });\\\\n    }\\\\n};\\\\nlet image_container;\\\\n<\\/script>\\\\n\\\\n<BlockLabel\\\\n\\\\t{show_label}\\\\n\\\\tIcon={ImageIcon}\\\\n\\\\tlabel={!show_label ? \\\\\"\\\\\" : label || i18n(\\\\\"image.image\\\\\")}\\\\n/>\\\\n{#if value === null || !value.url}\\\\n\\\\t<Empty unpadded_box={true} size=\\\\\"large\\\\\"><ImageIcon /></Empty>\\\\n{:else}\\\\n\\\\t<div class=\\\\\"image-container\\\\\" bind:this={image_container}>\\\\n\\\\t\\\\t<IconButtonWrapper\\\\n\\\\t\\\\t\\\\tdisplay_top_corner={display_icon_button_wrapper_top_corner}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#if show_fullscreen_button}\\\\n\\\\t\\\\t\\\\t\\\\t<FullscreenButton {fullscreen} on:fullscreen />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t\\\\t{#if show_download_button}\\\\n\\\\t\\\\t\\\\t\\\\t<DownloadLink href={value.url} download={value.orig_name || \\\\\"image\\\\\"}>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton Icon={Download} label={i18n(\\\\\"common.download\\\\\")} />\\\\n\\\\t\\\\t\\\\t\\\\t</DownloadLink>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t{#if show_share_button}\\\\n\\\\t\\\\t\\\\t\\\\t<ShareButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:share\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tformatter={async (value) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (!value) return \\\\\"\\\\\";\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlet url = await uploadToHuggingFace(value, \\\\\"url\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\treturn `<img src=\\\\\"${url}\\\\\" />`;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\t\\\\t<button on:click={handle_click}>\\\\n\\\\t\\\\t\\\\t<div class:selectable class=\\\\\"image-frame\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t<Image src={value.url} alt=\\\\\"\\\\\" loading=\\\\\"lazy\\\\\" on:load />\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</button>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.image-container {\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tmin-width: var(--size-20);\\\\n\\\\t}\\\\n\\\\n\\\\t.image-container button {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.image-frame :global(img) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: scale-down;\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: crosshair;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.fullscreen-controls svg) {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\ttop: 0px;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.image-container:fullscreen) {\\\\n\\\\t\\\\tbackground-color: black;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.image-container:fullscreen img) {\\\\n\\\\t\\\\tmax-width: 90vw;\\\\n\\\\t\\\\tmax-height: 90vh;\\\\n\\\\t\\\\tobject-fit: scale-down;\\\\n\\\\t}\\\\n\\\\n\\\\t.image-frame {\\\\n\\\\t\\\\twidth: auto;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAuEC,4CAAiB,CAChB,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,8BAAgB,CAAC,oBAAO,CACvB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,aAAa,CAAE,IAAI,WAAW,CAAC,CAE/B,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB,CAEA,0BAAY,CAAS,GAAK,CACzB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,UACb,CAEA,uCAAY,CACX,MAAM,CAAE,SACT,CAEQ,wBAA0B,CACjC,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GACN,CAEQ,2BAA6B,CACpC,gBAAgB,CAAE,KAAK,CACvB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd,CAEQ,+BAAiC,CACxC,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,UACb,CAEA,wCAAa,CACZ,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB\"}'\n};\nconst ImagePreview = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { label = void 0 } = $$props;\n  let { show_label } = $$props;\n  let { show_download_button = true } = $$props;\n  let { selectable = false } = $$props;\n  let { show_share_button = false } = $$props;\n  let { i18n } = $$props;\n  let { show_fullscreen_button = true } = $$props;\n  let { display_icon_button_wrapper_top_corner = false } = $$props;\n  let { fullscreen = false } = $$props;\n  createEventDispatcher();\n  let image_container;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.show_fullscreen_button === void 0 && $$bindings.show_fullscreen_button && show_fullscreen_button !== void 0)\n    $$bindings.show_fullscreen_button(show_fullscreen_button);\n  if ($$props.display_icon_button_wrapper_top_corner === void 0 && $$bindings.display_icon_button_wrapper_top_corner && display_icon_button_wrapper_top_corner !== void 0)\n    $$bindings.display_icon_button_wrapper_top_corner(display_icon_button_wrapper_top_corner);\n  if ($$props.fullscreen === void 0 && $$bindings.fullscreen && fullscreen !== void 0)\n    $$bindings.fullscreen(fullscreen);\n  $$result.css.add(css);\n  return `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n    $$result,\n    {\n      show_label,\n      Icon: Image,\n      label: !show_label ? \"\" : label || i18n(\"image.image\")\n    },\n    {},\n    {}\n  )} ${value === null || !value.url ? `${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true, size: \"large\" }, {}, {\n    default: () => {\n      return `${validate_component(Image, \"ImageIcon\").$$render($$result, {}, {}, {})}`;\n    }\n  })}` : `<div class=\"image-container svelte-zxsjoa\"${add_attribute(\"this\", image_container, 0)}>${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render(\n    $$result,\n    {\n      display_top_corner: display_icon_button_wrapper_top_corner\n    },\n    {},\n    {\n      default: () => {\n        return `${show_fullscreen_button ? `${validate_component(FullscreenButton, \"FullscreenButton\").$$render($$result, { fullscreen }, {}, {})}` : ``} ${show_download_button ? `${validate_component(DownloadLink, \"DownloadLink\").$$render(\n          $$result,\n          {\n            href: value.url,\n            download: value.orig_name || \"image\"\n          },\n          {},\n          {\n            default: () => {\n              return `${validate_component(IconButton, \"IconButton\").$$render(\n                $$result,\n                {\n                  Icon: Download,\n                  label: i18n(\"common.download\")\n                },\n                {},\n                {}\n              )}`;\n            }\n          }\n        )}` : ``} ${show_share_button ? `${validate_component(ShareButton, \"ShareButton\").$$render(\n          $$result,\n          {\n            i18n,\n            formatter: async (value2) => {\n              if (!value2)\n                return \"\";\n              let url = await uploadToHuggingFace(value2);\n              return `<img src=\"${url}\" />`;\n            },\n            value\n          },\n          {},\n          {}\n        )}` : ``}`;\n      }\n    }\n  )} <button class=\"svelte-zxsjoa\"><div class=\"${[\"image-frame svelte-zxsjoa\", selectable ? \"selectable\" : \"\"].join(\" \").trim()}\">${validate_component(Image$1, \"Image\").$$render($$result, { src: value.url, alt: \"\", loading: \"lazy\" }, {}, {})}</div></button></div>`}`;\n});\nexport {\n  ImagePreview as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAMA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,qyBAAqyB;AAC7yB,EAAE,GAAG,EAAE,6hJAA6hJ;AACpiJ,CAAC,CAAC;AACG,MAAC,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,oBAAoB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChD,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,sBAAsB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAClD,EAAE,IAAI,EAAE,sCAAsC,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACnE,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,sBAAsB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sBAAsB,IAAI,sBAAsB,KAAK,KAAK,CAAC;AACzH,IAAI,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;AAC9D,EAAE,IAAI,OAAO,CAAC,sCAAsC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sCAAsC,IAAI,sCAAsC,KAAK,KAAK,CAAC;AACzK,IAAI,UAAU,CAAC,sCAAsC,CAAC,sCAAsC,CAAC,CAAC;AAC9F,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,UAAU;AAChB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,KAAK,EAAE,CAAC,UAAU,GAAG,EAAE,GAAG,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC;AAC5D,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AAC1I,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACxF,KAAK;AACL,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,0CAA0C,EAAE,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ;AACtK,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,kBAAkB,EAAE,sCAAsC;AAChE,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,sBAAsB,GAAG,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAC/O,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,KAAK,CAAC,GAAG;AAC3B,YAAY,QAAQ,EAAE,KAAK,CAAC,SAAS,IAAI,OAAO;AAChD,WAAW;AACX,UAAU,EAAE;AACZ,UAAU;AACV,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC7E,gBAAgB,QAAQ;AACxB,gBAAgB;AAChB,kBAAkB,IAAI,EAAE,QAAQ;AAChC,kBAAkB,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC;AAChD,iBAAiB;AACjB,gBAAgB,EAAE;AAClB,gBAAgB,EAAE;AAClB,eAAe,CAAC,CAAC,CAAC;AAClB,aAAa;AACb,WAAW;AACX,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AAClG,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI;AAChB,YAAY,SAAS,EAAE,OAAO,MAAM,KAAK;AACzC,cAAc,IAAI,CAAC,MAAM;AACzB,gBAAgB,OAAO,EAAE,CAAC;AAC1B,cAAc,IAAI,GAAG,GAAG,MAAM,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAC1D,cAAc,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5C,aAAa;AACb,YAAY,KAAK;AACjB,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,GAAG,CAAC,2CAA2C,EAAE,CAAC,2BAA2B,EAAE,UAAU,GAAG,YAAY,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAC3Q,CAAC;;;;"}