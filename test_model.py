#!/usr/bin/env python3
"""
测试UI-TARS-1.5-7B模型加载和基本功能
"""

import os
import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import time

def test_model_loading():
    """测试模型加载"""
    print("🚀 开始测试UI-TARS-1.5-7B模型...")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU数量: {torch.cuda.device_count()}")
        print(f"GPU名称: {torch.cuda.get_device_name(0)}")
    
    # 模型路径
    model_path = "models/models--ByteDance-Seed--UI-TARS-1.5-7B/snapshots/683d002dd99d8f95104d31e70391a39348857f4e"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return False
    
    print(f"📁 模型路径: {model_path}")
    
    try:
        print("📥 加载分词器...")
        start_time = time.time()
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        tokenizer_time = time.time() - start_time
        print(f"✅ 分词器加载成功 (耗时: {tokenizer_time:.2f}秒)")
        
        print("📥 加载模型...")
        start_time = time.time()
        
        # 根据是否有GPU选择设备
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🖥️ 使用设备: {device}")
        
        # 加载模型，如果是CPU则使用float32，GPU则可以使用float16
        if device == "cpu":
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float32,
                device_map="auto",
                trust_remote_code=True
            )
        else:
            model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
        
        model_time = time.time() - start_time
        print(f"✅ 模型加载成功 (耗时: {model_time:.2f}秒)")
        
        # 测试简单的文本生成
        print("🧪 测试文本生成...")
        test_prompt = "Hello, I am UI-TARS, an AI assistant that can"
        
        inputs = tokenizer(test_prompt, return_tensors="pt")
        if device == "cuda":
            inputs = {k: v.to(device) for k, v in inputs.items()}
        
        print(f"📝 输入提示: {test_prompt}")
        
        with torch.no_grad():
            start_time = time.time()
            outputs = model.generate(
                **inputs,
                max_new_tokens=50,
                do_sample=True,
                temperature=0.7,
                pad_token_id=tokenizer.eos_token_id
            )
            generation_time = time.time() - start_time
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"🤖 生成文本: {generated_text}")
        print(f"⏱️ 生成耗时: {generation_time:.2f}秒")
        
        print("🎉 模型测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("\n✅ UI-TARS-1.5-7B模型已成功部署并可以正常使用！")
    else:
        print("\n❌ 模型部署测试失败，请检查错误信息。")
    
    sys.exit(0 if success else 1)
