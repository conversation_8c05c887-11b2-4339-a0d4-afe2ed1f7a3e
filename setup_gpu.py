#!/usr/bin/env python3
"""
GPU检测和PyTorch GPU版本安装脚本
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_nvidia_gpu():
    """检查NVIDIA GPU"""
    print("🔍 检查NVIDIA GPU...")
    
    # 检查nvidia-smi
    success, stdout, stderr = run_command("nvidia-smi")
    if success:
        print("✅ 检测到NVIDIA GPU")
        print(stdout)
        return True
    else:
        print("❌ 未检测到NVIDIA GPU或驱动")
        
        # 尝试用PowerShell检查显卡
        success, stdout, stderr = run_command('powershell "Get-WmiObject Win32_VideoController | Select-Object Name"')
        if success:
            print("系统显卡信息：")
            print(stdout)
        
        return False

def check_cuda_version():
    """检查CUDA版本"""
    print("\n🔍 检查CUDA版本...")
    
    success, stdout, stderr = run_command("nvcc --version")
    if success:
        print("✅ CUDA已安装")
        print(stdout)
        return True
    else:
        print("❌ CUDA未安装或不在PATH中")
        return False

def install_gpu_pytorch():
    """安装GPU版本的PyTorch"""
    print("\n📦 安装GPU版本的PyTorch...")
    
    # 卸载当前的CPU版本
    print("卸载当前的CPU版本...")
    run_command("pip uninstall torch torchvision torchaudio -y")
    
    # 安装GPU版本
    print("安装GPU版本...")
    commands = [
        # CUDA 12.1
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121",
        # 如果上面失败，尝试CUDA 11.8
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    ]
    
    for i, cmd in enumerate(commands):
        print(f"尝试安装方案 {i+1}...")
        success, stdout, stderr = run_command(cmd)
        if success:
            print("✅ GPU版本PyTorch安装成功")
            return True
        else:
            print(f"❌ 方案 {i+1} 失败: {stderr}")
    
    print("❌ 所有GPU安装方案都失败，保持CPU版本")
    # 重新安装CPU版本
    run_command("pip install torch torchvision torchaudio")
    return False

def test_gpu():
    """测试GPU是否可用"""
    print("\n🧪 测试GPU可用性...")
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
                props = torch.cuda.get_device_properties(i)
                print(f"  显存: {props.total_memory / 1024**3:.1f} GB")
            return True
        else:
            print("GPU不可用，将使用CPU")
            return False
            
    except ImportError:
        print("❌ PyTorch导入失败")
        return False

def main():
    print("🚀 GPU检测和配置工具")
    print("=" * 50)
    
    # 检查GPU硬件
    has_nvidia = check_nvidia_gpu()
    
    if not has_nvidia:
        print("\n💡 建议:")
        print("1. 如果您有NVIDIA GPU，请安装最新的NVIDIA驱动")
        print("2. 如果没有NVIDIA GPU，将使用CPU运行（速度较慢但可用）")
        print("3. 对于AMD GPU，可以考虑使用ROCm（较复杂）")
        return
    
    # 检查CUDA
    has_cuda = check_cuda_version()
    
    # 尝试安装GPU版本的PyTorch
    if has_nvidia:
        choice = input("\n是否尝试安装GPU版本的PyTorch？(y/N): ").lower()
        if choice == 'y':
            success = install_gpu_pytorch()
            if success:
                test_gpu()
        else:
            print("保持当前配置")
    
    # 最终测试
    test_gpu()
    
    print("\n🎯 配置完成！")
    if has_nvidia:
        print("如果GPU可用，模型将自动使用GPU加速")
    else:
        print("将使用CPU运行，虽然较慢但完全可用")

if __name__ == "__main__":
    main()
