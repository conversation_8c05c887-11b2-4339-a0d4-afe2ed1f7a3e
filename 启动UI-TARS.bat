@echo off
chcp 65001 >nul
title UI-TARS 1.5-7B 本地部署

echo.
echo ========================================
echo    🤖 UI-TARS 1.5-7B 本地部署
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

REM 检查必要文件
if not exist "start_ui_tars.py" (
    echo ❌ 找不到启动脚本 start_ui_tars.py
    echo 请确保所有文件都在同一目录中
    pause
    exit /b 1
)

echo.
echo 🚀 启动UI-TARS...
echo.

REM 启动Python脚本
python start_ui_tars.py

echo.
echo 程序已退出
pause
