# UI-TARS 1.5-7B 本地部署完整指南

## 概述
UI-TARS 1.5-7B 是ByteDance开发的多模态大语言模型，专门用于UI界面理解和交互。本指南将帮助您在本地成功部署该模型。

## 系统要求

### 硬件要求
- **GPU**: NVIDIA GPU，至少16GB显存（推荐24GB+）
- **内存**: 至少32GB RAM
- **存储**: 至少50GB可用空间
- **网络**: 稳定的网络连接（首次下载模型）

### 软件要求
- Python 3.8 或更高版本
- CUDA 11.8+ (NVIDIA GPU用户)
- Git

## 安装步骤

### 1. 环境准备

#### 创建虚拟环境
```bash
# 使用conda
conda create -n ui-tars python=3.10
conda activate ui-tars

# 或使用venv
python -m venv ui-tars-env
# Windows
ui-tars-env\Scripts\activate
# Linux/macOS
source ui-tars-env/bin/activate
```

#### 安装依赖
```bash
# 安装PyTorch (CUDA版本)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装transformers和相关库
pip install transformers>=4.36.0
pip install accelerate
pip install bitsandbytes
pip install pillow
pip install requests
pip install gradio  # 可选，用于Web界面
```

### 2. 模型下载

#### 方法一：自动下载（推荐）
```python
from transformers import AutoTokenizer, AutoModelForCausalLM

# 模型会自动下载到缓存目录
model_name = "ByteDance-Seed/UI-TARS-1.5-7B"
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(model_name, trust_remote_code=True)
```

#### 方法二：手动下载
```bash
# 使用git lfs下载
git lfs install
git clone https://huggingface.co/ByteDance-Seed/UI-TARS-1.5-7B
```

### 3. 运行部署脚本

```bash
# 基本运行
python ui_tars_local_deploy.py

# 指定设备
python ui_tars_local_deploy.py --device cuda

# 使用本地模型路径
python ui_tars_local_deploy.py --model_path ./UI-TARS-1.5-7B
```

## 使用方法

### 基本文本交互
```python
from ui_tars_local_deploy import UITarsLocalServer

# 初始化服务器
server = UITarsLocalServer()
server.load_model()

# 生成响应
response = server.generate_response("请帮我分析这个界面的布局")
print(response)
```

### 图像+文本交互
```python
# 带图像的交互
response = server.generate_response(
    "请描述这个界面的主要元素", 
    image_path="screenshot.png"
)
print(response)
```

## 性能优化

### GPU内存优化
```python
# 使用8bit量化
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    load_in_8bit=True,
    device_map="auto",
    trust_remote_code=True
)
```

### CPU运行优化
```python
# CPU运行时的优化设置
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype=torch.float32,
    trust_remote_code=True,
    low_cpu_mem_usage=True
)
```

## 常见问题解决

### 1. 显存不足
```bash
# 使用量化版本
pip install bitsandbytes
# 在代码中添加 load_in_8bit=True
```

### 2. 下载速度慢
```bash
# 使用镜像源
export HF_ENDPOINT=https://hf-mirror.com
# 或设置代理
export HTTP_PROXY=your_proxy
export HTTPS_PROXY=your_proxy
```

### 3. 模型加载失败
```python
# 检查CUDA是否可用
import torch
print(torch.cuda.is_available())
print(torch.cuda.get_device_name(0))
```

## Web界面部署

创建简单的Web界面：

```python
import gradio as gr
from ui_tars_local_deploy import UITarsLocalServer

server = UITarsLocalServer()
server.load_model()

def chat_interface(message, image):
    if image is not None:
        # 保存临时图像
        image.save("temp_image.png")
        response = server.generate_response(message, "temp_image.png")
    else:
        response = server.generate_response(message)
    return response

# 创建Gradio界面
iface = gr.Interface(
    fn=chat_interface,
    inputs=[
        gr.Textbox(label="输入指令"),
        gr.Image(label="上传图像（可选）", type="pil")
    ],
    outputs=gr.Textbox(label="AI响应"),
    title="UI-TARS 1.5-7B 本地部署",
    description="上传UI截图并输入指令，AI将帮助您分析界面"
)

iface.launch(server_name="0.0.0.0", server_port=7860)
```

## 与桌面应用集成

如果您已经下载了UI-TARS Desktop应用，可以：

1. 将本地部署的模型作为后端服务
2. 修改桌面应用的API端点指向本地服务
3. 享受完全本地化的UI分析体验

## 快速启动

### 一键启动（推荐）
```bash
# Windows用户
双击运行 "启动UI-TARS.bat"

# 或使用Python脚本
python start_ui_tars.py
```

### 手动启动
```bash
# Web界面
python ui_tars_web_interface.py

# 命令行界面
python ui_tars_local_deploy.py
```

## 使用技巧

### 1. 提示词优化
```
# 好的提示词示例：
"请分析这个登录界面的布局，指出可能的用户体验问题"
"这个按钮的颜色和位置是否符合设计规范？"
"如何改进这个表单的可访问性？"

# 避免的提示词：
"这是什么？"  # 太模糊
"帮我"        # 没有具体要求
```

### 2. 图像质量要求
- 分辨率：建议1920x1080或更高
- 格式：PNG、JPG、JPEG
- 清晰度：确保文字和UI元素清晰可见
- 完整性：包含完整的界面内容

### 3. 性能优化建议
```python
# 对于低显存用户
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# 使用量化模型
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    load_in_8bit=True,  # 8位量化
    device_map="auto"
)
```

## 与桌面应用集成

如果您已经安装了UI-TARS Desktop应用：

1. **配置API端点**
   - 打开桌面应用设置
   - 将API端点设置为：`http://localhost:8000`
   - 保存设置并重启应用

2. **启动本地服务**
   ```bash
   python ui_tars_local_deploy.py --port 8000
   ```

3. **验证连接**
   - 在桌面应用中测试功能
   - 确保本地服务正常响应

## 故障排除

### 常见错误及解决方案

#### 1. 模型下载失败
```bash
# 设置镜像源
export HF_ENDPOINT=https://hf-mirror.com
# 或
pip install -U huggingface_hub
huggingface-cli download ByteDance-Seed/UI-TARS-1.5-7B
```

#### 2. CUDA内存不足
```python
# 方案1：使用CPU
python ui_tars_web_interface.py --device cpu

# 方案2：使用量化
# 在代码中添加 load_in_8bit=True
```

#### 3. 依赖冲突
```bash
# 创建新的虚拟环境
conda create -n ui-tars-clean python=3.10
conda activate ui-tars-clean
python install_ui_tars.py
```

#### 4. 网络连接问题
```bash
# 使用代理
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port

# 或离线安装
# 1. 在有网络的机器上下载模型
# 2. 复制到目标机器
# 3. 使用本地路径加载
```

## 高级配置

### 自定义模型路径
```python
# 使用本地模型
server = UITarsLocalServer(model_path="./local_models/UI-TARS-1.5-7B")

# 使用不同的模型版本
server = UITarsLocalServer(model_path="ByteDance-Seed/UI-TARS-2.0-13B")
```

### 批量处理
```python
# 批量分析UI截图
import os
from pathlib import Path

server = UITarsLocalServer()
server.load_model()

image_dir = Path("./ui_screenshots")
for image_path in image_dir.glob("*.png"):
    response = server.generate_response(
        "请分析这个界面的设计质量",
        str(image_path)
    )
    print(f"{image_path.name}: {response}")
```

## 下一步

- 尝试不同的提示词来测试模型能力
- 集成到您的自动化工具中
- 根据需要调整模型参数
- 考虑使用更大的模型版本以获得更好的性能
- 探索与其他工具的集成可能性

## 技术支持

如果遇到问题，请检查：
1. Python和依赖版本是否正确
2. GPU驱动和CUDA版本是否兼容
3. 网络连接是否稳定
4. 系统资源是否充足
5. 查看日志文件获取详细错误信息

### 获取帮助
- 查看GitHub Issues
- 参考官方文档
- 社区论坛讨论

祝您使用愉快！🎉
