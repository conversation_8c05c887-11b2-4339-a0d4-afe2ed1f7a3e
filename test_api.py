import requests
import json

# 测试API连接
def test_api():
    url = "http://localhost:8000/chat/completions"
    
    # 简单的文本测试
    data = {
        "model": "ui-tars-1.5-7b",
        "messages": [
            {
                "role": "user",
                "content": "Hello, can you see this message?"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        print("🧪 测试API连接...")
        response = requests.post(url, json=data, timeout=30)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API响应成功!")
            print(f"📝 回复: {result['choices'][0]['message']['content']}")
        else:
            print(f"❌ API错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 连接错误: {str(e)}")

if __name__ == "__main__":
    test_api()
