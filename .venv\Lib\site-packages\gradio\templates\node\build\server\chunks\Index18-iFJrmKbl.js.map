{"version": 3, "file": "Index18-iFJrmKbl.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index18.js"], "sourcesContent": ["import { create_ssr_component, validate_component, add_attribute, each, escape } from \"svelte/internal\";\nimport \"svelte\";\nimport { n as Block, S as Static, B as BlockLabel, q as Image, k as Empty, I as IconButtonWrapper, F as FullscreenButton } from \"./client.js\";\nimport { r as resolve_wasm_src } from \"./DownloadLink.js\";\nconst css = {\n  code: \".base-image.svelte-303fln.svelte-303fln{display:block;width:100%;height:auto}.container.svelte-303fln.svelte-303fln{display:flex;position:relative;flex-direction:column;justify-content:center;align-items:center;width:var(--size-full);height:var(--size-full)}.image-container.svelte-303fln.svelte-303fln{position:relative;top:0;left:0;flex-grow:1;width:100%;overflow:hidden}.fit-height.svelte-303fln.svelte-303fln{top:0;left:0;width:100%;height:100%;object-fit:contain}.mask.svelte-303fln.svelte-303fln{opacity:0.85;transition:all 0.2s ease-in-out;position:absolute}.image-container.svelte-303fln:hover .mask.svelte-303fln{opacity:0.3}.mask.active.svelte-303fln.svelte-303fln{opacity:1}.mask.inactive.svelte-303fln.svelte-303fln{opacity:0}.legend.svelte-303fln.svelte-303fln{display:flex;flex-direction:row;flex-wrap:wrap;align-content:center;justify-content:center;align-items:center;gap:var(--spacing-sm);padding:var(--spacing-sm)}.legend-item.svelte-303fln.svelte-303fln{display:flex;flex-direction:row;align-items:center;cursor:pointer;border-radius:var(--radius-sm);padding:var(--spacing-sm)}\",\n  map: '{\"version\":3,\"file\":\"Index.svelte\",\"sources\":[\"Index.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport { Block, BlockLabel, Empty, IconButtonWrapper, FullscreenButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Image, Maximize, Minimize } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StatusTracker } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport {} from \\\\\"@gradio/client\\\\\";\\\\nimport { resolve_wasm_src } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nexport let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let visible = true;\\\\nexport let value = null;\\\\nlet old_value = null;\\\\nlet _value = null;\\\\nexport let gradio;\\\\nexport let label = gradio.i18n(\\\\\"annotated_image.annotated_image\\\\\");\\\\nexport let show_label = true;\\\\nexport let show_legend = true;\\\\nexport let height;\\\\nexport let width;\\\\nexport let color_map;\\\\nexport let container = true;\\\\nexport let scale = null;\\\\nexport let min_width = void 0;\\\\nlet active = null;\\\\nexport let loading_status;\\\\nexport let show_fullscreen_button = true;\\\\nlet image_container;\\\\nlet fullscreen = false;\\\\nlet latest_promise = null;\\\\n$: {\\\\n    if (value !== old_value) {\\\\n        old_value = value;\\\\n        gradio.dispatch(\\\\\"change\\\\\");\\\\n    }\\\\n    if (value) {\\\\n        const normalized_value = {\\\\n            image: value.image,\\\\n            annotations: value.annotations.map((ann) => ({\\\\n                image: ann.image,\\\\n                label: ann.label\\\\n            }))\\\\n        };\\\\n        _value = normalized_value;\\\\n        const image_url_promise = resolve_wasm_src(normalized_value.image.url);\\\\n        const annotation_urls_promise = Promise.all(normalized_value.annotations.map((ann) => resolve_wasm_src(ann.image.url)));\\\\n        const current_promise = Promise.all([\\\\n            image_url_promise,\\\\n            annotation_urls_promise\\\\n        ]);\\\\n        latest_promise = current_promise;\\\\n        current_promise.then(([image_url, annotation_urls]) => {\\\\n            if (latest_promise !== current_promise) {\\\\n                return;\\\\n            }\\\\n            const async_resolved_value = {\\\\n                image: {\\\\n                    ...normalized_value.image,\\\\n                    url: image_url ?? void 0\\\\n                },\\\\n                annotations: normalized_value.annotations.map((ann, i) => ({\\\\n                    ...ann,\\\\n                    image: {\\\\n                        ...ann.image,\\\\n                        url: annotation_urls[i] ?? void 0\\\\n                    }\\\\n                }))\\\\n            };\\\\n            _value = async_resolved_value;\\\\n        });\\\\n    }\\\\n    else {\\\\n        _value = null;\\\\n    }\\\\n}\\\\nfunction handle_mouseover(_label) {\\\\n    active = _label;\\\\n}\\\\nfunction handle_mouseout() {\\\\n    active = null;\\\\n}\\\\nfunction handle_click(i, value2) {\\\\n    gradio.dispatch(\\\\\"select\\\\\", {\\\\n        value: label,\\\\n        index: i\\\\n    });\\\\n}\\\\n<\\/script>\\\\n\\\\n<Block\\\\n\\\\t{visible}\\\\n\\\\t{elem_id}\\\\n\\\\t{elem_classes}\\\\n\\\\tpadding={false}\\\\n\\\\t{height}\\\\n\\\\t{width}\\\\n\\\\tallow_overflow={false}\\\\n\\\\t{container}\\\\n\\\\t{scale}\\\\n\\\\t{min_width}\\\\n\\\\tbind:fullscreen\\\\n>\\\\n\\\\t<StatusTracker\\\\n\\\\t\\\\tautoscroll={gradio.autoscroll}\\\\n\\\\t\\\\ti18n={gradio.i18n}\\\\n\\\\t\\\\t{...loading_status}\\\\n\\\\t/>\\\\n\\\\t<BlockLabel\\\\n\\\\t\\\\t{show_label}\\\\n\\\\t\\\\tIcon={Image}\\\\n\\\\t\\\\tlabel={label || gradio.i18n(\\\\\"image.image\\\\\")}\\\\n\\\\t/>\\\\n\\\\n\\\\t<div class=\\\\\"container\\\\\">\\\\n\\\\t\\\\t{#if _value == null}\\\\n\\\\t\\\\t\\\\t<Empty size=\\\\\"large\\\\\" unpadded_box={true}><Image /></Empty>\\\\n\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"image-container\\\\\" bind:this={image_container}>\\\\n\\\\t\\\\t\\\\t\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if show_fullscreen_button}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<FullscreenButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{fullscreen}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:fullscreen={({ detail }) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tfullscreen = detail;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t<img\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"base-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:fit-height={height && !fullscreen}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tsrc={_value ? _value.image.url : null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"the base file that is annotated\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{#each _value ? _value?.annotations : [] as ann, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<img\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"segmentation mask identifying {label} within the uploaded file\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"mask fit-height\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:fit-height={!fullscreen}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:active={active == ann.label}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:inactive={active != ann.label && active != null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={ann.image.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle={color_map && ann.label in color_map\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? null\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: `filter: hue-rotate(${Math.round(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(i * 360) / _value?.annotations.length\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}deg);`}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{#if show_legend && _value}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"legend\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each _value.annotations as ann, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"legend-item\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"background-color: {color_map && ann.label in color_map\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? color_map[ann.label] + \\'88\\'\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: `hsla(${Math.round(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(i * 360) / _value.annotations.length\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t)}, 100%, 50%, 0.3)`}\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseover={() => handle_mouseover(ann.label)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:focus={() => handle_mouseover(ann.label)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:mouseout={() => handle_mouseout()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:blur={() => handle_mouseout()}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => handle_click(i, ann.label)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{ann.label}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n</Block>\\\\n\\\\n<style>\\\\n\\\\t.base-image {\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: auto;\\\\n\\\\t}\\\\n\\\\t.container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\t.image-container {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\t.fit-height {\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}\\\\n\\\\t.mask {\\\\n\\\\t\\\\topacity: 0.85;\\\\n\\\\t\\\\ttransition: all 0.2s ease-in-out;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t}\\\\n\\\\t.image-container:hover .mask {\\\\n\\\\t\\\\topacity: 0.3;\\\\n\\\\t}\\\\n\\\\t.mask.active {\\\\n\\\\t\\\\topacity: 1;\\\\n\\\\t}\\\\n\\\\t.mask.inactive {\\\\n\\\\t\\\\topacity: 0;\\\\n\\\\t}\\\\n\\\\t.legend {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: row;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t\\\\talign-content: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t\\\\tpadding: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\t.legend-item {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: row;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tpadding: var(--spacing-sm);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA+KC,uCAAY,CACX,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CACA,sCAAW,CACV,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CACA,4CAAiB,CAChB,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,SAAS,CAAE,CAAC,CACZ,KAAK,CAAE,IAAI,CACX,QAAQ,CAAE,MACX,CACA,uCAAY,CACX,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,OACb,CACA,iCAAM,CACL,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WAAW,CAChC,QAAQ,CAAE,QACX,CACA,8BAAgB,MAAM,CAAC,mBAAM,CAC5B,OAAO,CAAE,GACV,CACA,KAAK,mCAAQ,CACZ,OAAO,CAAE,CACV,CACA,KAAK,qCAAU,CACd,OAAO,CAAE,CACV,CACA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,GAAG,CACnB,SAAS,CAAE,IAAI,CACf,aAAa,CAAE,MAAM,CACrB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,OAAO,CAAE,IAAI,YAAY,CAC1B,CACA,wCAAa,CACZ,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,GAAG,CACnB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,YAAY,CAC1B\"}'\n};\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = null } = $$props;\n  let old_value = null;\n  let _value = null;\n  let { gradio } = $$props;\n  let { label = gradio.i18n(\"annotated_image.annotated_image\") } = $$props;\n  let { show_label = true } = $$props;\n  let { show_legend = true } = $$props;\n  let { height } = $$props;\n  let { width } = $$props;\n  let { color_map } = $$props;\n  let { container = true } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let active = null;\n  let { loading_status } = $$props;\n  let { show_fullscreen_button = true } = $$props;\n  let image_container;\n  let fullscreen = false;\n  let latest_promise = null;\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.show_legend === void 0 && $$bindings.show_legend && show_legend !== void 0)\n    $$bindings.show_legend(show_legend);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.width === void 0 && $$bindings.width && width !== void 0)\n    $$bindings.width(width);\n  if ($$props.color_map === void 0 && $$bindings.color_map && color_map !== void 0)\n    $$bindings.color_map(color_map);\n  if ($$props.container === void 0 && $$bindings.container && container !== void 0)\n    $$bindings.container(container);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.show_fullscreen_button === void 0 && $$bindings.show_fullscreen_button && show_fullscreen_button !== void 0)\n    $$bindings.show_fullscreen_button(show_fullscreen_button);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      {\n        if (value !== old_value) {\n          old_value = value;\n          gradio.dispatch(\"change\");\n        }\n        if (value) {\n          const normalized_value = {\n            image: value.image,\n            annotations: value.annotations.map((ann) => ({ image: ann.image, label: ann.label }))\n          };\n          _value = normalized_value;\n          const image_url_promise = resolve_wasm_src(normalized_value.image.url);\n          const annotation_urls_promise = Promise.all(normalized_value.annotations.map((ann) => resolve_wasm_src(ann.image.url)));\n          const current_promise = Promise.all([image_url_promise, annotation_urls_promise]);\n          latest_promise = current_promise;\n          current_promise.then(([image_url, annotation_urls]) => {\n            if (latest_promise !== current_promise) {\n              return;\n            }\n            const async_resolved_value = {\n              image: {\n                ...normalized_value.image,\n                url: image_url ?? void 0\n              },\n              annotations: normalized_value.annotations.map((ann, i) => ({\n                ...ann,\n                image: {\n                  ...ann.image,\n                  url: annotation_urls[i] ?? void 0\n                }\n              }))\n            };\n            _value = async_resolved_value;\n          });\n        } else {\n          _value = null;\n        }\n      }\n    }\n    $$rendered = `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        elem_id,\n        elem_classes,\n        padding: false,\n        height,\n        width,\n        allow_overflow: false,\n        container,\n        scale,\n        min_width,\n        fullscreen\n      },\n      {\n        fullscreen: ($$value) => {\n          fullscreen = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${validate_component(BlockLabel, \"BlockLabel\").$$render(\n            $$result,\n            {\n              show_label,\n              Icon: Image,\n              label: label || gradio.i18n(\"image.image\")\n            },\n            {},\n            {}\n          )} <div class=\"container svelte-303fln\">${_value == null ? `${validate_component(Empty, \"Empty\").$$render($$result, { size: \"large\", unpadded_box: true }, {}, {\n            default: () => {\n              return `${validate_component(Image, \"Image\").$$render($$result, {}, {}, {})}`;\n            }\n          })}` : `<div class=\"image-container svelte-303fln\"${add_attribute(\"this\", image_container, 0)}>${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, {}, {}, {\n            default: () => {\n              return `${show_fullscreen_button ? `${validate_component(FullscreenButton, \"FullscreenButton\").$$render($$result, { fullscreen }, {}, {})}` : ``}`;\n            }\n          })} <img class=\"${[\n            \"base-image svelte-303fln\",\n            height && !fullscreen ? \"fit-height\" : \"\"\n          ].join(\" \").trim()}\"${add_attribute(\"src\", _value ? _value.image.url : null, 0)} alt=\"the base file that is annotated\"> ${each(_value ? _value?.annotations : [], (ann, i) => {\n            return `<img alt=\"${\"segmentation mask identifying \" + escape(label, true) + \" within the uploaded file\"}\" class=\"${[\n              \"mask fit-height svelte-303fln\",\n              (!fullscreen ? \"fit-height\" : \"\") + \" \" + (active == ann.label ? \"active\" : \"\") + \" \" + (active != ann.label && active != null ? \"inactive\" : \"\")\n            ].join(\" \").trim()}\"${add_attribute(\"src\", ann.image.url, 0)}${add_attribute(\n              \"style\",\n              color_map && ann.label in color_map ? null : `filter: hue-rotate(${Math.round(i * 360 / _value?.annotations.length)}deg);`,\n              0\n            )}>`;\n          })}</div> ${show_legend && _value ? `<div class=\"legend svelte-303fln\">${each(_value.annotations, (ann, i) => {\n            return `<button class=\"legend-item svelte-303fln\" style=\"${\"background-color: \" + escape(\n              color_map && ann.label in color_map ? color_map[ann.label] + \"88\" : `hsla(${Math.round(i * 360 / _value.annotations.length)}, 100%, 50%, 0.3)`,\n              true\n            )}\">${escape(ann.label)} </button>`;\n          })}</div>` : ``}`}</div>`;\n        }\n      }\n    )}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  Index as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,0kCAA0kC;AACllC,EAAE,GAAG,EAAE,u1QAAu1Q;AAC91Q,CAAC,CAAC;AACG,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,EAAE,GAAG,OAAO,CAAC;AAC3E,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,sBAAsB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAClD,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC;AACzB,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,sBAAsB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sBAAsB,IAAI,sBAAsB,KAAK,KAAK,CAAC;AACzH,IAAI,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;AAC9D,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;AACjC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,UAAU,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpC,SAAS;AACT,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,MAAM,gBAAgB,GAAG;AACnC,YAAY,KAAK,EAAE,KAAK,CAAC,KAAK;AAC9B,YAAY,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;AACjG,WAAW,CAAC;AACZ,UAAU,MAAM,GAAG,gBAAgB,CAAC;AACpC,UAAU,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjF,UAAU,MAAM,uBAAuB,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClI,UAAU,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,uBAAuB,CAAC,CAAC,CAAC;AAC5F,UAAU,cAAc,GAAG,eAAe,CAAC;AAC3C,UAAU,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,eAAe,CAAC,KAAK;AACjE,YAAY,IAAI,cAAc,KAAK,eAAe,EAAE;AACpD,cAAc,OAAO;AACrB,aAAa;AACb,YAAY,MAAM,oBAAoB,GAAG;AACzC,cAAc,KAAK,EAAE;AACrB,gBAAgB,GAAG,gBAAgB,CAAC,KAAK;AACzC,gBAAgB,GAAG,EAAE,SAAS,IAAI,KAAK,CAAC;AACxC,eAAe;AACf,cAAc,WAAW,EAAE,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM;AACzE,gBAAgB,GAAG,GAAG;AACtB,gBAAgB,KAAK,EAAE;AACvB,kBAAkB,GAAG,GAAG,CAAC,KAAK;AAC9B,kBAAkB,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;AACnD,iBAAiB;AACjB,eAAe,CAAC,CAAC;AACjB,aAAa,CAAC;AACd,YAAY,MAAM,GAAG,oBAAoB,CAAC;AAC1C,WAAW,CAAC,CAAC;AACb,SAAS,MAAM;AACf,UAAU,MAAM,GAAG,IAAI,CAAC;AACxB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC/D,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,OAAO;AACP,MAAM;AACN,QAAQ,UAAU,EAAE,CAAC,OAAO,KAAK;AACjC,UAAU,UAAU,GAAG,OAAO,CAAC;AAC/B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAChP,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU;AACxB,cAAc,IAAI,EAAE,KAAK;AACzB,cAAc,KAAK,EAAE,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;AACxD,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,sCAAsC,EAAE,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;AACzK,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5F,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,0CAA0C,EAAE,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AACjM,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,sBAAsB,GAAG,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjK,aAAa;AACb,WAAW,CAAC,CAAC,aAAa,EAAE;AAC5B,YAAY,0BAA0B;AACtC,YAAY,MAAM,IAAI,CAAC,UAAU,GAAG,YAAY,GAAG,EAAE;AACrD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,wCAAwC,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK;AACxL,YAAY,OAAO,CAAC,UAAU,EAAE,gCAAgC,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,2BAA2B,CAAC,SAAS,EAAE;AAChI,cAAc,+BAA+B;AAC7C,cAAc,CAAC,CAAC,UAAU,GAAG,YAAY,GAAG,EAAE,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC,KAAK,IAAI,MAAM,IAAI,IAAI,GAAG,UAAU,GAAG,EAAE,CAAC;AAC/J,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa;AACxF,cAAc,OAAO;AACrB,cAAc,SAAS,IAAI,GAAG,CAAC,KAAK,IAAI,SAAS,GAAG,IAAI,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;AACxI,cAAc,CAAC;AACf,aAAa,CAAC,CAAC,CAAC,CAAC;AACjB,WAAW,CAAC,CAAC,OAAO,EAAE,WAAW,IAAI,MAAM,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK;AACxH,YAAY,OAAO,CAAC,iDAAiD,EAAE,oBAAoB,GAAG,MAAM;AACpG,cAAc,SAAS,IAAI,GAAG,CAAC,KAAK,IAAI,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,iBAAiB,CAAC;AAC5J,cAAc,IAAI;AAClB,aAAa,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;AAChD,WAAW,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACpC,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}