#!/usr/bin/env python3
"""
UI-TARS 快速启动脚本
提供多种启动选项
"""

import argparse
import sys
import os
import subprocess
from pathlib import Path

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        "ui_tars_local_deploy.py",
        "ui_tars_web_interface.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("请确保所有脚本文件都在当前目录中")
        return False
    
    return True

def start_web_interface(host="127.0.0.1", port=7860, share=False):
    """启动Web界面"""
    print("🚀 启动UI-TARS Web界面...")
    
    cmd = [
        sys.executable, 
        "ui_tars_web_interface.py",
        "--host", host,
        "--port", str(port)
    ]
    
    if share:
        cmd.append("--share")
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 Web界面已关闭")

def start_cli_interface():
    """启动命令行界面"""
    print("🚀 启动UI-TARS 命令行界面...")
    
    try:
        subprocess.run([sys.executable, "ui_tars_local_deploy.py"])
    except KeyboardInterrupt:
        print("\n👋 命令行界面已关闭")

def install_dependencies():
    """安装依赖"""
    print("📦 开始安装依赖...")
    try:
        subprocess.run([sys.executable, "install_ui_tars.py"])
    except FileNotFoundError:
        print("❌ 找不到安装脚本 install_ui_tars.py")

def show_menu():
    """显示菜单"""
    print("""
🤖 UI-TARS 1.5-7B 本地部署

请选择启动方式：
1. Web界面 (推荐) - 在浏览器中使用
2. 命令行界面 - 在终端中使用  
3. 安装/更新依赖
4. 查看帮助
5. 退出

""")

def show_help():
    """显示帮助信息"""
    print("""
📖 UI-TARS 使用帮助

🔧 安装步骤：
1. 确保Python 3.8+已安装
2. 运行此脚本选择"安装/更新依赖"
3. 等待安装完成
4. 选择启动方式开始使用

💻 启动方式：
- Web界面：提供友好的图形界面，支持图像上传
- 命令行：适合开发者和高级用户

⚙️ 系统要求：
- Python 3.8+
- 16GB+ GPU显存 (推荐) 或 32GB+ 内存 (CPU模式)
- 50GB+ 可用存储空间

🌐 Web界面功能：
- 文本对话
- 图像分析
- UI界面理解
- 参数调节

📝 命令行参数：
python start_ui_tars.py --web --host 0.0.0.0 --port 7860
python start_ui_tars.py --cli
python start_ui_tars.py --install

🔗 更多信息：
查看 UI-TARS_部署指南.md 获取详细说明
""")

def main():
    parser = argparse.ArgumentParser(description="UI-TARS 启动脚本")
    parser.add_argument("--web", action="store_true", help="启动Web界面")
    parser.add_argument("--cli", action="store_true", help="启动命令行界面")
    parser.add_argument("--install", action="store_true", help="安装依赖")
    parser.add_argument("--host", default="127.0.0.1", help="Web服务主机")
    parser.add_argument("--port", type=int, default=7860, help="Web服务端口")
    parser.add_argument("--share", action="store_true", help="创建公共链接")
    
    args = parser.parse_args()
    
    # 检查文件
    if not check_files():
        return
    
    # 根据参数执行相应操作
    if args.install:
        install_dependencies()
        return
    
    if args.web:
        start_web_interface(args.host, args.port, args.share)
        return
    
    if args.cli:
        start_cli_interface()
        return
    
    # 如果没有参数，显示交互菜单
    while True:
        show_menu()
        try:
            choice = input("请输入选项 (1-5): ").strip()
            
            if choice == "1":
                print("\n启动Web界面...")
                print("💡 提示：首次运行会下载模型，请耐心等待")
                print(f"🌐 启动后访问: http://127.0.0.1:7860")
                input("按回车键继续...")
                start_web_interface()
                
            elif choice == "2":
                print("\n启动命令行界面...")
                print("💡 提示：首次运行会下载模型，请耐心等待")
                input("按回车键继续...")
                start_cli_interface()
                
            elif choice == "3":
                install_dependencies()
                
            elif choice == "4":
                show_help()
                input("\n按回车键返回菜单...")
                
            elif choice == "5":
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except EOFError:
            print("\n\n👋 再见！")
            break

if __name__ == "__main__":
    main()
