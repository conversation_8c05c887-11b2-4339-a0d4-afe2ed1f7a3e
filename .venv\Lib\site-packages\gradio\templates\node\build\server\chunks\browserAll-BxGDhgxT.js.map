{"version": 3, "file": "browserAll-BxGDhgxT.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/browserAll.js"], "sourcesContent": ["import { P as Point, E as ExtensionType, r as removeItems, T as Ticker, U as UPDATE_PRIORITY, a as EventEmitter, w as warn, e as extensions, C as Container } from \"./Index3.js\";\nimport \"./init.js\";\nclass FederatedEvent {\n  /**\n   * @param manager - The event boundary which manages this event. Propagation can only occur\n   *  within the boundary's jurisdiction.\n   */\n  constructor(manager) {\n    this.bubbles = true;\n    this.cancelBubble = true;\n    this.cancelable = false;\n    this.composed = false;\n    this.defaultPrevented = false;\n    this.eventPhase = FederatedEvent.prototype.NONE;\n    this.propagationStopped = false;\n    this.propagationImmediatelyStopped = false;\n    this.layer = new Point();\n    this.page = new Point();\n    this.NONE = 0;\n    this.CAPTURING_PHASE = 1;\n    this.AT_TARGET = 2;\n    this.BUBBLING_PHASE = 3;\n    this.manager = manager;\n  }\n  /** @readonly */\n  get layerX() {\n    return this.layer.x;\n  }\n  /** @readonly */\n  get layerY() {\n    return this.layer.y;\n  }\n  /** @readonly */\n  get pageX() {\n    return this.page.x;\n  }\n  /** @readonly */\n  get pageY() {\n    return this.page.y;\n  }\n  /**\n   * Fallback for the deprecated @code{InteractionEvent.data}.\n   * @deprecated since 7.0.0\n   */\n  get data() {\n    return this;\n  }\n  /** The propagation path for this event. Alias for {@link EventBoundary.propagationPath}. */\n  composedPath() {\n    if (this.manager && (!this.path || this.path[this.path.length - 1] !== this.target)) {\n      this.path = this.target ? this.manager.propagationPath(this.target) : [];\n    }\n    return this.path;\n  }\n  /**\n   * Unimplemented method included for implementing the DOM interface {@code Event}. It will throw an {@code Error}.\n   * @deprecated\n   * @param _type\n   * @param _bubbles\n   * @param _cancelable\n   */\n  initEvent(_type, _bubbles, _cancelable) {\n    throw new Error(\"initEvent() is a legacy DOM API. It is not implemented in the Federated Events API.\");\n  }\n  /**\n   * Unimplemented method included for implementing the DOM interface {@code UIEvent}. It will throw an {@code Error}.\n   * @deprecated\n   * @param _typeArg\n   * @param _bubblesArg\n   * @param _cancelableArg\n   * @param _viewArg\n   * @param _detailArg\n   */\n  initUIEvent(_typeArg, _bubblesArg, _cancelableArg, _viewArg, _detailArg) {\n    throw new Error(\"initUIEvent() is a legacy DOM API. It is not implemented in the Federated Events API.\");\n  }\n  /** Prevent default behavior of PixiJS and the user agent. */\n  preventDefault() {\n    if (this.nativeEvent instanceof Event && this.nativeEvent.cancelable) {\n      this.nativeEvent.preventDefault();\n    }\n    this.defaultPrevented = true;\n  }\n  /**\n   * Stop this event from propagating to any addition listeners, including on the\n   * {@link FederatedEventTarget.currentTarget currentTarget} and also the following\n   * event targets on the propagation path.\n   */\n  stopImmediatePropagation() {\n    this.propagationImmediatelyStopped = true;\n  }\n  /**\n   * Stop this event from propagating to the next {@link FederatedEventTarget}. The rest of the listeners\n   * on the {@link FederatedEventTarget.currentTarget currentTarget} will still be notified.\n   */\n  stopPropagation() {\n    this.propagationStopped = true;\n  }\n}\nvar appleIphone = /iPhone/i;\nvar appleIpod = /iPod/i;\nvar appleTablet = /iPad/i;\nvar appleUniversal = /\\biOS-universal(?:.+)Mac\\b/i;\nvar androidPhone = /\\bAndroid(?:.+)Mobile\\b/i;\nvar androidTablet = /Android/i;\nvar amazonPhone = /(?:SD4930UR|\\bSilk(?:.+)Mobile\\b)/i;\nvar amazonTablet = /Silk/i;\nvar windowsPhone = /Windows Phone/i;\nvar windowsTablet = /\\bWindows(?:.+)ARM\\b/i;\nvar otherBlackBerry = /BlackBerry/i;\nvar otherBlackBerry10 = /BB10/i;\nvar otherOpera = /Opera Mini/i;\nvar otherChrome = /\\b(CriOS|Chrome)(?:.+)Mobile/i;\nvar otherFirefox = /Mobile(?:.+)Firefox\\b/i;\nvar isAppleTabletOnIos13 = function(navigator2) {\n  return typeof navigator2 !== \"undefined\" && navigator2.platform === \"MacIntel\" && typeof navigator2.maxTouchPoints === \"number\" && navigator2.maxTouchPoints > 1 && typeof MSStream === \"undefined\";\n};\nfunction createMatch(userAgent) {\n  return function(regex) {\n    return regex.test(userAgent);\n  };\n}\nfunction isMobile$1(param) {\n  var nav = {\n    userAgent: \"\",\n    platform: \"\",\n    maxTouchPoints: 0\n  };\n  if (!param && typeof navigator !== \"undefined\") {\n    nav = {\n      userAgent: navigator.userAgent,\n      platform: navigator.platform,\n      maxTouchPoints: navigator.maxTouchPoints || 0\n    };\n  } else if (typeof param === \"string\") {\n    nav.userAgent = param;\n  } else if (param && param.userAgent) {\n    nav = {\n      userAgent: param.userAgent,\n      platform: param.platform,\n      maxTouchPoints: param.maxTouchPoints || 0\n    };\n  }\n  var userAgent = nav.userAgent;\n  var tmp = userAgent.split(\"[FBAN\");\n  if (typeof tmp[1] !== \"undefined\") {\n    userAgent = tmp[0];\n  }\n  tmp = userAgent.split(\"Twitter\");\n  if (typeof tmp[1] !== \"undefined\") {\n    userAgent = tmp[0];\n  }\n  var match = createMatch(userAgent);\n  var result = {\n    apple: {\n      phone: match(appleIphone) && !match(windowsPhone),\n      ipod: match(appleIpod),\n      tablet: !match(appleIphone) && (match(appleTablet) || isAppleTabletOnIos13(nav)) && !match(windowsPhone),\n      universal: match(appleUniversal),\n      device: (match(appleIphone) || match(appleIpod) || match(appleTablet) || match(appleUniversal) || isAppleTabletOnIos13(nav)) && !match(windowsPhone)\n    },\n    amazon: {\n      phone: match(amazonPhone),\n      tablet: !match(amazonPhone) && match(amazonTablet),\n      device: match(amazonPhone) || match(amazonTablet)\n    },\n    android: {\n      phone: !match(windowsPhone) && match(amazonPhone) || !match(windowsPhone) && match(androidPhone),\n      tablet: !match(windowsPhone) && !match(amazonPhone) && !match(androidPhone) && (match(amazonTablet) || match(androidTablet)),\n      device: !match(windowsPhone) && (match(amazonPhone) || match(amazonTablet) || match(androidPhone) || match(androidTablet)) || match(/\\bokhttp\\b/i)\n    },\n    windows: {\n      phone: match(windowsPhone),\n      tablet: match(windowsTablet),\n      device: match(windowsPhone) || match(windowsTablet)\n    },\n    other: {\n      blackberry: match(otherBlackBerry),\n      blackberry10: match(otherBlackBerry10),\n      opera: match(otherOpera),\n      firefox: match(otherFirefox),\n      chrome: match(otherChrome),\n      device: match(otherBlackBerry) || match(otherBlackBerry10) || match(otherOpera) || match(otherFirefox) || match(otherChrome)\n    },\n    any: false,\n    phone: false,\n    tablet: false\n  };\n  result.any = result.apple.device || result.android.device || result.windows.device || result.other.device;\n  result.phone = result.apple.phone || result.android.phone || result.windows.phone;\n  result.tablet = result.apple.tablet || result.android.tablet || result.windows.tablet;\n  return result;\n}\nconst isMobileCall = isMobile$1.default ?? isMobile$1;\nconst isMobile = isMobileCall(globalThis.navigator);\nconst KEY_CODE_TAB = 9;\nconst DIV_TOUCH_SIZE = 100;\nconst DIV_TOUCH_POS_X = 0;\nconst DIV_TOUCH_POS_Y = 0;\nconst DIV_TOUCH_ZINDEX = 2;\nconst DIV_HOOK_SIZE = 1;\nconst DIV_HOOK_POS_X = -1e3;\nconst DIV_HOOK_POS_Y = -1e3;\nconst DIV_HOOK_ZINDEX = 2;\nconst _AccessibilitySystem = class _AccessibilitySystem2 {\n  // 2fps\n  // eslint-disable-next-line jsdoc/require-param\n  /**\n   * @param {WebGLRenderer|WebGPURenderer} renderer - A reference to the current renderer\n   */\n  constructor(renderer, _mobileInfo = isMobile) {\n    this._mobileInfo = _mobileInfo;\n    this.debug = false;\n    this._activateOnTab = true;\n    this._deactivateOnMouseMove = true;\n    this._isActive = false;\n    this._isMobileAccessibility = false;\n    this._div = null;\n    this._pool = [];\n    this._renderId = 0;\n    this._children = [];\n    this._androidUpdateCount = 0;\n    this._androidUpdateFrequency = 500;\n    this._hookDiv = null;\n    if (_mobileInfo.tablet || _mobileInfo.phone) {\n      this._createTouchHook();\n    }\n    this._renderer = renderer;\n  }\n  /**\n   * Value of `true` if accessibility is currently active and accessibility layers are showing.\n   * @member {boolean}\n   * @readonly\n   */\n  get isActive() {\n    return this._isActive;\n  }\n  /**\n   * Value of `true` if accessibility is enabled for touch devices.\n   * @member {boolean}\n   * @readonly\n   */\n  get isMobileAccessibility() {\n    return this._isMobileAccessibility;\n  }\n  get hookDiv() {\n    return this._hookDiv;\n  }\n  /**\n   * Creates the touch hooks.\n   * @private\n   */\n  _createTouchHook() {\n    const hookDiv = document.createElement(\"button\");\n    hookDiv.style.width = `${DIV_HOOK_SIZE}px`;\n    hookDiv.style.height = `${DIV_HOOK_SIZE}px`;\n    hookDiv.style.position = \"absolute\";\n    hookDiv.style.top = `${DIV_HOOK_POS_X}px`;\n    hookDiv.style.left = `${DIV_HOOK_POS_Y}px`;\n    hookDiv.style.zIndex = DIV_HOOK_ZINDEX.toString();\n    hookDiv.style.backgroundColor = \"#FF0000\";\n    hookDiv.title = \"select to enable accessibility for this content\";\n    hookDiv.addEventListener(\"focus\", () => {\n      this._isMobileAccessibility = true;\n      this._activate();\n      this._destroyTouchHook();\n    });\n    document.body.appendChild(hookDiv);\n    this._hookDiv = hookDiv;\n  }\n  /**\n   * Destroys the touch hooks.\n   * @private\n   */\n  _destroyTouchHook() {\n    if (!this._hookDiv) {\n      return;\n    }\n    document.body.removeChild(this._hookDiv);\n    this._hookDiv = null;\n  }\n  /**\n   * Activating will cause the Accessibility layer to be shown.\n   * This is called when a user presses the tab key.\n   * @private\n   */\n  _activate() {\n    if (this._isActive) {\n      return;\n    }\n    this._isActive = true;\n    if (!this._div) {\n      this._div = document.createElement(\"div\");\n      this._div.style.width = `${DIV_TOUCH_SIZE}px`;\n      this._div.style.height = `${DIV_TOUCH_SIZE}px`;\n      this._div.style.position = \"absolute\";\n      this._div.style.top = `${DIV_TOUCH_POS_X}px`;\n      this._div.style.left = `${DIV_TOUCH_POS_Y}px`;\n      this._div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n      this._div.style.pointerEvents = \"none\";\n    }\n    if (this._activateOnTab) {\n      this._onKeyDown = this._onKeyDown.bind(this);\n      globalThis.addEventListener(\"keydown\", this._onKeyDown, false);\n    }\n    if (this._deactivateOnMouseMove) {\n      this._onMouseMove = this._onMouseMove.bind(this);\n      globalThis.document.addEventListener(\"mousemove\", this._onMouseMove, true);\n    }\n    const canvas = this._renderer.view.canvas;\n    if (!canvas.parentNode) {\n      const observer = new MutationObserver(() => {\n        if (canvas.parentNode) {\n          canvas.parentNode.appendChild(this._div);\n          observer.disconnect();\n          this._initAccessibilitySetup();\n        }\n      });\n      observer.observe(document.body, { childList: true, subtree: true });\n    } else {\n      canvas.parentNode.appendChild(this._div);\n      this._initAccessibilitySetup();\n    }\n  }\n  // New method to handle initialization after div is ready\n  _initAccessibilitySetup() {\n    this._renderer.runners.postrender.add(this);\n    if (this._renderer.lastObjectRendered) {\n      this._updateAccessibleObjects(this._renderer.lastObjectRendered);\n    }\n  }\n  /**\n   * Deactivates the accessibility system. Removes listeners and accessibility elements.\n   * @private\n   */\n  _deactivate() {\n    if (!this._isActive || this._isMobileAccessibility) {\n      return;\n    }\n    this._isActive = false;\n    globalThis.document.removeEventListener(\"mousemove\", this._onMouseMove, true);\n    if (this._activateOnTab) {\n      globalThis.addEventListener(\"keydown\", this._onKeyDown, false);\n    }\n    this._renderer.runners.postrender.remove(this);\n    for (const child of this._children) {\n      if (child._accessibleDiv && child._accessibleDiv.parentNode) {\n        child._accessibleDiv.parentNode.removeChild(child._accessibleDiv);\n        child._accessibleDiv = null;\n      }\n      child._accessibleActive = false;\n    }\n    this._pool.forEach((div) => {\n      if (div.parentNode) {\n        div.parentNode.removeChild(div);\n      }\n    });\n    if (this._div && this._div.parentNode) {\n      this._div.parentNode.removeChild(this._div);\n    }\n    this._pool = [];\n    this._children = [];\n  }\n  /**\n   * This recursive function will run through the scene graph and add any new accessible objects to the DOM layer.\n   * @private\n   * @param {Container} container - The Container to check.\n   */\n  _updateAccessibleObjects(container) {\n    if (!container.visible || !container.accessibleChildren) {\n      return;\n    }\n    if (container.accessible) {\n      if (!container._accessibleActive) {\n        this._addChild(container);\n      }\n      container._renderId = this._renderId;\n    }\n    const children = container.children;\n    if (children) {\n      for (let i = 0; i < children.length; i++) {\n        this._updateAccessibleObjects(children[i]);\n      }\n    }\n  }\n  /**\n   * Runner init called, view is available at this point.\n   * @ignore\n   */\n  init(options) {\n    const defaultOpts = _AccessibilitySystem2.defaultOptions;\n    const mergedOptions = {\n      accessibilityOptions: {\n        ...defaultOpts,\n        ...options?.accessibilityOptions || {}\n      }\n    };\n    this.debug = mergedOptions.accessibilityOptions.debug;\n    this._activateOnTab = mergedOptions.accessibilityOptions.activateOnTab;\n    this._deactivateOnMouseMove = mergedOptions.accessibilityOptions.deactivateOnMouseMove;\n    if (mergedOptions.accessibilityOptions.enabledByDefault) {\n      this._activate();\n    } else if (this._activateOnTab) {\n      this._onKeyDown = this._onKeyDown.bind(this);\n      globalThis.addEventListener(\"keydown\", this._onKeyDown, false);\n    }\n    this._renderer.runners.postrender.remove(this);\n  }\n  /**\n   * Updates the accessibility layer during rendering.\n   * - Removes divs for containers no longer in the scene\n   * - Updates the position and dimensions of the root div\n   * - Updates positions of active accessibility divs\n   * Only fires while the accessibility system is active.\n   * @ignore\n   */\n  postrender() {\n    const now = performance.now();\n    if (this._mobileInfo.android.device && now < this._androidUpdateCount) {\n      return;\n    }\n    this._androidUpdateCount = now + this._androidUpdateFrequency;\n    if (!this._renderer.renderingToScreen || !this._renderer.view.canvas) {\n      return;\n    }\n    const activeIds = /* @__PURE__ */ new Set();\n    if (this._renderer.lastObjectRendered) {\n      this._updateAccessibleObjects(this._renderer.lastObjectRendered);\n      for (const child of this._children) {\n        if (child._renderId === this._renderId) {\n          activeIds.add(this._children.indexOf(child));\n        }\n      }\n    }\n    for (let i = this._children.length - 1; i >= 0; i--) {\n      const child = this._children[i];\n      if (!activeIds.has(i)) {\n        if (child._accessibleDiv && child._accessibleDiv.parentNode) {\n          child._accessibleDiv.parentNode.removeChild(child._accessibleDiv);\n          this._pool.push(child._accessibleDiv);\n          child._accessibleDiv = null;\n        }\n        child._accessibleActive = false;\n        removeItems(this._children, i, 1);\n      }\n    }\n    if (this._renderer.renderingToScreen) {\n      const { x, y, width: viewWidth, height: viewHeight } = this._renderer.screen;\n      const div = this._div;\n      div.style.left = `${x}px`;\n      div.style.top = `${y}px`;\n      div.style.width = `${viewWidth}px`;\n      div.style.height = `${viewHeight}px`;\n    }\n    for (let i = 0; i < this._children.length; i++) {\n      const child = this._children[i];\n      if (!child._accessibleActive || !child._accessibleDiv) {\n        continue;\n      }\n      const div = child._accessibleDiv;\n      const hitArea = child.hitArea || child.getBounds().rectangle;\n      if (child.hitArea) {\n        const wt = child.worldTransform;\n        const sx = this._renderer.resolution;\n        const sy = this._renderer.resolution;\n        div.style.left = `${(wt.tx + hitArea.x * wt.a) * sx}px`;\n        div.style.top = `${(wt.ty + hitArea.y * wt.d) * sy}px`;\n        div.style.width = `${hitArea.width * wt.a * sx}px`;\n        div.style.height = `${hitArea.height * wt.d * sy}px`;\n      } else {\n        this._capHitArea(hitArea);\n        const sx = this._renderer.resolution;\n        const sy = this._renderer.resolution;\n        div.style.left = `${hitArea.x * sx}px`;\n        div.style.top = `${hitArea.y * sy}px`;\n        div.style.width = `${hitArea.width * sx}px`;\n        div.style.height = `${hitArea.height * sy}px`;\n      }\n    }\n    this._renderId++;\n  }\n  /**\n   * private function that will visually add the information to the\n   * accessibility div\n   * @param {HTMLElement} div -\n   */\n  _updateDebugHTML(div) {\n    div.innerHTML = `type: ${div.type}</br> title : ${div.title}</br> tabIndex: ${div.tabIndex}`;\n  }\n  /**\n   * Adjust the hit area based on the bounds of a display object\n   * @param {Rectangle} hitArea - Bounds of the child\n   */\n  _capHitArea(hitArea) {\n    if (hitArea.x < 0) {\n      hitArea.width += hitArea.x;\n      hitArea.x = 0;\n    }\n    if (hitArea.y < 0) {\n      hitArea.height += hitArea.y;\n      hitArea.y = 0;\n    }\n    const { width: viewWidth, height: viewHeight } = this._renderer;\n    if (hitArea.x + hitArea.width > viewWidth) {\n      hitArea.width = viewWidth - hitArea.x;\n    }\n    if (hitArea.y + hitArea.height > viewHeight) {\n      hitArea.height = viewHeight - hitArea.y;\n    }\n  }\n  /**\n   * Creates or reuses a div element for a Container and adds it to the accessibility layer.\n   * Sets up ARIA attributes, event listeners, and positioning based on the container's properties.\n   * @private\n   * @param {Container} container - The child to make accessible.\n   */\n  _addChild(container) {\n    let div = this._pool.pop();\n    if (!div) {\n      if (container.accessibleType === \"button\") {\n        div = document.createElement(\"button\");\n      } else {\n        div = document.createElement(container.accessibleType);\n        div.style.cssText = `\n                        color: transparent;\n                        pointer-events: none;\n                        padding: 0;\n                        margin: 0;\n                        border: 0;\n                        outline: 0;\n                        background: transparent;\n                        box-sizing: border-box;\n                        user-select: none;\n                        -webkit-user-select: none;\n                        -moz-user-select: none;\n                        -ms-user-select: none;\n                    `;\n        if (container.accessibleText) {\n          div.innerText = container.accessibleText;\n        }\n      }\n      div.style.width = `${DIV_TOUCH_SIZE}px`;\n      div.style.height = `${DIV_TOUCH_SIZE}px`;\n      div.style.backgroundColor = this.debug ? \"rgba(255,255,255,0.5)\" : \"transparent\";\n      div.style.position = \"absolute\";\n      div.style.zIndex = DIV_TOUCH_ZINDEX.toString();\n      div.style.borderStyle = \"none\";\n      if (navigator.userAgent.toLowerCase().includes(\"chrome\")) {\n        div.setAttribute(\"aria-live\", \"off\");\n      } else {\n        div.setAttribute(\"aria-live\", \"polite\");\n      }\n      if (navigator.userAgent.match(/rv:.*Gecko\\//)) {\n        div.setAttribute(\"aria-relevant\", \"additions\");\n      } else {\n        div.setAttribute(\"aria-relevant\", \"text\");\n      }\n      div.addEventListener(\"click\", this._onClick.bind(this));\n      div.addEventListener(\"focus\", this._onFocus.bind(this));\n      div.addEventListener(\"focusout\", this._onFocusOut.bind(this));\n    }\n    div.style.pointerEvents = container.accessiblePointerEvents;\n    div.type = container.accessibleType;\n    if (container.accessibleTitle && container.accessibleTitle !== null) {\n      div.title = container.accessibleTitle;\n    } else if (!container.accessibleHint || container.accessibleHint === null) {\n      div.title = `container ${container.tabIndex}`;\n    }\n    if (container.accessibleHint && container.accessibleHint !== null) {\n      div.setAttribute(\"aria-label\", container.accessibleHint);\n    }\n    if (this.debug) {\n      this._updateDebugHTML(div);\n    }\n    container._accessibleActive = true;\n    container._accessibleDiv = div;\n    div.container = container;\n    this._children.push(container);\n    this._div.appendChild(container._accessibleDiv);\n    if (container.interactive) {\n      container._accessibleDiv.tabIndex = container.tabIndex;\n    }\n  }\n  /**\n   * Dispatch events with the EventSystem.\n   * @param e\n   * @param type\n   * @private\n   */\n  _dispatchEvent(e, type) {\n    const { container: target } = e.target;\n    const boundary = this._renderer.events.rootBoundary;\n    const event = Object.assign(new FederatedEvent(boundary), { target });\n    boundary.rootTarget = this._renderer.lastObjectRendered;\n    type.forEach((type2) => boundary.dispatchEvent(event, type2));\n  }\n  /**\n   * Maps the div button press to pixi's EventSystem (click)\n   * @private\n   * @param {MouseEvent} e - The click event.\n   */\n  _onClick(e) {\n    this._dispatchEvent(e, [\"click\", \"pointertap\", \"tap\"]);\n  }\n  /**\n   * Maps the div focus events to pixi's EventSystem (mouseover)\n   * @private\n   * @param {FocusEvent} e - The focus event.\n   */\n  _onFocus(e) {\n    if (!e.target.getAttribute(\"aria-live\")) {\n      e.target.setAttribute(\"aria-live\", \"assertive\");\n    }\n    this._dispatchEvent(e, [\"mouseover\"]);\n  }\n  /**\n   * Maps the div focus events to pixi's EventSystem (mouseout)\n   * @private\n   * @param {FocusEvent} e - The focusout event.\n   */\n  _onFocusOut(e) {\n    if (!e.target.getAttribute(\"aria-live\")) {\n      e.target.setAttribute(\"aria-live\", \"polite\");\n    }\n    this._dispatchEvent(e, [\"mouseout\"]);\n  }\n  /**\n   * Is called when a key is pressed\n   * @private\n   * @param {KeyboardEvent} e - The keydown event.\n   */\n  _onKeyDown(e) {\n    if (e.keyCode !== KEY_CODE_TAB || !this._activateOnTab) {\n      return;\n    }\n    this._activate();\n  }\n  /**\n   * Is called when the mouse moves across the renderer element\n   * @private\n   * @param {MouseEvent} e - The mouse event.\n   */\n  _onMouseMove(e) {\n    if (e.movementX === 0 && e.movementY === 0) {\n      return;\n    }\n    this._deactivate();\n  }\n  /** Destroys the accessibility system. Removes all elements and listeners. */\n  destroy() {\n    this._deactivate();\n    this._destroyTouchHook();\n    this._div = null;\n    this._pool = null;\n    this._children = null;\n    this._renderer = null;\n    if (this._activateOnTab) {\n      globalThis.removeEventListener(\"keydown\", this._onKeyDown);\n    }\n  }\n  /**\n   * Enables or disables the accessibility system.\n   * @param enabled - Whether to enable or disable accessibility.\n   */\n  setAccessibilityEnabled(enabled) {\n    if (enabled) {\n      this._activate();\n    } else {\n      this._deactivate();\n    }\n  }\n};\n_AccessibilitySystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"accessibility\"\n};\n_AccessibilitySystem.defaultOptions = {\n  /**\n   * Whether to enable accessibility features on initialization\n   * @default false\n   */\n  enabledByDefault: false,\n  /**\n   * Whether to visually show the accessibility divs for debugging\n   * @default false\n   */\n  debug: false,\n  /**\n   * Whether to activate accessibility when tab key is pressed\n   * @default true\n   */\n  activateOnTab: true,\n  /**\n   * Whether to deactivate accessibility when mouse moves\n   * @default true\n   */\n  deactivateOnMouseMove: true\n};\nlet AccessibilitySystem = _AccessibilitySystem;\nconst accessibilityTarget = {\n  /**\n   * Flag for if the object is accessible. If true AccessibilityManager will overlay a\n   * shadow div with attributes set\n   * @member {boolean}\n   * @memberof scene.Container#\n   */\n  accessible: false,\n  /**\n   * Sets the title attribute of the shadow div\n   * If accessibleTitle AND accessibleHint has not been this will default to 'container [tabIndex]'\n   * @member {string}\n   * @memberof scene.Container#\n   */\n  accessibleTitle: null,\n  /**\n   * Sets the aria-label attribute of the shadow div\n   * @member {string}\n   * @memberof scene.Container#\n   */\n  accessibleHint: null,\n  /**\n   * @member {number}\n   * @memberof scene.Container#\n   * @todo Needs docs.\n   */\n  tabIndex: 0,\n  /**\n   * @member {boolean}\n   * @memberof scene.Container#\n   * @private\n   */\n  _accessibleActive: false,\n  /**\n   * @memberof scene.Container#\n   * @private\n   */\n  _accessibleDiv: null,\n  /**\n   * Specify the type of div the accessible layer is. Screen readers treat the element differently\n   * depending on this type. Defaults to button.\n   * @member {string}\n   * @memberof scene.Container#\n   * @default 'button'\n   */\n  accessibleType: \"button\",\n  /**\n   * Sets the text content of the shadow div\n   * @member {string}\n   * @memberof scene.Container#\n   */\n  accessibleText: null,\n  /**\n   * Specify the pointer-events the accessible div will use\n   * Defaults to auto.\n   * @type {PointerEvents}\n   * @memberof scene.Container#\n   * @default 'auto'\n   */\n  accessiblePointerEvents: \"auto\",\n  /**\n   * Setting to false will prevent any children inside this container to\n   * be accessible. Defaults to true.\n   * @member {boolean}\n   * @memberof scene.Container#\n   * @default true\n   */\n  accessibleChildren: true,\n  /**\n   * @member {number}\n   * @memberof scene.Container#\n   * @private\n   */\n  _renderId: -1\n};\nclass EventsTickerClass {\n  constructor() {\n    this.interactionFrequency = 10;\n    this._deltaTime = 0;\n    this._didMove = false;\n    this._tickerAdded = false;\n    this._pauseUpdate = true;\n  }\n  /**\n   * Initializes the event ticker.\n   * @param events - The event system.\n   */\n  init(events) {\n    this.removeTickerListener();\n    this.events = events;\n    this.interactionFrequency = 10;\n    this._deltaTime = 0;\n    this._didMove = false;\n    this._tickerAdded = false;\n    this._pauseUpdate = true;\n  }\n  /** Whether to pause the update checks or not. */\n  get pauseUpdate() {\n    return this._pauseUpdate;\n  }\n  set pauseUpdate(paused) {\n    this._pauseUpdate = paused;\n  }\n  /** Adds the ticker listener. */\n  addTickerListener() {\n    if (this._tickerAdded || !this.domElement) {\n      return;\n    }\n    Ticker.system.add(this._tickerUpdate, this, UPDATE_PRIORITY.INTERACTION);\n    this._tickerAdded = true;\n  }\n  /** Removes the ticker listener. */\n  removeTickerListener() {\n    if (!this._tickerAdded) {\n      return;\n    }\n    Ticker.system.remove(this._tickerUpdate, this);\n    this._tickerAdded = false;\n  }\n  /** Sets flag to not fire extra events when the user has already moved there mouse */\n  pointerMoved() {\n    this._didMove = true;\n  }\n  /** Updates the state of interactive objects. */\n  _update() {\n    if (!this.domElement || this._pauseUpdate) {\n      return;\n    }\n    if (this._didMove) {\n      this._didMove = false;\n      return;\n    }\n    const rootPointerEvent = this.events[\"_rootPointerEvent\"];\n    if (this.events.supportsTouchEvents && rootPointerEvent.pointerType === \"touch\") {\n      return;\n    }\n    globalThis.document.dispatchEvent(new PointerEvent(\"pointermove\", {\n      clientX: rootPointerEvent.clientX,\n      clientY: rootPointerEvent.clientY,\n      pointerType: rootPointerEvent.pointerType,\n      pointerId: rootPointerEvent.pointerId\n    }));\n  }\n  /**\n   * Updates the state of interactive objects if at least {@link interactionFrequency}\n   * milliseconds have passed since the last invocation.\n   *\n   * Invoked by a throttled ticker update from {@link Ticker.system}.\n   * @param ticker - The throttled ticker.\n   */\n  _tickerUpdate(ticker) {\n    this._deltaTime += ticker.deltaTime;\n    if (this._deltaTime < this.interactionFrequency) {\n      return;\n    }\n    this._deltaTime = 0;\n    this._update();\n  }\n}\nconst EventsTicker = new EventsTickerClass();\nclass FederatedMouseEvent extends FederatedEvent {\n  constructor() {\n    super(...arguments);\n    this.client = new Point();\n    this.movement = new Point();\n    this.offset = new Point();\n    this.global = new Point();\n    this.screen = new Point();\n  }\n  /** @readonly */\n  get clientX() {\n    return this.client.x;\n  }\n  /** @readonly */\n  get clientY() {\n    return this.client.y;\n  }\n  /**\n   * Alias for {@link FederatedMouseEvent.clientX this.clientX}.\n   * @readonly\n   */\n  get x() {\n    return this.clientX;\n  }\n  /**\n   * Alias for {@link FederatedMouseEvent.clientY this.clientY}.\n   * @readonly\n   */\n  get y() {\n    return this.clientY;\n  }\n  /** @readonly */\n  get movementX() {\n    return this.movement.x;\n  }\n  /** @readonly */\n  get movementY() {\n    return this.movement.y;\n  }\n  /** @readonly */\n  get offsetX() {\n    return this.offset.x;\n  }\n  /** @readonly */\n  get offsetY() {\n    return this.offset.y;\n  }\n  /** @readonly */\n  get globalX() {\n    return this.global.x;\n  }\n  /** @readonly */\n  get globalY() {\n    return this.global.y;\n  }\n  /**\n   * The pointer coordinates in the renderer's screen. Alias for {@code screen.x}.\n   * @readonly\n   */\n  get screenX() {\n    return this.screen.x;\n  }\n  /**\n   * The pointer coordinates in the renderer's screen. Alias for {@code screen.y}.\n   * @readonly\n   */\n  get screenY() {\n    return this.screen.y;\n  }\n  /**\n   * This will return the local coordinates of the specified container for this InteractionData\n   * @param {Container} container - The Container that you would like the local\n   *  coords off\n   * @param {PointData} point - A Point object in which to store the value, optional (otherwise\n   *  will create a new point)\n   * @param {PointData} globalPos - A Point object containing your custom global coords, optional\n   *  (otherwise will use the current global coords)\n   * @returns - A point containing the coordinates of the InteractionData position relative\n   *  to the Container\n   */\n  getLocalPosition(container, point, globalPos) {\n    return container.worldTransform.applyInverse(globalPos || this.global, point);\n  }\n  /**\n   * Whether the modifier key was pressed when this event natively occurred.\n   * @param key - The modifier key.\n   */\n  getModifierState(key) {\n    return \"getModifierState\" in this.nativeEvent && this.nativeEvent.getModifierState(key);\n  }\n  /**\n   * Not supported.\n   * @param _typeArg\n   * @param _canBubbleArg\n   * @param _cancelableArg\n   * @param _viewArg\n   * @param _detailArg\n   * @param _screenXArg\n   * @param _screenYArg\n   * @param _clientXArg\n   * @param _clientYArg\n   * @param _ctrlKeyArg\n   * @param _altKeyArg\n   * @param _shiftKeyArg\n   * @param _metaKeyArg\n   * @param _buttonArg\n   * @param _relatedTargetArg\n   * @deprecated since 7.0.0\n   */\n  // eslint-disable-next-line max-params\n  initMouseEvent(_typeArg, _canBubbleArg, _cancelableArg, _viewArg, _detailArg, _screenXArg, _screenYArg, _clientXArg, _clientYArg, _ctrlKeyArg, _altKeyArg, _shiftKeyArg, _metaKeyArg, _buttonArg, _relatedTargetArg) {\n    throw new Error(\"Method not implemented.\");\n  }\n}\nclass FederatedPointerEvent extends FederatedMouseEvent {\n  constructor() {\n    super(...arguments);\n    this.width = 0;\n    this.height = 0;\n    this.isPrimary = false;\n  }\n  // Only included for completeness for now\n  getCoalescedEvents() {\n    if (this.type === \"pointermove\" || this.type === \"mousemove\" || this.type === \"touchmove\") {\n      return [this];\n    }\n    return [];\n  }\n  // Only included for completeness for now\n  getPredictedEvents() {\n    throw new Error(\"getPredictedEvents is not supported!\");\n  }\n}\nclass FederatedWheelEvent extends FederatedMouseEvent {\n  constructor() {\n    super(...arguments);\n    this.DOM_DELTA_PIXEL = 0;\n    this.DOM_DELTA_LINE = 1;\n    this.DOM_DELTA_PAGE = 2;\n  }\n}\nFederatedWheelEvent.DOM_DELTA_PIXEL = 0;\nFederatedWheelEvent.DOM_DELTA_LINE = 1;\nFederatedWheelEvent.DOM_DELTA_PAGE = 2;\nconst PROPAGATION_LIMIT = 2048;\nconst tempHitLocation = new Point();\nconst tempLocalMapping = new Point();\nclass EventBoundary {\n  /**\n   * @param rootTarget - The holder of the event boundary.\n   */\n  constructor(rootTarget) {\n    this.dispatch = new EventEmitter();\n    this.moveOnAll = false;\n    this.enableGlobalMoveEvents = true;\n    this.mappingState = {\n      trackingData: {}\n    };\n    this.eventPool = /* @__PURE__ */ new Map();\n    this._allInteractiveElements = [];\n    this._hitElements = [];\n    this._isPointerMoveEvent = false;\n    this.rootTarget = rootTarget;\n    this.hitPruneFn = this.hitPruneFn.bind(this);\n    this.hitTestFn = this.hitTestFn.bind(this);\n    this.mapPointerDown = this.mapPointerDown.bind(this);\n    this.mapPointerMove = this.mapPointerMove.bind(this);\n    this.mapPointerOut = this.mapPointerOut.bind(this);\n    this.mapPointerOver = this.mapPointerOver.bind(this);\n    this.mapPointerUp = this.mapPointerUp.bind(this);\n    this.mapPointerUpOutside = this.mapPointerUpOutside.bind(this);\n    this.mapWheel = this.mapWheel.bind(this);\n    this.mappingTable = {};\n    this.addEventMapping(\"pointerdown\", this.mapPointerDown);\n    this.addEventMapping(\"pointermove\", this.mapPointerMove);\n    this.addEventMapping(\"pointerout\", this.mapPointerOut);\n    this.addEventMapping(\"pointerleave\", this.mapPointerOut);\n    this.addEventMapping(\"pointerover\", this.mapPointerOver);\n    this.addEventMapping(\"pointerup\", this.mapPointerUp);\n    this.addEventMapping(\"pointerupoutside\", this.mapPointerUpOutside);\n    this.addEventMapping(\"wheel\", this.mapWheel);\n  }\n  /**\n   * Adds an event mapping for the event `type` handled by `fn`.\n   *\n   * Event mappings can be used to implement additional or custom events. They take an event\n   * coming from the upstream scene (or directly from the {@link EventSystem}) and dispatch new downstream events\n   * generally trickling down and bubbling up to {@link EventBoundary.rootTarget this.rootTarget}.\n   *\n   * To modify the semantics of existing events, the built-in mapping methods of EventBoundary should be overridden\n   * instead.\n   * @param type - The type of upstream event to map.\n   * @param fn - The mapping method. The context of this function must be bound manually, if desired.\n   */\n  addEventMapping(type, fn) {\n    if (!this.mappingTable[type]) {\n      this.mappingTable[type] = [];\n    }\n    this.mappingTable[type].push({\n      fn,\n      priority: 0\n    });\n    this.mappingTable[type].sort((a, b) => a.priority - b.priority);\n  }\n  /**\n   * Dispatches the given event\n   * @param e - The event to dispatch.\n   * @param type - The type of event to dispatch. Defaults to `e.type`.\n   */\n  dispatchEvent(e, type) {\n    e.propagationStopped = false;\n    e.propagationImmediatelyStopped = false;\n    this.propagate(e, type);\n    this.dispatch.emit(type || e.type, e);\n  }\n  /**\n   * Maps the given upstream event through the event boundary and propagates it downstream.\n   * @param e - The event to map.\n   */\n  mapEvent(e) {\n    if (!this.rootTarget) {\n      return;\n    }\n    const mappers = this.mappingTable[e.type];\n    if (mappers) {\n      for (let i = 0, j = mappers.length; i < j; i++) {\n        mappers[i].fn(e);\n      }\n    } else {\n      warn(`[EventBoundary]: Event mapping not defined for ${e.type}`);\n    }\n  }\n  /**\n   * Finds the Container that is the target of a event at the given coordinates.\n   *\n   * The passed (x,y) coordinates are in the world space above this event boundary.\n   * @param x - The x coordinate of the event.\n   * @param y - The y coordinate of the event.\n   */\n  hitTest(x, y) {\n    EventsTicker.pauseUpdate = true;\n    const useMove = this._isPointerMoveEvent && this.enableGlobalMoveEvents;\n    const fn = useMove ? \"hitTestMoveRecursive\" : \"hitTestRecursive\";\n    const invertedPath = this[fn](\n      this.rootTarget,\n      this.rootTarget.eventMode,\n      tempHitLocation.set(x, y),\n      this.hitTestFn,\n      this.hitPruneFn\n    );\n    return invertedPath && invertedPath[0];\n  }\n  /**\n   * Propagate the passed event from from {@link EventBoundary.rootTarget this.rootTarget} to its\n   * target {@code e.target}.\n   * @param e - The event to propagate.\n   * @param type - The type of event to propagate. Defaults to `e.type`.\n   */\n  propagate(e, type) {\n    if (!e.target) {\n      return;\n    }\n    const composedPath = e.composedPath();\n    e.eventPhase = e.CAPTURING_PHASE;\n    for (let i = 0, j = composedPath.length - 1; i < j; i++) {\n      e.currentTarget = composedPath[i];\n      this.notifyTarget(e, type);\n      if (e.propagationStopped || e.propagationImmediatelyStopped)\n        return;\n    }\n    e.eventPhase = e.AT_TARGET;\n    e.currentTarget = e.target;\n    this.notifyTarget(e, type);\n    if (e.propagationStopped || e.propagationImmediatelyStopped)\n      return;\n    e.eventPhase = e.BUBBLING_PHASE;\n    for (let i = composedPath.length - 2; i >= 0; i--) {\n      e.currentTarget = composedPath[i];\n      this.notifyTarget(e, type);\n      if (e.propagationStopped || e.propagationImmediatelyStopped)\n        return;\n    }\n  }\n  /**\n   * Emits the event {@code e} to all interactive containers. The event is propagated in the bubbling phase always.\n   *\n   * This is used in the `globalpointermove` event.\n   * @param e - The emitted event.\n   * @param type - The listeners to notify.\n   * @param targets - The targets to notify.\n   */\n  all(e, type, targets = this._allInteractiveElements) {\n    if (targets.length === 0)\n      return;\n    e.eventPhase = e.BUBBLING_PHASE;\n    const events = Array.isArray(type) ? type : [type];\n    for (let i = targets.length - 1; i >= 0; i--) {\n      events.forEach((event) => {\n        e.currentTarget = targets[i];\n        this.notifyTarget(e, event);\n      });\n    }\n  }\n  /**\n   * Finds the propagation path from {@link EventBoundary.rootTarget rootTarget} to the passed\n   * {@code target}. The last element in the path is {@code target}.\n   * @param target - The target to find the propagation path to.\n   */\n  propagationPath(target) {\n    const propagationPath = [target];\n    for (let i = 0; i < PROPAGATION_LIMIT && (target !== this.rootTarget && target.parent); i++) {\n      if (!target.parent) {\n        throw new Error(\"Cannot find propagation path to disconnected target\");\n      }\n      propagationPath.push(target.parent);\n      target = target.parent;\n    }\n    propagationPath.reverse();\n    return propagationPath;\n  }\n  hitTestMoveRecursive(currentTarget, eventMode, location, testFn, pruneFn, ignore = false) {\n    let shouldReturn = false;\n    if (this._interactivePrune(currentTarget))\n      return null;\n    if (currentTarget.eventMode === \"dynamic\" || eventMode === \"dynamic\") {\n      EventsTicker.pauseUpdate = false;\n    }\n    if (currentTarget.interactiveChildren && currentTarget.children) {\n      const children = currentTarget.children;\n      for (let i = children.length - 1; i >= 0; i--) {\n        const child = children[i];\n        const nestedHit = this.hitTestMoveRecursive(\n          child,\n          this._isInteractive(eventMode) ? eventMode : child.eventMode,\n          location,\n          testFn,\n          pruneFn,\n          ignore || pruneFn(currentTarget, location)\n        );\n        if (nestedHit) {\n          if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent) {\n            continue;\n          }\n          const isInteractive = currentTarget.isInteractive();\n          if (nestedHit.length > 0 || isInteractive) {\n            if (isInteractive)\n              this._allInteractiveElements.push(currentTarget);\n            nestedHit.push(currentTarget);\n          }\n          if (this._hitElements.length === 0)\n            this._hitElements = nestedHit;\n          shouldReturn = true;\n        }\n      }\n    }\n    const isInteractiveMode = this._isInteractive(eventMode);\n    const isInteractiveTarget = currentTarget.isInteractive();\n    if (isInteractiveTarget && isInteractiveTarget)\n      this._allInteractiveElements.push(currentTarget);\n    if (ignore || this._hitElements.length > 0)\n      return null;\n    if (shouldReturn)\n      return this._hitElements;\n    if (isInteractiveMode && (!pruneFn(currentTarget, location) && testFn(currentTarget, location))) {\n      return isInteractiveTarget ? [currentTarget] : [];\n    }\n    return null;\n  }\n  /**\n   * Recursive implementation for {@link EventBoundary.hitTest hitTest}.\n   * @param currentTarget - The Container that is to be hit tested.\n   * @param eventMode - The event mode for the `currentTarget` or one of its parents.\n   * @param location - The location that is being tested for overlap.\n   * @param testFn - Callback that determines whether the target passes hit testing. This callback\n   *  can assume that `pruneFn` failed to prune the container.\n   * @param pruneFn - Callback that determiness whether the target and all of its children\n   *  cannot pass the hit test. It is used as a preliminary optimization to prune entire subtrees\n   *  of the scene graph.\n   * @returns An array holding the hit testing target and all its ancestors in order. The first element\n   *  is the target itself and the last is {@link EventBoundary.rootTarget rootTarget}. This is the opposite\n   *  order w.r.t. the propagation path. If no hit testing target is found, null is returned.\n   */\n  hitTestRecursive(currentTarget, eventMode, location, testFn, pruneFn) {\n    if (this._interactivePrune(currentTarget) || pruneFn(currentTarget, location)) {\n      return null;\n    }\n    if (currentTarget.eventMode === \"dynamic\" || eventMode === \"dynamic\") {\n      EventsTicker.pauseUpdate = false;\n    }\n    if (currentTarget.interactiveChildren && currentTarget.children) {\n      const children = currentTarget.children;\n      const relativeLocation = location;\n      for (let i = children.length - 1; i >= 0; i--) {\n        const child = children[i];\n        const nestedHit = this.hitTestRecursive(\n          child,\n          this._isInteractive(eventMode) ? eventMode : child.eventMode,\n          relativeLocation,\n          testFn,\n          pruneFn\n        );\n        if (nestedHit) {\n          if (nestedHit.length > 0 && !nestedHit[nestedHit.length - 1].parent) {\n            continue;\n          }\n          const isInteractive = currentTarget.isInteractive();\n          if (nestedHit.length > 0 || isInteractive)\n            nestedHit.push(currentTarget);\n          return nestedHit;\n        }\n      }\n    }\n    const isInteractiveMode = this._isInteractive(eventMode);\n    const isInteractiveTarget = currentTarget.isInteractive();\n    if (isInteractiveMode && testFn(currentTarget, location)) {\n      return isInteractiveTarget ? [currentTarget] : [];\n    }\n    return null;\n  }\n  _isInteractive(int) {\n    return int === \"static\" || int === \"dynamic\";\n  }\n  _interactivePrune(container) {\n    if (!container || !container.visible || !container.renderable || !container.measurable) {\n      return true;\n    }\n    if (container.eventMode === \"none\") {\n      return true;\n    }\n    if (container.eventMode === \"passive\" && !container.interactiveChildren) {\n      return true;\n    }\n    return false;\n  }\n  /**\n   * Checks whether the container or any of its children cannot pass the hit test at all.\n   *\n   * {@link EventBoundary}'s implementation uses the {@link Container.hitArea hitArea}\n   * and {@link Container._maskEffect} for pruning.\n   * @param container - The container to prune.\n   * @param location - The location to test for overlap.\n   */\n  hitPruneFn(container, location) {\n    if (container.hitArea) {\n      container.worldTransform.applyInverse(location, tempLocalMapping);\n      if (!container.hitArea.contains(tempLocalMapping.x, tempLocalMapping.y)) {\n        return true;\n      }\n    }\n    if (container.effects && container.effects.length) {\n      for (let i = 0; i < container.effects.length; i++) {\n        const effect = container.effects[i];\n        if (effect.containsPoint) {\n          const effectContainsPoint = effect.containsPoint(location, this.hitTestFn);\n          if (!effectContainsPoint) {\n            return true;\n          }\n        }\n      }\n    }\n    return false;\n  }\n  /**\n   * Checks whether the container passes hit testing for the given location.\n   * @param container - The container to test.\n   * @param location - The location to test for overlap.\n   * @returns - Whether `container` passes hit testing for `location`.\n   */\n  hitTestFn(container, location) {\n    if (container.hitArea) {\n      return true;\n    }\n    if (container?.containsPoint) {\n      container.worldTransform.applyInverse(location, tempLocalMapping);\n      return container.containsPoint(tempLocalMapping);\n    }\n    return false;\n  }\n  /**\n   * Notify all the listeners to the event's `currentTarget`.\n   *\n   * If the `currentTarget` contains the property `on<type>`, then it is called here,\n   * simulating the behavior from version 6.x and prior.\n   * @param e - The event passed to the target.\n   * @param type - The type of event to notify. Defaults to `e.type`.\n   */\n  notifyTarget(e, type) {\n    if (!e.currentTarget.isInteractive()) {\n      return;\n    }\n    type ?? (type = e.type);\n    const handlerKey = `on${type}`;\n    e.currentTarget[handlerKey]?.(e);\n    const key = e.eventPhase === e.CAPTURING_PHASE || e.eventPhase === e.AT_TARGET ? `${type}capture` : type;\n    this._notifyListeners(e, key);\n    if (e.eventPhase === e.AT_TARGET) {\n      this._notifyListeners(e, type);\n    }\n  }\n  /**\n   * Maps the upstream `pointerdown` events to a downstream `pointerdown` event.\n   *\n   * `touchstart`, `rightdown`, `mousedown` events are also dispatched for specific pointer types.\n   * @param from - The upstream `pointerdown` event.\n   */\n  mapPointerDown(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const e = this.createPointerEvent(from);\n    this.dispatchEvent(e, \"pointerdown\");\n    if (e.pointerType === \"touch\") {\n      this.dispatchEvent(e, \"touchstart\");\n    } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n      const isRightButton = e.button === 2;\n      this.dispatchEvent(e, isRightButton ? \"rightdown\" : \"mousedown\");\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    trackingData.pressTargetsByButton[from.button] = e.composedPath();\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointermove` to downstream `pointerout`, `pointerover`, and `pointermove` events, in that order.\n   *\n   * The tracking data for the specific pointer has an updated `overTarget`. `mouseout`, `mouseover`,\n   * `mousemove`, and `touchmove` events are fired as well for specific pointer types.\n   * @param from - The upstream `pointermove` event.\n   */\n  mapPointerMove(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    this._allInteractiveElements.length = 0;\n    this._hitElements.length = 0;\n    this._isPointerMoveEvent = true;\n    const e = this.createPointerEvent(from);\n    this._isPointerMoveEvent = false;\n    const isMouse = e.pointerType === \"mouse\" || e.pointerType === \"pen\";\n    const trackingData = this.trackingData(from.pointerId);\n    const outTarget = this.findMountedTarget(trackingData.overTargets);\n    if (trackingData.overTargets?.length > 0 && outTarget !== e.target) {\n      const outType = from.type === \"mousemove\" ? \"mouseout\" : \"pointerout\";\n      const outEvent = this.createPointerEvent(from, outType, outTarget);\n      this.dispatchEvent(outEvent, \"pointerout\");\n      if (isMouse)\n        this.dispatchEvent(outEvent, \"mouseout\");\n      if (!e.composedPath().includes(outTarget)) {\n        const leaveEvent = this.createPointerEvent(from, \"pointerleave\", outTarget);\n        leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n        while (leaveEvent.target && !e.composedPath().includes(leaveEvent.target)) {\n          leaveEvent.currentTarget = leaveEvent.target;\n          this.notifyTarget(leaveEvent);\n          if (isMouse)\n            this.notifyTarget(leaveEvent, \"mouseleave\");\n          leaveEvent.target = leaveEvent.target.parent;\n        }\n        this.freeEvent(leaveEvent);\n      }\n      this.freeEvent(outEvent);\n    }\n    if (outTarget !== e.target) {\n      const overType = from.type === \"mousemove\" ? \"mouseover\" : \"pointerover\";\n      const overEvent = this.clonePointerEvent(e, overType);\n      this.dispatchEvent(overEvent, \"pointerover\");\n      if (isMouse)\n        this.dispatchEvent(overEvent, \"mouseover\");\n      let overTargetAncestor = outTarget?.parent;\n      while (overTargetAncestor && overTargetAncestor !== this.rootTarget.parent) {\n        if (overTargetAncestor === e.target)\n          break;\n        overTargetAncestor = overTargetAncestor.parent;\n      }\n      const didPointerEnter = !overTargetAncestor || overTargetAncestor === this.rootTarget.parent;\n      if (didPointerEnter) {\n        const enterEvent = this.clonePointerEvent(e, \"pointerenter\");\n        enterEvent.eventPhase = enterEvent.AT_TARGET;\n        while (enterEvent.target && enterEvent.target !== outTarget && enterEvent.target !== this.rootTarget.parent) {\n          enterEvent.currentTarget = enterEvent.target;\n          this.notifyTarget(enterEvent);\n          if (isMouse)\n            this.notifyTarget(enterEvent, \"mouseenter\");\n          enterEvent.target = enterEvent.target.parent;\n        }\n        this.freeEvent(enterEvent);\n      }\n      this.freeEvent(overEvent);\n    }\n    const allMethods = [];\n    const allowGlobalPointerEvents = this.enableGlobalMoveEvents ?? true;\n    this.moveOnAll ? allMethods.push(\"pointermove\") : this.dispatchEvent(e, \"pointermove\");\n    allowGlobalPointerEvents && allMethods.push(\"globalpointermove\");\n    if (e.pointerType === \"touch\") {\n      this.moveOnAll ? allMethods.splice(1, 0, \"touchmove\") : this.dispatchEvent(e, \"touchmove\");\n      allowGlobalPointerEvents && allMethods.push(\"globaltouchmove\");\n    }\n    if (isMouse) {\n      this.moveOnAll ? allMethods.splice(1, 0, \"mousemove\") : this.dispatchEvent(e, \"mousemove\");\n      allowGlobalPointerEvents && allMethods.push(\"globalmousemove\");\n      this.cursor = e.target?.cursor;\n    }\n    if (allMethods.length > 0) {\n      this.all(e, allMethods);\n    }\n    this._allInteractiveElements.length = 0;\n    this._hitElements.length = 0;\n    trackingData.overTargets = e.composedPath();\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointerover` to downstream `pointerover` and `pointerenter` events, in that order.\n   *\n   * The tracking data for the specific pointer gets a new `overTarget`.\n   * @param from - The upstream `pointerover` event.\n   */\n  mapPointerOver(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    const e = this.createPointerEvent(from);\n    const isMouse = e.pointerType === \"mouse\" || e.pointerType === \"pen\";\n    this.dispatchEvent(e, \"pointerover\");\n    if (isMouse)\n      this.dispatchEvent(e, \"mouseover\");\n    if (e.pointerType === \"mouse\")\n      this.cursor = e.target?.cursor;\n    const enterEvent = this.clonePointerEvent(e, \"pointerenter\");\n    enterEvent.eventPhase = enterEvent.AT_TARGET;\n    while (enterEvent.target && enterEvent.target !== this.rootTarget.parent) {\n      enterEvent.currentTarget = enterEvent.target;\n      this.notifyTarget(enterEvent);\n      if (isMouse)\n        this.notifyTarget(enterEvent, \"mouseenter\");\n      enterEvent.target = enterEvent.target.parent;\n    }\n    trackingData.overTargets = e.composedPath();\n    this.freeEvent(e);\n    this.freeEvent(enterEvent);\n  }\n  /**\n   * Maps the upstream `pointerout` to downstream `pointerout`, `pointerleave` events, in that order.\n   *\n   * The tracking data for the specific pointer is cleared of a `overTarget`.\n   * @param from - The upstream `pointerout` event.\n   */\n  mapPointerOut(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    if (trackingData.overTargets) {\n      const isMouse = from.pointerType === \"mouse\" || from.pointerType === \"pen\";\n      const outTarget = this.findMountedTarget(trackingData.overTargets);\n      const outEvent = this.createPointerEvent(from, \"pointerout\", outTarget);\n      this.dispatchEvent(outEvent);\n      if (isMouse)\n        this.dispatchEvent(outEvent, \"mouseout\");\n      const leaveEvent = this.createPointerEvent(from, \"pointerleave\", outTarget);\n      leaveEvent.eventPhase = leaveEvent.AT_TARGET;\n      while (leaveEvent.target && leaveEvent.target !== this.rootTarget.parent) {\n        leaveEvent.currentTarget = leaveEvent.target;\n        this.notifyTarget(leaveEvent);\n        if (isMouse)\n          this.notifyTarget(leaveEvent, \"mouseleave\");\n        leaveEvent.target = leaveEvent.target.parent;\n      }\n      trackingData.overTargets = null;\n      this.freeEvent(outEvent);\n      this.freeEvent(leaveEvent);\n    }\n    this.cursor = null;\n  }\n  /**\n   * Maps the upstream `pointerup` event to downstream `pointerup`, `pointerupoutside`,\n   * and `click`/`rightclick`/`pointertap` events, in that order.\n   *\n   * The `pointerupoutside` event bubbles from the original `pointerdown` target to the most specific\n   * ancestor of the `pointerdown` and `pointerup` targets, which is also the `click` event's target. `touchend`,\n   * `rightup`, `mouseup`, `touchendoutside`, `rightupoutside`, `mouseupoutside`, and `tap` are fired as well for\n   * specific pointer types.\n   * @param from - The upstream `pointerup` event.\n   */\n  mapPointerUp(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const now = performance.now();\n    const e = this.createPointerEvent(from);\n    this.dispatchEvent(e, \"pointerup\");\n    if (e.pointerType === \"touch\") {\n      this.dispatchEvent(e, \"touchend\");\n    } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n      const isRightButton = e.button === 2;\n      this.dispatchEvent(e, isRightButton ? \"rightup\" : \"mouseup\");\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n    let clickTarget = pressTarget;\n    if (pressTarget && !e.composedPath().includes(pressTarget)) {\n      let currentTarget = pressTarget;\n      while (currentTarget && !e.composedPath().includes(currentTarget)) {\n        e.currentTarget = currentTarget;\n        this.notifyTarget(e, \"pointerupoutside\");\n        if (e.pointerType === \"touch\") {\n          this.notifyTarget(e, \"touchendoutside\");\n        } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n          const isRightButton = e.button === 2;\n          this.notifyTarget(e, isRightButton ? \"rightupoutside\" : \"mouseupoutside\");\n        }\n        currentTarget = currentTarget.parent;\n      }\n      delete trackingData.pressTargetsByButton[from.button];\n      clickTarget = currentTarget;\n    }\n    if (clickTarget) {\n      const clickEvent = this.clonePointerEvent(e, \"click\");\n      clickEvent.target = clickTarget;\n      clickEvent.path = null;\n      if (!trackingData.clicksByButton[from.button]) {\n        trackingData.clicksByButton[from.button] = {\n          clickCount: 0,\n          target: clickEvent.target,\n          timeStamp: now\n        };\n      }\n      const clickHistory = trackingData.clicksByButton[from.button];\n      if (clickHistory.target === clickEvent.target && now - clickHistory.timeStamp < 200) {\n        ++clickHistory.clickCount;\n      } else {\n        clickHistory.clickCount = 1;\n      }\n      clickHistory.target = clickEvent.target;\n      clickHistory.timeStamp = now;\n      clickEvent.detail = clickHistory.clickCount;\n      if (clickEvent.pointerType === \"mouse\") {\n        const isRightButton = clickEvent.button === 2;\n        this.dispatchEvent(clickEvent, isRightButton ? \"rightclick\" : \"click\");\n      } else if (clickEvent.pointerType === \"touch\") {\n        this.dispatchEvent(clickEvent, \"tap\");\n      }\n      this.dispatchEvent(clickEvent, \"pointertap\");\n      this.freeEvent(clickEvent);\n    }\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `pointerupoutside` event to a downstream `pointerupoutside` event, bubbling from the original\n   * `pointerdown` target to `rootTarget`.\n   *\n   * (The most specific ancestor of the `pointerdown` event and the `pointerup` event must the\n   * `{@link EventBoundary}'s root because the `pointerup` event occurred outside of the boundary.)\n   *\n   * `touchendoutside`, `mouseupoutside`, and `rightupoutside` events are fired as well for specific pointer\n   * types. The tracking data for the specific pointer is cleared of a `pressTarget`.\n   * @param from - The upstream `pointerupoutside` event.\n   */\n  mapPointerUpOutside(from) {\n    if (!(from instanceof FederatedPointerEvent)) {\n      warn(\"EventBoundary cannot map a non-pointer event as a pointer event\");\n      return;\n    }\n    const trackingData = this.trackingData(from.pointerId);\n    const pressTarget = this.findMountedTarget(trackingData.pressTargetsByButton[from.button]);\n    const e = this.createPointerEvent(from);\n    if (pressTarget) {\n      let currentTarget = pressTarget;\n      while (currentTarget) {\n        e.currentTarget = currentTarget;\n        this.notifyTarget(e, \"pointerupoutside\");\n        if (e.pointerType === \"touch\") {\n          this.notifyTarget(e, \"touchendoutside\");\n        } else if (e.pointerType === \"mouse\" || e.pointerType === \"pen\") {\n          this.notifyTarget(e, e.button === 2 ? \"rightupoutside\" : \"mouseupoutside\");\n        }\n        currentTarget = currentTarget.parent;\n      }\n      delete trackingData.pressTargetsByButton[from.button];\n    }\n    this.freeEvent(e);\n  }\n  /**\n   * Maps the upstream `wheel` event to a downstream `wheel` event.\n   * @param from - The upstream `wheel` event.\n   */\n  mapWheel(from) {\n    if (!(from instanceof FederatedWheelEvent)) {\n      warn(\"EventBoundary cannot map a non-wheel event as a wheel event\");\n      return;\n    }\n    const wheelEvent = this.createWheelEvent(from);\n    this.dispatchEvent(wheelEvent);\n    this.freeEvent(wheelEvent);\n  }\n  /**\n   * Finds the most specific event-target in the given propagation path that is still mounted in the scene graph.\n   *\n   * This is used to find the correct `pointerup` and `pointerout` target in the case that the original `pointerdown`\n   * or `pointerover` target was unmounted from the scene graph.\n   * @param propagationPath - The propagation path was valid in the past.\n   * @returns - The most specific event-target still mounted at the same location in the scene graph.\n   */\n  findMountedTarget(propagationPath) {\n    if (!propagationPath) {\n      return null;\n    }\n    let currentTarget = propagationPath[0];\n    for (let i = 1; i < propagationPath.length; i++) {\n      if (propagationPath[i].parent === currentTarget) {\n        currentTarget = propagationPath[i];\n      } else {\n        break;\n      }\n    }\n    return currentTarget;\n  }\n  /**\n   * Creates an event whose {@code originalEvent} is {@code from}, with an optional `type` and `target` override.\n   *\n   * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The {@code originalEvent} for the returned event.\n   * @param [type=from.type] - The type of the returned event.\n   * @param target - The target of the returned event.\n   */\n  createPointerEvent(from, type, target) {\n    const event = this.allocateEvent(FederatedPointerEvent);\n    this.copyPointerData(from, event);\n    this.copyMouseData(from, event);\n    this.copyData(from, event);\n    event.nativeEvent = from.nativeEvent;\n    event.originalEvent = from;\n    event.target = target ?? this.hitTest(event.global.x, event.global.y) ?? this._hitElements[0];\n    if (typeof type === \"string\") {\n      event.type = type;\n    }\n    return event;\n  }\n  /**\n   * Creates a wheel event whose {@code originalEvent} is {@code from}.\n   *\n   * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The upstream wheel event.\n   */\n  createWheelEvent(from) {\n    const event = this.allocateEvent(FederatedWheelEvent);\n    this.copyWheelData(from, event);\n    this.copyMouseData(from, event);\n    this.copyData(from, event);\n    event.nativeEvent = from.nativeEvent;\n    event.originalEvent = from;\n    event.target = this.hitTest(event.global.x, event.global.y);\n    return event;\n  }\n  /**\n   * Clones the event {@code from}, with an optional {@code type} override.\n   *\n   * The event is allocated using {@link EventBoundary#allocateEvent this.allocateEvent}.\n   * @param from - The event to clone.\n   * @param [type=from.type] - The type of the returned event.\n   */\n  clonePointerEvent(from, type) {\n    const event = this.allocateEvent(FederatedPointerEvent);\n    event.nativeEvent = from.nativeEvent;\n    event.originalEvent = from.originalEvent;\n    this.copyPointerData(from, event);\n    this.copyMouseData(from, event);\n    this.copyData(from, event);\n    event.target = from.target;\n    event.path = from.composedPath().slice();\n    event.type = type ?? event.type;\n    return event;\n  }\n  /**\n   * Copies wheel {@link FederatedWheelEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + deltaMode\n   * + deltaX\n   * + deltaY\n   * + deltaZ\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyWheelData(from, to) {\n    to.deltaMode = from.deltaMode;\n    to.deltaX = from.deltaX;\n    to.deltaY = from.deltaY;\n    to.deltaZ = from.deltaZ;\n  }\n  /**\n   * Copies pointer {@link FederatedPointerEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + pointerId\n   * + width\n   * + height\n   * + isPrimary\n   * + pointerType\n   * + pressure\n   * + tangentialPressure\n   * + tiltX\n   * + tiltY\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyPointerData(from, to) {\n    if (!(from instanceof FederatedPointerEvent && to instanceof FederatedPointerEvent))\n      return;\n    to.pointerId = from.pointerId;\n    to.width = from.width;\n    to.height = from.height;\n    to.isPrimary = from.isPrimary;\n    to.pointerType = from.pointerType;\n    to.pressure = from.pressure;\n    to.tangentialPressure = from.tangentialPressure;\n    to.tiltX = from.tiltX;\n    to.tiltY = from.tiltY;\n    to.twist = from.twist;\n  }\n  /**\n   * Copies mouse {@link FederatedMouseEvent} data from {@code from} to {@code to}.\n   *\n   * The following properties are copied:\n   * + altKey\n   * + button\n   * + buttons\n   * + clientX\n   * + clientY\n   * + metaKey\n   * + movementX\n   * + movementY\n   * + pageX\n   * + pageY\n   * + x\n   * + y\n   * + screen\n   * + shiftKey\n   * + global\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyMouseData(from, to) {\n    if (!(from instanceof FederatedMouseEvent && to instanceof FederatedMouseEvent))\n      return;\n    to.altKey = from.altKey;\n    to.button = from.button;\n    to.buttons = from.buttons;\n    to.client.copyFrom(from.client);\n    to.ctrlKey = from.ctrlKey;\n    to.metaKey = from.metaKey;\n    to.movement.copyFrom(from.movement);\n    to.screen.copyFrom(from.screen);\n    to.shiftKey = from.shiftKey;\n    to.global.copyFrom(from.global);\n  }\n  /**\n   * Copies base {@link FederatedEvent} data from {@code from} into {@code to}.\n   *\n   * The following properties are copied:\n   * + isTrusted\n   * + srcElement\n   * + timeStamp\n   * + type\n   * @param from - The event to copy data from.\n   * @param to - The event to copy data into.\n   */\n  copyData(from, to) {\n    to.isTrusted = from.isTrusted;\n    to.srcElement = from.srcElement;\n    to.timeStamp = performance.now();\n    to.type = from.type;\n    to.detail = from.detail;\n    to.view = from.view;\n    to.which = from.which;\n    to.layer.copyFrom(from.layer);\n    to.page.copyFrom(from.page);\n  }\n  /**\n   * @param id - The pointer ID.\n   * @returns The tracking data stored for the given pointer. If no data exists, a blank\n   *  state will be created.\n   */\n  trackingData(id) {\n    if (!this.mappingState.trackingData[id]) {\n      this.mappingState.trackingData[id] = {\n        pressTargetsByButton: {},\n        clicksByButton: {},\n        overTarget: null\n      };\n    }\n    return this.mappingState.trackingData[id];\n  }\n  /**\n   * Allocate a specific type of event from {@link EventBoundary#eventPool this.eventPool}.\n   *\n   * This allocation is constructor-agnostic, as long as it only takes one argument - this event\n   * boundary.\n   * @param constructor - The event's constructor.\n   */\n  allocateEvent(constructor) {\n    if (!this.eventPool.has(constructor)) {\n      this.eventPool.set(constructor, []);\n    }\n    const event = this.eventPool.get(constructor).pop() || new constructor(this);\n    event.eventPhase = event.NONE;\n    event.currentTarget = null;\n    event.defaultPrevented = false;\n    event.path = null;\n    event.target = null;\n    return event;\n  }\n  /**\n   * Frees the event and puts it back into the event pool.\n   *\n   * It is illegal to reuse the event until it is allocated again, using `this.allocateEvent`.\n   *\n   * It is also advised that events not allocated from {@link EventBoundary#allocateEvent this.allocateEvent}\n   * not be freed. This is because of the possibility that the same event is freed twice, which can cause\n   * it to be allocated twice & result in overwriting.\n   * @param event - The event to be freed.\n   * @throws Error if the event is managed by another event boundary.\n   */\n  freeEvent(event) {\n    if (event.manager !== this)\n      throw new Error(\"It is illegal to free an event not managed by this EventBoundary!\");\n    const constructor = event.constructor;\n    if (!this.eventPool.has(constructor)) {\n      this.eventPool.set(constructor, []);\n    }\n    this.eventPool.get(constructor).push(event);\n  }\n  /**\n   * Similar to {@link EventEmitter.emit}, except it stops if the `propagationImmediatelyStopped` flag\n   * is set on the event.\n   * @param e - The event to call each listener with.\n   * @param type - The event key.\n   */\n  _notifyListeners(e, type) {\n    const listeners = e.currentTarget._events[type];\n    if (!listeners)\n      return;\n    if (\"fn\" in listeners) {\n      if (listeners.once)\n        e.currentTarget.removeListener(type, listeners.fn, void 0, true);\n      listeners.fn.call(listeners.context, e);\n    } else {\n      for (let i = 0, j = listeners.length; i < j && !e.propagationImmediatelyStopped; i++) {\n        if (listeners[i].once)\n          e.currentTarget.removeListener(type, listeners[i].fn, void 0, true);\n        listeners[i].fn.call(listeners[i].context, e);\n      }\n    }\n  }\n}\nconst MOUSE_POINTER_ID = 1;\nconst TOUCH_TO_POINTER = {\n  touchstart: \"pointerdown\",\n  touchend: \"pointerup\",\n  touchendoutside: \"pointerupoutside\",\n  touchmove: \"pointermove\",\n  touchcancel: \"pointercancel\"\n};\nconst _EventSystem = class _EventSystem2 {\n  /**\n   * @param {Renderer} renderer\n   */\n  constructor(renderer) {\n    this.supportsTouchEvents = \"ontouchstart\" in globalThis;\n    this.supportsPointerEvents = !!globalThis.PointerEvent;\n    this.domElement = null;\n    this.resolution = 1;\n    this.renderer = renderer;\n    this.rootBoundary = new EventBoundary(null);\n    EventsTicker.init(this);\n    this.autoPreventDefault = true;\n    this._eventsAdded = false;\n    this._rootPointerEvent = new FederatedPointerEvent(null);\n    this._rootWheelEvent = new FederatedWheelEvent(null);\n    this.cursorStyles = {\n      default: \"inherit\",\n      pointer: \"pointer\"\n    };\n    this.features = new Proxy({ ..._EventSystem2.defaultEventFeatures }, {\n      set: (target, key, value) => {\n        if (key === \"globalMove\") {\n          this.rootBoundary.enableGlobalMoveEvents = value;\n        }\n        target[key] = value;\n        return true;\n      }\n    });\n    this._onPointerDown = this._onPointerDown.bind(this);\n    this._onPointerMove = this._onPointerMove.bind(this);\n    this._onPointerUp = this._onPointerUp.bind(this);\n    this._onPointerOverOut = this._onPointerOverOut.bind(this);\n    this.onWheel = this.onWheel.bind(this);\n  }\n  /**\n   * The default interaction mode for all display objects.\n   * @see Container.eventMode\n   * @type {EventMode}\n   * @readonly\n   * @since 7.2.0\n   */\n  static get defaultEventMode() {\n    return this._defaultEventMode;\n  }\n  /**\n   * Runner init called, view is available at this point.\n   * @ignore\n   */\n  init(options) {\n    const { canvas, resolution } = this.renderer;\n    this.setTargetElement(canvas);\n    this.resolution = resolution;\n    _EventSystem2._defaultEventMode = options.eventMode ?? \"passive\";\n    Object.assign(this.features, options.eventFeatures ?? {});\n    this.rootBoundary.enableGlobalMoveEvents = this.features.globalMove;\n  }\n  /**\n   * Handle changing resolution.\n   * @ignore\n   */\n  resolutionChange(resolution) {\n    this.resolution = resolution;\n  }\n  /** Destroys all event listeners and detaches the renderer. */\n  destroy() {\n    this.setTargetElement(null);\n    this.renderer = null;\n    this._currentCursor = null;\n  }\n  /**\n   * Sets the current cursor mode, handling any callbacks or CSS style changes.\n   * @param mode - cursor mode, a key from the cursorStyles dictionary\n   */\n  setCursor(mode) {\n    mode || (mode = \"default\");\n    let applyStyles = true;\n    if (globalThis.OffscreenCanvas && this.domElement instanceof OffscreenCanvas) {\n      applyStyles = false;\n    }\n    if (this._currentCursor === mode) {\n      return;\n    }\n    this._currentCursor = mode;\n    const style = this.cursorStyles[mode];\n    if (style) {\n      switch (typeof style) {\n        case \"string\":\n          if (applyStyles) {\n            this.domElement.style.cursor = style;\n          }\n          break;\n        case \"function\":\n          style(mode);\n          break;\n        case \"object\":\n          if (applyStyles) {\n            Object.assign(this.domElement.style, style);\n          }\n          break;\n      }\n    } else if (applyStyles && typeof mode === \"string\" && !Object.prototype.hasOwnProperty.call(this.cursorStyles, mode)) {\n      this.domElement.style.cursor = mode;\n    }\n  }\n  /**\n   * The global pointer event.\n   * Useful for getting the pointer position without listening to events.\n   * @since 7.2.0\n   */\n  get pointer() {\n    return this._rootPointerEvent;\n  }\n  /**\n   * Event handler for pointer down events on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch event.\n   */\n  _onPointerDown(nativeEvent) {\n    if (!this.features.click)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    const events = this._normalizeToPointerData(nativeEvent);\n    if (this.autoPreventDefault && events[0].isNormalized) {\n      const cancelable = nativeEvent.cancelable || !(\"cancelable\" in nativeEvent);\n      if (cancelable) {\n        nativeEvent.preventDefault();\n      }\n    }\n    for (let i = 0, j = events.length; i < j; i++) {\n      const nativeEvent2 = events[i];\n      const federatedEvent = this._bootstrapEvent(this._rootPointerEvent, nativeEvent2);\n      this.rootBoundary.mapEvent(federatedEvent);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Event handler for pointer move events on on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch events.\n   */\n  _onPointerMove(nativeEvent) {\n    if (!this.features.move)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    EventsTicker.pointerMoved();\n    const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n    for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n      const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n      this.rootBoundary.mapEvent(event);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Event handler for pointer up events on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch event.\n   */\n  _onPointerUp(nativeEvent) {\n    if (!this.features.click)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    let target = nativeEvent.target;\n    if (nativeEvent.composedPath && nativeEvent.composedPath().length > 0) {\n      target = nativeEvent.composedPath()[0];\n    }\n    const outside = target !== this.domElement ? \"outside\" : \"\";\n    const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n    for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n      const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n      event.type += outside;\n      this.rootBoundary.mapEvent(event);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Event handler for pointer over & out events on {@link EventSystem#domElement this.domElement}.\n   * @param nativeEvent - The native mouse/pointer/touch event.\n   */\n  _onPointerOverOut(nativeEvent) {\n    if (!this.features.click)\n      return;\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    const normalizedEvents = this._normalizeToPointerData(nativeEvent);\n    for (let i = 0, j = normalizedEvents.length; i < j; i++) {\n      const event = this._bootstrapEvent(this._rootPointerEvent, normalizedEvents[i]);\n      this.rootBoundary.mapEvent(event);\n    }\n    this.setCursor(this.rootBoundary.cursor);\n  }\n  /**\n   * Passive handler for `wheel` events on {@link EventSystem.domElement this.domElement}.\n   * @param nativeEvent - The native wheel event.\n   */\n  onWheel(nativeEvent) {\n    if (!this.features.wheel)\n      return;\n    const wheelEvent = this.normalizeWheelEvent(nativeEvent);\n    this.rootBoundary.rootTarget = this.renderer.lastObjectRendered;\n    this.rootBoundary.mapEvent(wheelEvent);\n  }\n  /**\n   * Sets the {@link EventSystem#domElement domElement} and binds event listeners.\n   *\n   * To deregister the current DOM element without setting a new one, pass {@code null}.\n   * @param element - The new DOM element.\n   */\n  setTargetElement(element) {\n    this._removeEvents();\n    this.domElement = element;\n    EventsTicker.domElement = element;\n    this._addEvents();\n  }\n  /** Register event listeners on {@link Renderer#domElement this.domElement}. */\n  _addEvents() {\n    if (this._eventsAdded || !this.domElement) {\n      return;\n    }\n    EventsTicker.addTickerListener();\n    const style = this.domElement.style;\n    if (style) {\n      if (globalThis.navigator.msPointerEnabled) {\n        style.msContentZooming = \"none\";\n        style.msTouchAction = \"none\";\n      } else if (this.supportsPointerEvents) {\n        style.touchAction = \"none\";\n      }\n    }\n    if (this.supportsPointerEvents) {\n      globalThis.document.addEventListener(\"pointermove\", this._onPointerMove, true);\n      this.domElement.addEventListener(\"pointerdown\", this._onPointerDown, true);\n      this.domElement.addEventListener(\"pointerleave\", this._onPointerOverOut, true);\n      this.domElement.addEventListener(\"pointerover\", this._onPointerOverOut, true);\n      globalThis.addEventListener(\"pointerup\", this._onPointerUp, true);\n    } else {\n      globalThis.document.addEventListener(\"mousemove\", this._onPointerMove, true);\n      this.domElement.addEventListener(\"mousedown\", this._onPointerDown, true);\n      this.domElement.addEventListener(\"mouseout\", this._onPointerOverOut, true);\n      this.domElement.addEventListener(\"mouseover\", this._onPointerOverOut, true);\n      globalThis.addEventListener(\"mouseup\", this._onPointerUp, true);\n      if (this.supportsTouchEvents) {\n        this.domElement.addEventListener(\"touchstart\", this._onPointerDown, true);\n        this.domElement.addEventListener(\"touchend\", this._onPointerUp, true);\n        this.domElement.addEventListener(\"touchmove\", this._onPointerMove, true);\n      }\n    }\n    this.domElement.addEventListener(\"wheel\", this.onWheel, {\n      passive: true,\n      capture: true\n    });\n    this._eventsAdded = true;\n  }\n  /** Unregister event listeners on {@link EventSystem#domElement this.domElement}. */\n  _removeEvents() {\n    if (!this._eventsAdded || !this.domElement) {\n      return;\n    }\n    EventsTicker.removeTickerListener();\n    const style = this.domElement.style;\n    if (style) {\n      if (globalThis.navigator.msPointerEnabled) {\n        style.msContentZooming = \"\";\n        style.msTouchAction = \"\";\n      } else if (this.supportsPointerEvents) {\n        style.touchAction = \"\";\n      }\n    }\n    if (this.supportsPointerEvents) {\n      globalThis.document.removeEventListener(\"pointermove\", this._onPointerMove, true);\n      this.domElement.removeEventListener(\"pointerdown\", this._onPointerDown, true);\n      this.domElement.removeEventListener(\"pointerleave\", this._onPointerOverOut, true);\n      this.domElement.removeEventListener(\"pointerover\", this._onPointerOverOut, true);\n      globalThis.removeEventListener(\"pointerup\", this._onPointerUp, true);\n    } else {\n      globalThis.document.removeEventListener(\"mousemove\", this._onPointerMove, true);\n      this.domElement.removeEventListener(\"mousedown\", this._onPointerDown, true);\n      this.domElement.removeEventListener(\"mouseout\", this._onPointerOverOut, true);\n      this.domElement.removeEventListener(\"mouseover\", this._onPointerOverOut, true);\n      globalThis.removeEventListener(\"mouseup\", this._onPointerUp, true);\n      if (this.supportsTouchEvents) {\n        this.domElement.removeEventListener(\"touchstart\", this._onPointerDown, true);\n        this.domElement.removeEventListener(\"touchend\", this._onPointerUp, true);\n        this.domElement.removeEventListener(\"touchmove\", this._onPointerMove, true);\n      }\n    }\n    this.domElement.removeEventListener(\"wheel\", this.onWheel, true);\n    this.domElement = null;\n    this._eventsAdded = false;\n  }\n  /**\n   * Maps x and y coords from a DOM object and maps them correctly to the PixiJS view. The\n   * resulting value is stored in the point. This takes into account the fact that the DOM\n   * element could be scaled and positioned anywhere on the screen.\n   * @param  {PointData} point - the point that the result will be stored in\n   * @param  {number} x - the x coord of the position to map\n   * @param  {number} y - the y coord of the position to map\n   */\n  mapPositionToPoint(point, x, y) {\n    const rect = this.domElement.isConnected ? this.domElement.getBoundingClientRect() : {\n      x: 0,\n      y: 0,\n      width: this.domElement.width,\n      height: this.domElement.height,\n      left: 0,\n      top: 0\n    };\n    const resolutionMultiplier = 1 / this.resolution;\n    point.x = (x - rect.left) * (this.domElement.width / rect.width) * resolutionMultiplier;\n    point.y = (y - rect.top) * (this.domElement.height / rect.height) * resolutionMultiplier;\n  }\n  /**\n   * Ensures that the original event object contains all data that a regular pointer event would have\n   * @param event - The original event data from a touch or mouse event\n   * @returns An array containing a single normalized pointer event, in the case of a pointer\n   *  or mouse event, or a multiple normalized pointer events if there are multiple changed touches\n   */\n  _normalizeToPointerData(event) {\n    const normalizedEvents = [];\n    if (this.supportsTouchEvents && event instanceof TouchEvent) {\n      for (let i = 0, li = event.changedTouches.length; i < li; i++) {\n        const touch = event.changedTouches[i];\n        if (typeof touch.button === \"undefined\")\n          touch.button = 0;\n        if (typeof touch.buttons === \"undefined\")\n          touch.buttons = 1;\n        if (typeof touch.isPrimary === \"undefined\") {\n          touch.isPrimary = event.touches.length === 1 && event.type === \"touchstart\";\n        }\n        if (typeof touch.width === \"undefined\")\n          touch.width = touch.radiusX || 1;\n        if (typeof touch.height === \"undefined\")\n          touch.height = touch.radiusY || 1;\n        if (typeof touch.tiltX === \"undefined\")\n          touch.tiltX = 0;\n        if (typeof touch.tiltY === \"undefined\")\n          touch.tiltY = 0;\n        if (typeof touch.pointerType === \"undefined\")\n          touch.pointerType = \"touch\";\n        if (typeof touch.pointerId === \"undefined\")\n          touch.pointerId = touch.identifier || 0;\n        if (typeof touch.pressure === \"undefined\")\n          touch.pressure = touch.force || 0.5;\n        if (typeof touch.twist === \"undefined\")\n          touch.twist = 0;\n        if (typeof touch.tangentialPressure === \"undefined\")\n          touch.tangentialPressure = 0;\n        if (typeof touch.layerX === \"undefined\")\n          touch.layerX = touch.offsetX = touch.clientX;\n        if (typeof touch.layerY === \"undefined\")\n          touch.layerY = touch.offsetY = touch.clientY;\n        touch.isNormalized = true;\n        touch.type = event.type;\n        normalizedEvents.push(touch);\n      }\n    } else if (!globalThis.MouseEvent || event instanceof MouseEvent && (!this.supportsPointerEvents || !(event instanceof globalThis.PointerEvent))) {\n      const tempEvent = event;\n      if (typeof tempEvent.isPrimary === \"undefined\")\n        tempEvent.isPrimary = true;\n      if (typeof tempEvent.width === \"undefined\")\n        tempEvent.width = 1;\n      if (typeof tempEvent.height === \"undefined\")\n        tempEvent.height = 1;\n      if (typeof tempEvent.tiltX === \"undefined\")\n        tempEvent.tiltX = 0;\n      if (typeof tempEvent.tiltY === \"undefined\")\n        tempEvent.tiltY = 0;\n      if (typeof tempEvent.pointerType === \"undefined\")\n        tempEvent.pointerType = \"mouse\";\n      if (typeof tempEvent.pointerId === \"undefined\")\n        tempEvent.pointerId = MOUSE_POINTER_ID;\n      if (typeof tempEvent.pressure === \"undefined\")\n        tempEvent.pressure = 0.5;\n      if (typeof tempEvent.twist === \"undefined\")\n        tempEvent.twist = 0;\n      if (typeof tempEvent.tangentialPressure === \"undefined\")\n        tempEvent.tangentialPressure = 0;\n      tempEvent.isNormalized = true;\n      normalizedEvents.push(tempEvent);\n    } else {\n      normalizedEvents.push(event);\n    }\n    return normalizedEvents;\n  }\n  /**\n   * Normalizes the native {@link https://w3c.github.io/uievents/#interface-wheelevent WheelEvent}.\n   *\n   * The returned {@link FederatedWheelEvent} is a shared instance. It will not persist across\n   * multiple native wheel events.\n   * @param nativeEvent - The native wheel event that occurred on the canvas.\n   * @returns A federated wheel event.\n   */\n  normalizeWheelEvent(nativeEvent) {\n    const event = this._rootWheelEvent;\n    this._transferMouseData(event, nativeEvent);\n    event.deltaX = nativeEvent.deltaX;\n    event.deltaY = nativeEvent.deltaY;\n    event.deltaZ = nativeEvent.deltaZ;\n    event.deltaMode = nativeEvent.deltaMode;\n    this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n    event.global.copyFrom(event.screen);\n    event.offset.copyFrom(event.screen);\n    event.nativeEvent = nativeEvent;\n    event.type = nativeEvent.type;\n    return event;\n  }\n  /**\n   * Normalizes the `nativeEvent` into a federateed {@link FederatedPointerEvent}.\n   * @param event\n   * @param nativeEvent\n   */\n  _bootstrapEvent(event, nativeEvent) {\n    event.originalEvent = null;\n    event.nativeEvent = nativeEvent;\n    event.pointerId = nativeEvent.pointerId;\n    event.width = nativeEvent.width;\n    event.height = nativeEvent.height;\n    event.isPrimary = nativeEvent.isPrimary;\n    event.pointerType = nativeEvent.pointerType;\n    event.pressure = nativeEvent.pressure;\n    event.tangentialPressure = nativeEvent.tangentialPressure;\n    event.tiltX = nativeEvent.tiltX;\n    event.tiltY = nativeEvent.tiltY;\n    event.twist = nativeEvent.twist;\n    this._transferMouseData(event, nativeEvent);\n    this.mapPositionToPoint(event.screen, nativeEvent.clientX, nativeEvent.clientY);\n    event.global.copyFrom(event.screen);\n    event.offset.copyFrom(event.screen);\n    event.isTrusted = nativeEvent.isTrusted;\n    if (event.type === \"pointerleave\") {\n      event.type = \"pointerout\";\n    }\n    if (event.type.startsWith(\"mouse\")) {\n      event.type = event.type.replace(\"mouse\", \"pointer\");\n    }\n    if (event.type.startsWith(\"touch\")) {\n      event.type = TOUCH_TO_POINTER[event.type] || event.type;\n    }\n    return event;\n  }\n  /**\n   * Transfers base & mouse event data from the {@code nativeEvent} to the federated event.\n   * @param event\n   * @param nativeEvent\n   */\n  _transferMouseData(event, nativeEvent) {\n    event.isTrusted = nativeEvent.isTrusted;\n    event.srcElement = nativeEvent.srcElement;\n    event.timeStamp = performance.now();\n    event.type = nativeEvent.type;\n    event.altKey = nativeEvent.altKey;\n    event.button = nativeEvent.button;\n    event.buttons = nativeEvent.buttons;\n    event.client.x = nativeEvent.clientX;\n    event.client.y = nativeEvent.clientY;\n    event.ctrlKey = nativeEvent.ctrlKey;\n    event.metaKey = nativeEvent.metaKey;\n    event.movement.x = nativeEvent.movementX;\n    event.movement.y = nativeEvent.movementY;\n    event.page.x = nativeEvent.pageX;\n    event.page.y = nativeEvent.pageY;\n    event.relatedTarget = null;\n    event.shiftKey = nativeEvent.shiftKey;\n  }\n};\n_EventSystem.extension = {\n  name: \"events\",\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.CanvasSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  priority: -1\n};\n_EventSystem.defaultEventFeatures = {\n  /** Enables pointer events associated with pointer movement. */\n  move: true,\n  /** Enables global pointer move events. */\n  globalMove: true,\n  /** Enables pointer events associated with clicking. */\n  click: true,\n  /** Enables wheel events. */\n  wheel: true\n};\nlet EventSystem = _EventSystem;\nconst FederatedContainer = {\n  /**\n   * Property-based event handler for the `click` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onclick = (event) => {\n   *  //some function here that happens on click\n   * }\n   */\n  onclick: null,\n  /**\n   * Property-based event handler for the `mousedown` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmousedown = (event) => {\n   *  //some function here that happens on mousedown\n   * }\n   */\n  onmousedown: null,\n  /**\n   * Property-based event handler for the `mouseenter` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseenter = (event) => {\n   *  //some function here that happens on mouseenter\n   * }\n   */\n  onmouseenter: null,\n  /**\n   * Property-based event handler for the `mouseleave` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseleave = (event) => {\n   *  //some function here that happens on mouseleave\n   * }\n   */\n  onmouseleave: null,\n  /**\n   * Property-based event handler for the `mousemove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmousemove = (event) => {\n   *  //some function here that happens on mousemove\n   * }\n   */\n  onmousemove: null,\n  /**\n   * Property-based event handler for the `globalmousemove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onglobalmousemove = (event) => {\n   *  //some function here that happens on globalmousemove\n   * }\n   */\n  onglobalmousemove: null,\n  /**\n   * Property-based event handler for the `mouseout` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseout = (event) => {\n   *  //some function here that happens on mouseout\n   * }\n   */\n  onmouseout: null,\n  /**\n   * Property-based event handler for the `mouseover` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseover = (event) => {\n   *  //some function here that happens on mouseover\n   * }\n   */\n  onmouseover: null,\n  /**\n   * Property-based event handler for the `mouseup` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseup = (event) => {\n   *  //some function here that happens on mouseup\n   * }\n   */\n  onmouseup: null,\n  /**\n   * Property-based event handler for the `mouseupoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onmouseupoutside = (event) => {\n   *  //some function here that happens on mouseupoutside\n   * }\n   */\n  onmouseupoutside: null,\n  /**\n   * Property-based event handler for the `pointercancel` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointercancel = (event) => {\n   *  //some function here that happens on pointercancel\n   * }\n   */\n  onpointercancel: null,\n  /**\n   * Property-based event handler for the `pointerdown` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerdown = (event) => {\n   *  //some function here that happens on pointerdown\n   * }\n   */\n  onpointerdown: null,\n  /**\n   * Property-based event handler for the `pointerenter` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerenter = (event) => {\n   *  //some function here that happens on pointerenter\n   * }\n   */\n  onpointerenter: null,\n  /**\n   * Property-based event handler for the `pointerleave` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerleave = (event) => {\n   *  //some function here that happens on pointerleave\n   * }\n   */\n  onpointerleave: null,\n  /**\n   * Property-based event handler for the `pointermove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointermove = (event) => {\n   *  //some function here that happens on pointermove\n   * }\n   */\n  onpointermove: null,\n  /**\n   * Property-based event handler for the `globalpointermove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onglobalpointermove = (event) => {\n   *  //some function here that happens on globalpointermove\n   * }\n   */\n  onglobalpointermove: null,\n  /**\n   * Property-based event handler for the `pointerout` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerout = (event) => {\n   *  //some function here that happens on pointerout\n   * }\n   */\n  onpointerout: null,\n  /**\n   * Property-based event handler for the `pointerover` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerover = (event) => {\n   *  //some function here that happens on pointerover\n   * }\n   */\n  onpointerover: null,\n  /**\n   * Property-based event handler for the `pointertap` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointertap = (event) => {\n   *  //some function here that happens on pointertap\n   * }\n   */\n  onpointertap: null,\n  /**\n   * Property-based event handler for the `pointerup` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerup = (event) => {\n   *  //some function here that happens on pointerup\n   * }\n   */\n  onpointerup: null,\n  /**\n   * Property-based event handler for the `pointerupoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onpointerupoutside = (event) => {\n   *  //some function here that happens on pointerupoutside\n   * }\n   */\n  onpointerupoutside: null,\n  /**\n   * Property-based event handler for the `rightclick` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightclick = (event) => {\n   *  //some function here that happens on rightclick\n   * }\n   */\n  onrightclick: null,\n  /**\n   * Property-based event handler for the `rightdown` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightdown = (event) => {\n   *  //some function here that happens on rightdown\n   * }\n   */\n  onrightdown: null,\n  /**\n   * Property-based event handler for the `rightup` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightup = (event) => {\n   *  //some function here that happens on rightup\n   * }\n   */\n  onrightup: null,\n  /**\n   * Property-based event handler for the `rightupoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onrightupoutside = (event) => {\n   *  //some function here that happens on rightupoutside\n   * }\n   */\n  onrightupoutside: null,\n  /**\n   * Property-based event handler for the `tap` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontap = (event) => {\n   *  //some function here that happens on tap\n   * }\n   */\n  ontap: null,\n  /**\n   * Property-based event handler for the `touchcancel` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchcancel = (event) => {\n   *  //some function here that happens on touchcancel\n   * }\n   */\n  ontouchcancel: null,\n  /**\n   * Property-based event handler for the `touchend` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchend = (event) => {\n   *  //some function here that happens on touchend\n   * }\n   */\n  ontouchend: null,\n  /**\n   * Property-based event handler for the `touchendoutside` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchendoutside = (event) => {\n   *  //some function here that happens on touchendoutside\n   * }\n   */\n  ontouchendoutside: null,\n  /**\n   * Property-based event handler for the `touchmove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchmove = (event) => {\n   *  //some function here that happens on touchmove\n   * }\n   */\n  ontouchmove: null,\n  /**\n   * Property-based event handler for the `globaltouchmove` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onglobaltouchmove = (event) => {\n   *  //some function here that happens on globaltouchmove\n   * }\n   */\n  onglobaltouchmove: null,\n  /**\n   * Property-based event handler for the `touchstart` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.ontouchstart = (event) => {\n   *  //some function here that happens on touchstart\n   * }\n   */\n  ontouchstart: null,\n  /**\n   * Property-based event handler for the `wheel` event.\n   * @memberof scene.Container#\n   * @default null\n   * @example\n   * this.onwheel = (event) => {\n   *  //some function here that happens on wheel\n   * }\n   */\n  onwheel: null,\n  /**\n   * Enable interaction events for the Container. Touch, pointer and mouse\n   * @memberof scene.Container#\n   */\n  get interactive() {\n    return this.eventMode === \"dynamic\" || this.eventMode === \"static\";\n  },\n  set interactive(value) {\n    this.eventMode = value ? \"static\" : \"passive\";\n  },\n  /**\n   * @ignore\n   */\n  _internalEventMode: void 0,\n  /**\n   * Enable interaction events for the Container. Touch, pointer and mouse.\n   * There are 5 types of interaction settings:\n   * - `'none'`: Ignores all interaction events, even on its children.\n   * - `'passive'`: **(default)** Does not emit events and ignores all hit testing on itself and non-interactive children.\n   * Interactive children will still emit events.\n   * - `'auto'`: Does not emit events but is hit tested if parent is interactive. Same as `interactive = false` in v7\n   * - `'static'`: Emit events and is hit tested. Same as `interaction = true` in v7\n   * - `'dynamic'`: Emits events and is hit tested but will also receive mock interaction events fired from a ticker to\n   * allow for interaction when the mouse isn't moving\n   * @example\n   * import { Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.eventMode = 'static';\n   * sprite.on('tap', (event) => {\n   *     // Handle event\n   * });\n   * @memberof scene.Container#\n   * @since 7.2.0\n   */\n  get eventMode() {\n    return this._internalEventMode ?? EventSystem.defaultEventMode;\n  },\n  set eventMode(value) {\n    this._internalEventMode = value;\n  },\n  /**\n   * Determines if the container is interactive or not\n   * @returns {boolean} Whether the container is interactive or not\n   * @memberof scene.Container#\n   * @since 7.2.0\n   * @example\n   * import { Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.eventMode = 'static';\n   * sprite.isInteractive(); // true\n   *\n   * sprite.eventMode = 'dynamic';\n   * sprite.isInteractive(); // true\n   *\n   * sprite.eventMode = 'none';\n   * sprite.isInteractive(); // false\n   *\n   * sprite.eventMode = 'passive';\n   * sprite.isInteractive(); // false\n   *\n   * sprite.eventMode = 'auto';\n   * sprite.isInteractive(); // false\n   */\n  isInteractive() {\n    return this.eventMode === \"static\" || this.eventMode === \"dynamic\";\n  },\n  /**\n   * Determines if the children to the container can be clicked/touched\n   * Setting this to false allows PixiJS to bypass a recursive `hitTest` function\n   * @memberof scene.Container#\n   */\n  interactiveChildren: true,\n  /**\n   * Interaction shape. Children will be hit first, then this shape will be checked.\n   * Setting this will cause this shape to be checked in hit tests rather than the container's bounds.\n   * @example\n   * import { Rectangle, Sprite } from 'pixi.js';\n   *\n   * const sprite = new Sprite(texture);\n   * sprite.interactive = true;\n   * sprite.hitArea = new Rectangle(0, 0, 100, 100);\n   * @member {IHitArea}\n   * @memberof scene.Container#\n   */\n  hitArea: null,\n  /**\n   * Unlike `on` or `addListener` which are methods from EventEmitter, `addEventListener`\n   * seeks to be compatible with the DOM's `addEventListener` with support for options.\n   * @memberof scene.Container\n   * @param type - The type of event to listen to.\n   * @param listener - The listener callback or object.\n   * @param options - Listener options, used for capture phase.\n   * @example\n   * // Tell the user whether they did a single, double, triple, or nth click.\n   * button.addEventListener('click', {\n   *     handleEvent(e): {\n   *         let prefix;\n   *\n   *         switch (e.detail) {\n   *             case 1: prefix = 'single'; break;\n   *             case 2: prefix = 'double'; break;\n   *             case 3: prefix = 'triple'; break;\n   *             default: prefix = e.detail + 'th'; break;\n   *         }\n   *\n   *         console.log('That was a ' + prefix + 'click');\n   *     }\n   * });\n   *\n   * // But skip the first click!\n   * button.parent.addEventListener('click', function blockClickOnce(e) {\n   *     e.stopImmediatePropagation();\n   *     button.parent.removeEventListener('click', blockClickOnce, true);\n   * }, {\n   *     capture: true,\n   * });\n   */\n  addEventListener(type, listener, options) {\n    const capture = typeof options === \"boolean\" && options || typeof options === \"object\" && options.capture;\n    const signal = typeof options === \"object\" ? options.signal : void 0;\n    const once = typeof options === \"object\" ? options.once === true : false;\n    const context = typeof listener === \"function\" ? void 0 : listener;\n    type = capture ? `${type}capture` : type;\n    const listenerFn = typeof listener === \"function\" ? listener : listener.handleEvent;\n    const emitter = this;\n    if (signal) {\n      signal.addEventListener(\"abort\", () => {\n        emitter.off(type, listenerFn, context);\n      });\n    }\n    if (once) {\n      emitter.once(type, listenerFn, context);\n    } else {\n      emitter.on(type, listenerFn, context);\n    }\n  },\n  /**\n   * Unlike `off` or `removeListener` which are methods from EventEmitter, `removeEventListener`\n   * seeks to be compatible with the DOM's `removeEventListener` with support for options.\n   * @memberof scene.Container\n   * @param type - The type of event the listener is bound to.\n   * @param listener - The listener callback or object.\n   * @param options - The original listener options. This is required to deregister a capture phase listener.\n   */\n  removeEventListener(type, listener, options) {\n    const capture = typeof options === \"boolean\" && options || typeof options === \"object\" && options.capture;\n    const context = typeof listener === \"function\" ? void 0 : listener;\n    type = capture ? `${type}capture` : type;\n    listener = typeof listener === \"function\" ? listener : listener.handleEvent;\n    this.off(type, listener, context);\n  },\n  /**\n   * Dispatch the event on this {@link Container} using the event's {@link EventBoundary}.\n   *\n   * The target of the event is set to `this` and the `defaultPrevented` flag is cleared before dispatch.\n   * @memberof scene.Container\n   * @param e - The event to dispatch.\n   * @returns Whether the {@link FederatedEvent.preventDefault preventDefault}() method was not invoked.\n   * @example\n   * // Reuse a click event!\n   * button.dispatchEvent(clickEvent);\n   */\n  dispatchEvent(e) {\n    if (!(e instanceof FederatedEvent)) {\n      throw new Error(\"Container cannot propagate events outside of the Federated Events API\");\n    }\n    e.defaultPrevented = false;\n    e.path = null;\n    e.target = this;\n    e.manager.dispatchEvent(e);\n    return !e.defaultPrevented;\n  }\n};\nextensions.add(AccessibilitySystem);\nContainer.mixin(accessibilityTarget);\nextensions.add(EventSystem);\nContainer.mixin(FederatedContainer);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA,MAAM,cAAc,CAAC;AACrB;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAClC,IAAI,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC;AACpD,IAAI,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACpC,IAAI,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACvB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;AACzF,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AAC/E,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE;AAC1C,IAAI,MAAM,IAAI,KAAK,CAAC,qFAAqF,CAAC,CAAC;AAC3G,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC3E,IAAI,MAAM,IAAI,KAAK,CAAC,uFAAuF,CAAC,CAAC;AAC7G,GAAG;AACH;AACA,EAAE,cAAc,GAAG;AACnB,IAAI,IAAI,IAAI,CAAC,WAAW,YAAY,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;AAC1E,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,wBAAwB,GAAG;AAC7B,IAAI,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACnC,GAAG;AACH,CAAC;AACD,IAAI,WAAW,GAAG,SAAS,CAAC;AAC5B,IAAI,SAAS,GAAG,OAAO,CAAC;AACxB,IAAI,WAAW,GAAG,OAAO,CAAC;AAC1B,IAAI,cAAc,GAAG,6BAA6B,CAAC;AACnD,IAAI,YAAY,GAAG,0BAA0B,CAAC;AAC9C,IAAI,aAAa,GAAG,UAAU,CAAC;AAC/B,IAAI,WAAW,GAAG,oCAAoC,CAAC;AACvD,IAAI,YAAY,GAAG,OAAO,CAAC;AAC3B,IAAI,YAAY,GAAG,gBAAgB,CAAC;AACpC,IAAI,aAAa,GAAG,uBAAuB,CAAC;AAC5C,IAAI,eAAe,GAAG,aAAa,CAAC;AACpC,IAAI,iBAAiB,GAAG,OAAO,CAAC;AAChC,IAAI,UAAU,GAAG,aAAa,CAAC;AAC/B,IAAI,WAAW,GAAG,+BAA+B,CAAC;AAClD,IAAI,YAAY,GAAG,wBAAwB,CAAC;AAC5C,IAAI,oBAAoB,GAAG,SAAS,UAAU,EAAE;AAChD,EAAE,OAAO,OAAO,UAAU,KAAK,WAAW,IAAI,UAAU,CAAC,QAAQ,KAAK,UAAU,IAAI,OAAO,UAAU,CAAC,cAAc,KAAK,QAAQ,IAAI,UAAU,CAAC,cAAc,GAAG,CAAC,IAAI,OAAO,QAAQ,KAAK,WAAW,CAAC;AACtM,CAAC,CAAC;AACF,SAAS,WAAW,CAAC,SAAS,EAAE;AAChC,EAAE,OAAO,SAAS,KAAK,EAAE;AACzB,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACjC,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,SAAS,EAAE,EAAE;AACjB,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,cAAc,EAAE,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AAClD,IAAI,GAAG,GAAG;AACV,MAAM,SAAS,EAAE,SAAS,CAAC,SAAS;AACpC,MAAM,QAAQ,EAAE,SAAS,CAAC,QAAQ;AAClC,MAAM,cAAc,EAAE,SAAS,CAAC,cAAc,IAAI,CAAC;AACnD,KAAK,CAAC;AACN,GAAG,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACxC,IAAI,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC;AAC1B,GAAG,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE;AACvC,IAAI,GAAG,GAAG;AACV,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS;AAChC,MAAM,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAC9B,MAAM,cAAc,EAAE,KAAK,CAAC,cAAc,IAAI,CAAC;AAC/C,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AAChC,EAAE,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACrC,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AACrC,IAAI,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG;AACH,EAAE,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACnC,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AACrC,IAAI,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;AACrC,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AACvD,MAAM,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC;AAC5B,MAAM,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,WAAW,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AAC9G,MAAM,SAAS,EAAE,KAAK,CAAC,cAAc,CAAC;AACtC,MAAM,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;AAC1J,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC;AAC/B,MAAM,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC;AACxD,MAAM,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC;AACvD,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,KAAK,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC;AACtG,MAAM,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;AAClI,MAAM,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC;AACxJ,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC;AAChC,MAAM,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC;AAClC,MAAM,MAAM,EAAE,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC;AACzD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,UAAU,EAAE,KAAK,CAAC,eAAe,CAAC;AACxC,MAAM,YAAY,EAAE,KAAK,CAAC,iBAAiB,CAAC;AAC5C,MAAM,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC;AAC9B,MAAM,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC;AAClC,MAAM,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC;AAChC,MAAM,MAAM,EAAE,KAAK,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC;AAClI,KAAK;AACL,IAAI,GAAG,EAAE,KAAK;AACd,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,MAAM,EAAE,KAAK;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAC5G,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AACpF,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACxF,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC;AACtD,MAAM,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AACpD,MAAM,YAAY,GAAG,CAAC,CAAC;AACvB,MAAM,cAAc,GAAG,GAAG,CAAC;AAC3B,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,cAAc,GAAG,CAAC,GAAG,CAAC;AAC5B,MAAM,cAAc,GAAG,CAAC,GAAG,CAAC;AAC5B,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,MAAM,oBAAoB,GAAG,MAAM,qBAAqB,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,GAAG,QAAQ,EAAE;AAChD,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACvC,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;AACxC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,uBAAuB,GAAG,GAAG,CAAC;AACvC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,KAAK,EAAE;AACjD,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,qBAAqB,GAAG;AAC9B,IAAI,OAAO,IAAI,CAAC,sBAAsB,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,gBAAgB,GAAG;AACrB,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACrD,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;AAC/C,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;AAChD,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACxC,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;AAC9C,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;AAC/C,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC;AACtD,IAAI,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;AAC9C,IAAI,OAAO,CAAC,KAAK,GAAG,iDAAiD,CAAC;AACtE,IAAI,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAC5C,MAAM,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACzC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,iBAAiB,GAAG;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACxB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACpB,MAAM,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChD,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;AACrD,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC5C,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC;AACnD,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AAC3D,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;AAC7C,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAC7B,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnD,MAAM,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,sBAAsB,EAAE;AACrC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvD,MAAM,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACjF,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AAC5B,MAAM,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,MAAM;AAClD,QAAQ,IAAI,MAAM,CAAC,UAAU,EAAE;AAC/B,UAAU,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnD,UAAU,QAAQ,CAAC,UAAU,EAAE,CAAC;AAChC,UAAU,IAAI,CAAC,uBAAuB,EAAE,CAAC;AACzC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;AACrC,KAAK;AACL,GAAG;AACH;AACA,EAAE,uBAAuB,GAAG;AAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChD,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE;AAC3C,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;AACvE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,sBAAsB,EAAE;AACxD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAClF,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAC7B,MAAM,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;AACxC,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,EAAE;AACnE,QAAQ,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC1E,QAAQ,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;AACpC,OAAO;AACP,MAAM,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAChC,MAAM,IAAI,GAAG,CAAC,UAAU,EAAE;AAC1B,QAAQ,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACxC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,wBAAwB,CAAC,SAAS,EAAE;AACtC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE;AAC7D,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,UAAU,EAAE;AAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;AACxC,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAClC,OAAO;AACP,MAAM,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAC3C,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,QAAQ,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,WAAW,GAAG,qBAAqB,CAAC,cAAc,CAAC;AAC7D,IAAI,MAAM,aAAa,GAAG;AAC1B,MAAM,oBAAoB,EAAE;AAC5B,QAAQ,GAAG,WAAW;AACtB,QAAQ,GAAG,OAAO,EAAE,oBAAoB,IAAI,EAAE;AAC9C,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,KAAK,CAAC;AAC1D,IAAI,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,oBAAoB,CAAC,aAAa,CAAC;AAC3E,IAAI,IAAI,CAAC,sBAAsB,GAAG,aAAa,CAAC,oBAAoB,CAAC,qBAAqB,CAAC;AAC3F,IAAI,IAAI,aAAa,CAAC,oBAAoB,CAAC,gBAAgB,EAAE;AAC7D,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,KAAK,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE;AACpC,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnD,MAAM,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAClC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,mBAAmB,EAAE;AAC3E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAClE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;AAC1E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,SAAS,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAChD,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE;AAC3C,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;AACvE,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;AAC1C,QAAQ,IAAI,KAAK,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE;AAChD,UAAU,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AACvD,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACzD,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AAC7B,QAAQ,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,EAAE;AACrE,UAAU,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5E,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAChD,UAAU,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;AACtC,SAAS;AACT,QAAQ,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACxC,QAAQ,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;AAC1C,MAAM,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACnF,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AAC5B,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAChC,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/B,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;AACzC,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpD,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AAC7D,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC;AACvC,MAAM,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC;AACnE,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,MAAM,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC;AACxC,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC7C,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC7C,QAAQ,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAChE,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/D,QAAQ,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3D,QAAQ,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7D,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAClC,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC7C,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC7C,QAAQ,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/C,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9C,QAAQ,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACpD,QAAQ,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACtD,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,GAAG,EAAE;AACxB,IAAI,GAAG,CAAC,SAAS,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,KAAK,CAAC,gBAAgB,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjG,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE;AACvB,MAAM,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC;AACjC,MAAM,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE;AACvB,MAAM,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;AAClC,MAAM,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;AACpE,IAAI,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,SAAS,EAAE;AAC/C,MAAM,OAAO,CAAC,KAAK,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE;AACjD,MAAM,OAAO,CAAC,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,SAAS,EAAE;AACvB,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,IAAI,SAAS,CAAC,cAAc,KAAK,QAAQ,EAAE;AACjD,QAAQ,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC/C,OAAO,MAAM;AACb,QAAQ,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AAC/D,QAAQ,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,CAAC,CAAC;AACtB,QAAQ,IAAI,SAAS,CAAC,cAAc,EAAE;AACtC,UAAU,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,cAAc,CAAC;AACnD,SAAS;AACT,OAAO;AACP,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;AAC9C,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;AAC/C,MAAM,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,GAAG,uBAAuB,GAAG,aAAa,CAAC;AACvF,MAAM,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACtC,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACrD,MAAM,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AACrC,MAAM,IAAI,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAChE,QAAQ,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAC7C,OAAO,MAAM;AACb,QAAQ,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AAChD,OAAO;AACP,MAAM,IAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;AACrD,QAAQ,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;AACvD,OAAO,MAAM;AACb,QAAQ,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AAClD,OAAO;AACP,MAAM,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9D,MAAM,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9D,MAAM,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE,KAAK;AACL,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,SAAS,CAAC,uBAAuB,CAAC;AAChE,IAAI,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC,cAAc,CAAC;AACxC,IAAI,IAAI,SAAS,CAAC,eAAe,IAAI,SAAS,CAAC,eAAe,KAAK,IAAI,EAAE;AACzE,MAAM,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,eAAe,CAAC;AAC5C,KAAK,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,IAAI,SAAS,CAAC,cAAc,KAAK,IAAI,EAAE;AAC/E,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,cAAc,IAAI,SAAS,CAAC,cAAc,KAAK,IAAI,EAAE;AACvE,MAAM,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACpB,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACvC,IAAI,SAAS,CAAC,cAAc,GAAG,GAAG,CAAC;AACnC,IAAI,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AACpD,IAAI,IAAI,SAAS,CAAC,WAAW,EAAE;AAC/B,MAAM,SAAS,CAAC,cAAc,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AAC7D,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE;AAC1B,IAAI,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;AAC3C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;AACxD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;AAC1E,IAAI,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;AAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAClE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE;AACd,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE;AACd,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;AAC7C,MAAM,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACtD,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,EAAE;AACjB,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;AAC7C,MAAM,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AACzC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,CAAC,EAAE;AAChB,IAAI,IAAI,CAAC,CAAC,OAAO,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAC5D,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,CAAC,EAAE;AAClB,IAAI,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,EAAE;AAChD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAC7B,MAAM,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACjE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,uBAAuB,CAAC,OAAO,EAAE;AACnC,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACzB,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,oBAAoB,CAAC,SAAS,GAAG;AACjC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,eAAe;AACvB,CAAC,CAAC;AACF,oBAAoB,CAAC,cAAc,GAAG;AACtC;AACA;AACA;AACA;AACA,EAAE,gBAAgB,EAAE,KAAK;AACzB;AACA;AACA;AACA;AACA,EAAE,KAAK,EAAE,KAAK;AACd;AACA;AACA;AACA;AACA,EAAE,aAAa,EAAE,IAAI;AACrB;AACA;AACA;AACA;AACA,EAAE,qBAAqB,EAAE,IAAI;AAC7B,CAAC,CAAC;AACF,IAAI,mBAAmB,GAAG,oBAAoB,CAAC;AAC/C,MAAM,mBAAmB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,EAAE,KAAK;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,EAAE,IAAI;AACvB;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,EAAE,IAAI;AACtB;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,EAAE,CAAC;AACb;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,EAAE,KAAK;AAC1B;AACA;AACA;AACA;AACA,EAAE,cAAc,EAAE,IAAI;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,EAAE,QAAQ;AAC1B;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,EAAE,IAAI;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,uBAAuB,EAAE,MAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,EAAE,IAAI;AAC1B;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,EAAE,CAAC,CAAC;AACf,CAAC,CAAC;AACF,MAAM,iBAAiB,CAAC;AACxB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC9B,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAChC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC9B,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,iBAAiB,GAAG;AACtB,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC/C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC;AAC7E,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,oBAAoB,GAAG;AACzB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAC5B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE;AAC/C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC5B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC9D,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,WAAW,KAAK,OAAO,EAAE;AACrF,MAAM,OAAO;AACb,KAAK;AACL,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,aAAa,EAAE;AACtE,MAAM,OAAO,EAAE,gBAAgB,CAAC,OAAO;AACvC,MAAM,OAAO,EAAE,gBAAgB,CAAC,OAAO;AACvC,MAAM,WAAW,EAAE,gBAAgB,CAAC,WAAW;AAC/C,MAAM,SAAS,EAAE,gBAAgB,CAAC,SAAS;AAC3C,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,MAAM,EAAE;AACxB,IAAI,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,SAAS,CAAC;AACxC,IAAI,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE;AACrD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;AACnB,GAAG;AACH,CAAC;AACD,MAAM,YAAY,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAC7C,MAAM,mBAAmB,SAAS,cAAc,CAAC;AACjD,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;AAChC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;AAChD,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAClF,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,GAAG,EAAE;AACxB,IAAI,OAAO,kBAAkB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC5F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,QAAQ,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE;AACvN,IAAI,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC/C,GAAG;AACH,CAAC;AACD,MAAM,qBAAqB,SAAS,mBAAmB,CAAC;AACxD,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE;AAC/F,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;AAC5D,GAAG;AACH,CAAC;AACD,MAAM,mBAAmB,SAAS,mBAAmB,CAAC;AACtD,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAC5B,GAAG;AACH,CAAC;AACD,mBAAmB,CAAC,eAAe,GAAG,CAAC,CAAC;AACxC,mBAAmB,CAAC,cAAc,GAAG,CAAC,CAAC;AACvC,mBAAmB,CAAC,cAAc,GAAG,CAAC,CAAC;AACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC;AAC/B,MAAM,eAAe,GAAG,IAAI,KAAK,EAAE,CAAC;AACpC,MAAM,gBAAgB,GAAG,IAAI,KAAK,EAAE,CAAC;AACrC,MAAM,aAAa,CAAC;AACpB;AACA;AACA;AACA,EAAE,WAAW,CAAC,UAAU,EAAE;AAC1B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;AACvC,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACvC,IAAI,IAAI,CAAC,YAAY,GAAG;AACxB,MAAM,YAAY,EAAE,EAAE;AACtB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC/C,IAAI,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;AACtC,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AACrC,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvD,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnE,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACvE,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAClC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACnC,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACjC,MAAM,EAAE;AACR,MAAM,QAAQ,EAAE,CAAC;AACjB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;AACpE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE;AACzB,IAAI,CAAC,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACjC,IAAI,CAAC,CAAC,6BAA6B,GAAG,KAAK,CAAC;AAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtD,QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,CAAC,+CAA+C,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACvE,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;AAChB,IAAI,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,sBAAsB,CAAC;AAC5E,IAAI,MAAM,EAAE,GAAG,OAAO,GAAG,sBAAsB,GAAG,kBAAkB,CAAC;AACrE,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC;AACjC,MAAM,IAAI,CAAC,UAAU;AACrB,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS;AAC/B,MAAM,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/B,MAAM,IAAI,CAAC,SAAS;AACpB,MAAM,IAAI,CAAC,UAAU;AACrB,KAAK,CAAC;AACN,IAAI,OAAO,YAAY,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE;AACrB,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;AACnB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;AAC1C,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,MAAM,CAAC,CAAC,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACxC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,CAAC,kBAAkB,IAAI,CAAC,CAAC,6BAA6B;AACjE,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,SAAS,CAAC;AAC/B,IAAI,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,CAAC,kBAAkB,IAAI,CAAC,CAAC,6BAA6B;AAC/D,MAAM,OAAO;AACb,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,CAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACvD,MAAM,CAAC,CAAC,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACxC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,CAAC,kBAAkB,IAAI,CAAC,CAAC,6BAA6B;AACjE,QAAQ,OAAO;AACf,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC,uBAAuB,EAAE;AACvD,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;AAC5B,MAAM,OAAO;AACb,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,CAAC;AACpC,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AACvD,IAAI,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAClD,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAChC,QAAQ,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACpC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,MAAM,EAAE;AAC1B,IAAI,MAAM,eAAe,GAAG,CAAC,MAAM,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,KAAK,MAAM,KAAK,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;AACjG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;AAC/E,OAAO;AACP,MAAM,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC1C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;AAC9B,IAAI,OAAO,eAAe,CAAC;AAC3B,GAAG;AACH,EAAE,oBAAoB,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,KAAK,EAAE;AAC5F,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;AAC7C,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,aAAa,CAAC,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE;AAC1E,MAAM,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,aAAa,CAAC,mBAAmB,IAAI,aAAa,CAAC,QAAQ,EAAE;AACrE,MAAM,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC9C,MAAM,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACrD,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClC,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB;AACnD,UAAU,KAAK;AACf,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC,SAAS;AACtE,UAAU,QAAQ;AAClB,UAAU,MAAM;AAChB,UAAU,OAAO;AACjB,UAAU,MAAM,IAAI,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC;AACpD,SAAS,CAAC;AACV,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE;AAC/E,YAAY,SAAS;AACrB,WAAW;AACX,UAAU,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;AAC9D,UAAU,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,EAAE;AACrD,YAAY,IAAI,aAAa;AAC7B,cAAc,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC/D,YAAY,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1C,WAAW;AACX,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;AAC5C,YAAY,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;AAC1C,UAAU,YAAY,GAAG,IAAI,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;AAC7D,IAAI,MAAM,mBAAmB,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;AAC9D,IAAI,IAAI,mBAAmB,IAAI,mBAAmB;AAClD,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACvD,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC;AAC9C,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,YAAY;AACpB,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;AAC/B,IAAI,IAAI,iBAAiB,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE;AACrG,MAAM,OAAO,mBAAmB,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;AACxE,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE;AACnF,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,aAAa,CAAC,SAAS,KAAK,SAAS,IAAI,SAAS,KAAK,SAAS,EAAE;AAC1E,MAAM,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,aAAa,CAAC,mBAAmB,IAAI,aAAa,CAAC,QAAQ,EAAE;AACrE,MAAM,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC9C,MAAM,MAAM,gBAAgB,GAAG,QAAQ,CAAC;AACxC,MAAM,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACrD,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClC,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB;AAC/C,UAAU,KAAK;AACf,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC,SAAS;AACtE,UAAU,gBAAgB;AAC1B,UAAU,MAAM;AAChB,UAAU,OAAO;AACjB,SAAS,CAAC;AACV,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE;AAC/E,YAAY,SAAS;AACrB,WAAW;AACX,UAAU,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;AAC9D,UAAU,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa;AACnD,YAAY,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1C,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;AAC7D,IAAI,MAAM,mBAAmB,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;AAC9D,IAAI,IAAI,iBAAiB,IAAI,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE;AAC9D,MAAM,OAAO,mBAAmB,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,cAAc,CAAC,GAAG,EAAE;AACtB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC;AACjD,GAAG;AACH,EAAE,iBAAiB,CAAC,SAAS,EAAE;AAC/B,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;AAC5F,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,SAAS,KAAK,MAAM,EAAE;AACxC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE;AAC7E,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,SAAS,EAAE,QAAQ,EAAE;AAClC,IAAI,IAAI,SAAS,CAAC,OAAO,EAAE;AAC3B,MAAM,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AACxE,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE;AAC/E,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE;AACvD,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzD,QAAQ,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5C,QAAQ,IAAI,MAAM,CAAC,aAAa,EAAE;AAClC,UAAU,MAAM,mBAAmB,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACrF,UAAU,IAAI,CAAC,mBAAmB,EAAE;AACpC,YAAY,OAAO,IAAI,CAAC;AACxB,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,SAAS,EAAE,QAAQ,EAAE;AACjC,IAAI,IAAI,SAAS,CAAC,OAAO,EAAE;AAC3B,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,SAAS,EAAE,aAAa,EAAE;AAClC,MAAM,SAAS,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AACxE,MAAM,OAAO,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE;AACxB,IAAI,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,aAAa,EAAE,EAAE;AAC1C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,MAAM,UAAU,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AAC7G,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,SAAS,EAAE;AACtC,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACrC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,IAAI,EAAE;AACvB,IAAI,IAAI,EAAE,IAAI,YAAY,qBAAqB,CAAC,EAAE;AAClD,MAAM,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAC9E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE;AACnC,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AAC1C,KAAK,MAAM,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,EAAE;AACrE,MAAM,MAAM,aAAa,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AAC3C,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,GAAG,WAAW,GAAG,WAAW,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;AACtE,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,IAAI,EAAE;AACvB,IAAI,IAAI,EAAE,IAAI,YAAY,qBAAqB,CAAC,EAAE;AAClD,MAAM,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAC9E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACpC,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;AACrC,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC;AACzE,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;AACvE,IAAI,IAAI,YAAY,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC,MAAM,EAAE;AACxE,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,KAAK,WAAW,GAAG,UAAU,GAAG,YAAY,CAAC;AAC5E,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AACzE,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACjD,MAAM,IAAI,OAAO;AACjB,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACjD,MAAM,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AACjD,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;AACpF,QAAQ,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;AACrD,QAAQ,OAAO,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AACnF,UAAU,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;AACvD,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AACxC,UAAU,IAAI,OAAO;AACrB,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACxD,UAAU,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACvD,SAAS;AACT,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,MAAM,EAAE;AAChC,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,WAAW,GAAG,WAAW,GAAG,aAAa,CAAC;AAC/E,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC5D,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AACnD,MAAM,IAAI,OAAO;AACjB,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AACnD,MAAM,IAAI,kBAAkB,GAAG,SAAS,EAAE,MAAM,CAAC;AACjD,MAAM,OAAO,kBAAkB,IAAI,kBAAkB,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAClF,QAAQ,IAAI,kBAAkB,KAAK,CAAC,CAAC,MAAM;AAC3C,UAAU,MAAM;AAChB,QAAQ,kBAAkB,GAAG,kBAAkB,CAAC,MAAM,CAAC;AACvD,OAAO;AACP,MAAM,MAAM,eAAe,GAAG,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;AACnG,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;AACrE,QAAQ,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;AACrD,QAAQ,OAAO,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AACrH,UAAU,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;AACvD,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AACxC,UAAU,IAAI,OAAO;AACrB,YAAY,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACxD,UAAU,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACvD,SAAS;AACT,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC;AAC1B,IAAI,MAAM,wBAAwB,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC;AACzE,IAAI,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AAC3F,IAAI,wBAAwB,IAAI,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE;AACnC,MAAM,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACjG,MAAM,wBAAwB,IAAI,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACjG,MAAM,wBAAwB,IAAI,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACrE,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AACjC,IAAI,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;AAChD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,IAAI,EAAE;AACvB,IAAI,IAAI,EAAE,IAAI,YAAY,qBAAqB,CAAC,EAAE;AAClD,MAAM,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAC9E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,CAAC;AACzE,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACzC,IAAI,IAAI,OAAO;AACf,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO;AACjC,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC;AACrC,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;AACjE,IAAI,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;AACjD,IAAI,OAAO,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAC9E,MAAM,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;AACnD,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AACpC,MAAM,IAAI,OAAO;AACjB,QAAQ,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACpD,MAAM,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACnD,KAAK;AACL,IAAI,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;AAChD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,IAAI,EAAE;AACtB,IAAI,IAAI,EAAE,IAAI,YAAY,qBAAqB,CAAC,EAAE;AAClD,MAAM,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAC9E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,IAAI,YAAY,CAAC,WAAW,EAAE;AAClC,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,KAAK,OAAO,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC;AACjF,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;AACzE,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;AAC9E,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACnC,MAAM,IAAI,OAAO;AACjB,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACjD,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC;AAClF,MAAM,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC;AACnD,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAChF,QAAQ,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;AACrD,QAAQ,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AACtC,QAAQ,IAAI,OAAO;AACnB,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACtD,QAAQ,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACrD,OAAO;AACP,MAAM,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC;AACtC,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAC/B,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,IAAI,EAAE;AACrB,IAAI,IAAI,EAAE,IAAI,YAAY,qBAAqB,CAAC,EAAE;AAClD,MAAM,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAC9E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAClC,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE;AACnC,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AACxC,KAAK,MAAM,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,EAAE;AACrE,MAAM,MAAM,aAAa,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AAC3C,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;AACnE,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/F,IAAI,IAAI,WAAW,GAAG,WAAW,CAAC;AAClC,IAAI,IAAI,WAAW,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAChE,MAAM,IAAI,aAAa,GAAG,WAAW,CAAC;AACtC,MAAM,OAAO,aAAa,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AACzE,QAAQ,CAAC,CAAC,aAAa,GAAG,aAAa,CAAC;AACxC,QAAQ,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;AACjD,QAAQ,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE;AACvC,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;AAClD,SAAS,MAAM,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,EAAE;AACzE,UAAU,MAAM,aAAa,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AAC/C,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,CAAC;AACpF,SAAS;AACT,QAAQ,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;AAC7C,OAAO;AACP,MAAM,OAAO,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5D,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,KAAK;AACL,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5D,MAAM,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;AACtC,MAAM,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACrD,QAAQ,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG;AACnD,UAAU,UAAU,EAAE,CAAC;AACvB,UAAU,MAAM,EAAE,UAAU,CAAC,MAAM;AACnC,UAAU,SAAS,EAAE,GAAG;AACxB,SAAS,CAAC;AACV,OAAO;AACP,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpE,MAAM,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,GAAG,GAAG,YAAY,CAAC,SAAS,GAAG,GAAG,EAAE;AAC3F,QAAQ,EAAE,YAAY,CAAC,UAAU,CAAC;AAClC,OAAO,MAAM;AACb,QAAQ,YAAY,CAAC,UAAU,GAAG,CAAC,CAAC;AACpC,OAAO;AACP,MAAM,YAAY,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AAC9C,MAAM,YAAY,CAAC,SAAS,GAAG,GAAG,CAAC;AACnC,MAAM,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;AAClD,MAAM,IAAI,UAAU,CAAC,WAAW,KAAK,OAAO,EAAE;AAC9C,QAAQ,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;AACtD,QAAQ,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,GAAG,YAAY,GAAG,OAAO,CAAC,CAAC;AAC/E,OAAO,MAAM,IAAI,UAAU,CAAC,WAAW,KAAK,OAAO,EAAE;AACrD,QAAQ,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAC9C,OAAO;AACP,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACnD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,EAAE,IAAI,YAAY,qBAAqB,CAAC,EAAE;AAClD,MAAM,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAC9E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/F,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,IAAI,aAAa,GAAG,WAAW,CAAC;AACtC,MAAM,OAAO,aAAa,EAAE;AAC5B,QAAQ,CAAC,CAAC,aAAa,GAAG,aAAa,CAAC;AACxC,QAAQ,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;AACjD,QAAQ,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE;AACvC,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;AAClD,SAAS,MAAM,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,EAAE;AACzE,UAAU,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,KAAK,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,CAAC;AACrF,SAAS;AACT,QAAQ,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;AAC7C,OAAO;AACP,MAAM,OAAO,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,IAAI,EAAE;AACjB,IAAI,IAAI,EAAE,IAAI,YAAY,mBAAmB,CAAC,EAAE;AAChD,MAAM,IAAI,CAAC,6DAA6D,CAAC,CAAC;AAC1E,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,eAAe,EAAE;AACrC,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,aAAa,EAAE;AACvD,QAAQ,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AAC3C,OAAO,MAAM;AACb,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,OAAO,aAAa,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACzC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACtC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/B,IAAI,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AACzC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;AAC/B,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAClG,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,IAAI,EAAE;AACzB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/B,IAAI,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AACzC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;AAC/B,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAChE,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE;AAChC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;AAC5D,IAAI,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AACzC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7C,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACtC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/B,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,KAAK,EAAE,CAAC;AAC7C,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE;AAC1B,IAAI,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,IAAI,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,IAAI,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,IAAI,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;AAC5B,IAAI,IAAI,EAAE,IAAI,YAAY,qBAAqB,IAAI,EAAE,YAAY,qBAAqB,CAAC;AACvF,MAAM,OAAO;AACb,IAAI,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1B,IAAI,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,IAAI,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,IAAI,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AACtC,IAAI,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,EAAE,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACpD,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1B,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1B,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE;AAC1B,IAAI,IAAI,EAAE,IAAI,YAAY,mBAAmB,IAAI,EAAE,YAAY,mBAAmB,CAAC;AACnF,MAAM,OAAO;AACb,IAAI,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,IAAI,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,IAAI,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,IAAI,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,IAAI,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE;AACrB,IAAI,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,IAAI,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACpC,IAAI,EAAE,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACrC,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACxB,IAAI,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,IAAI,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACxB,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC1B,IAAI,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,EAAE,EAAE;AACnB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE;AAC7C,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG;AAC3C,QAAQ,oBAAoB,EAAE,EAAE;AAChC,QAAQ,cAAc,EAAE,EAAE;AAC1B,QAAQ,UAAU,EAAE,IAAI;AACxB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,WAAW,EAAE;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AAC1C,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACjF,IAAI,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC;AAClC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;AAC/B,IAAI,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC;AACnC,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACtB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AACxB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,KAAK,EAAE;AACnB,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI;AAC9B,MAAM,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;AAC3F,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;AAC1C,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AAC1C,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAE;AAC5B,IAAI,MAAM,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,SAAS;AAClB,MAAM,OAAO;AACb,IAAI,IAAI,IAAI,IAAI,SAAS,EAAE;AAC3B,MAAM,IAAI,SAAS,CAAC,IAAI;AACxB,QAAQ,CAAC,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AACzE,MAAM,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B,EAAE,CAAC,EAAE,EAAE;AAC5F,QAAQ,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI;AAC7B,UAAU,CAAC,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9E,QAAQ,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACtD,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,gBAAgB,GAAG;AACzB,EAAE,UAAU,EAAE,aAAa;AAC3B,EAAE,QAAQ,EAAE,WAAW;AACvB,EAAE,eAAe,EAAE,kBAAkB;AACrC,EAAE,SAAS,EAAE,aAAa;AAC1B,EAAE,WAAW,EAAE,eAAe;AAC9B,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,MAAM,aAAa,CAAC;AACzC;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,mBAAmB,GAAG,cAAc,IAAI,UAAU,CAAC;AAC5D,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC;AAC3D,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AAChD,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACnC,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC9B,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,YAAY,GAAG;AACxB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,SAAS;AACxB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,aAAa,CAAC,oBAAoB,EAAE,EAAE;AACzE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,KAAK;AACnC,QAAQ,IAAI,GAAG,KAAK,YAAY,EAAE;AAClC,UAAU,IAAI,CAAC,YAAY,CAAC,sBAAsB,GAAG,KAAK,CAAC;AAC3D,SAAS;AACT,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC5B,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,gBAAgB,GAAG;AAChC,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjD,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,aAAa,CAAC,iBAAiB,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC;AACrE,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,YAAY,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;AACxE,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,UAAU,EAAE;AAC/B,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC,CAAC;AAC/B,IAAI,IAAI,WAAW,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,UAAU,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,YAAY,eAAe,EAAE;AAClF,MAAM,WAAW,GAAG,KAAK,CAAC;AAC1B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AACtC,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,QAAQ,OAAO,KAAK;AAC1B,QAAQ,KAAK,QAAQ;AACrB,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;AACjD,WAAW;AACX,UAAU,MAAM;AAChB,QAAQ,KAAK,UAAU;AACvB,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC;AACtB,UAAU,MAAM;AAChB,QAAQ,KAAK,QAAQ;AACrB,UAAU,IAAI,WAAW,EAAE;AAC3B,YAAY,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxD,WAAW;AACX,UAAU,MAAM;AAChB,OAAO;AACP,KAAK,MAAM,IAAI,WAAW,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE;AAC1H,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC;AAClC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,WAAW,EAAE;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;AACpE,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;AAC7D,IAAI,IAAI,IAAI,CAAC,kBAAkB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE;AAC3D,MAAM,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,EAAE,YAAY,IAAI,WAAW,CAAC,CAAC;AAClF,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,WAAW,CAAC,cAAc,EAAE,CAAC;AACrC,OAAO;AACP,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACrC,MAAM,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;AACxF,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,WAAW,EAAE;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;AAC3B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;AACpE,IAAI,YAAY,CAAC,YAAY,EAAE,CAAC;AAChC,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;AACvE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,WAAW,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;AACpE,IAAI,IAAI,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AACpC,IAAI,IAAI,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3E,MAAM,MAAM,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,MAAM,KAAK,IAAI,CAAC,UAAU,GAAG,SAAS,GAAG,EAAE,CAAC;AAChE,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;AACvE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,MAAM,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC;AAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,WAAW,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;AACpE,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;AACvE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7D,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,WAAW,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK;AAC5B,MAAM,OAAO;AACb,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;AACpE,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,OAAO,EAAE;AAC5B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;AAC9B,IAAI,YAAY,CAAC,UAAU,GAAG,OAAO,CAAC;AACtC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,GAAG;AACH;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC/C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,YAAY,CAAC,iBAAiB,EAAE,CAAC;AACrC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AACxC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,UAAU,CAAC,SAAS,CAAC,gBAAgB,EAAE;AACjD,QAAQ,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC;AACxC,QAAQ,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;AACrC,OAAO,MAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC7C,QAAQ,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;AACpC,MAAM,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrF,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACjF,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACrF,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACpF,MAAM,UAAU,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACxE,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACnF,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC/E,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACjF,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AAClF,MAAM,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACtE,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACpC,QAAQ,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAClF,QAAQ,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAC9E,QAAQ,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACjF,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;AAC5D,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,IAAI;AACnB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAChD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,YAAY,CAAC,oBAAoB,EAAE,CAAC;AACxC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;AACxC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,UAAU,CAAC,SAAS,CAAC,gBAAgB,EAAE;AACjD,QAAQ,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;AACpC,QAAQ,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;AACjC,OAAO,MAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC7C,QAAQ,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;AAC/B,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;AACpC,MAAM,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACxF,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpF,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACxF,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACvF,MAAM,UAAU,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAC3E,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACtF,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAClF,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACpF,MAAM,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACrF,MAAM,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACzE,MAAM,IAAI,IAAI,CAAC,mBAAmB,EAAE;AACpC,QAAQ,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrF,QAAQ,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACjF,QAAQ,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpF,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE;AAClC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,GAAG;AACzF,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;AAClC,MAAM,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;AACpC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,GAAG,EAAE,CAAC;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AACrD,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,oBAAoB,CAAC;AAC5F,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,oBAAoB,CAAC;AAC7F,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,uBAAuB,CAAC,KAAK,EAAE;AACjC,IAAI,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAChC,IAAI,IAAI,IAAI,CAAC,mBAAmB,IAAI,KAAK,YAAY,UAAU,EAAE;AACjE,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACrE,QAAQ,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9C,QAAQ,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;AAC/C,UAAU,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,WAAW;AAChD,UAAU,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;AAC5B,QAAQ,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,WAAW,EAAE;AACpD,UAAU,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC;AACtF,SAAS;AACT,QAAQ,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;AAC9C,UAAU,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;AAC3C,QAAQ,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;AAC/C,UAAU,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;AAC5C,QAAQ,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;AAC9C,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC1B,QAAQ,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;AAC9C,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC1B,QAAQ,IAAI,OAAO,KAAK,CAAC,WAAW,KAAK,WAAW;AACpD,UAAU,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;AACtC,QAAQ,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,WAAW;AAClD,UAAU,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;AAClD,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,WAAW;AACjD,UAAU,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC;AAC9C,QAAQ,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,WAAW;AAC9C,UAAU,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AAC1B,QAAQ,IAAI,OAAO,KAAK,CAAC,kBAAkB,KAAK,WAAW;AAC3D,UAAU,KAAK,CAAC,kBAAkB,GAAG,CAAC,CAAC;AACvC,QAAQ,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;AAC/C,UAAU,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AACvD,QAAQ,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,WAAW;AAC/C,UAAU,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AACvD,QAAQ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;AAClC,QAAQ,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAChC,QAAQ,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrC,OAAO;AACP,KAAK,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,KAAK,YAAY,UAAU,KAAK,CAAC,IAAI,CAAC,qBAAqB,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC,YAAY,CAAC,CAAC,EAAE;AACtJ,MAAM,MAAM,SAAS,GAAG,KAAK,CAAC;AAC9B,MAAM,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,WAAW;AACpD,QAAQ,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;AACnC,MAAM,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAChD,QAAQ,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5B,MAAM,IAAI,OAAO,SAAS,CAAC,MAAM,KAAK,WAAW;AACjD,QAAQ,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC7B,MAAM,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAChD,QAAQ,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5B,MAAM,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAChD,QAAQ,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5B,MAAM,IAAI,OAAO,SAAS,CAAC,WAAW,KAAK,WAAW;AACtD,QAAQ,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;AACxC,MAAM,IAAI,OAAO,SAAS,CAAC,SAAS,KAAK,WAAW;AACpD,QAAQ,SAAS,CAAC,SAAS,GAAG,gBAAgB,CAAC;AAC/C,MAAM,IAAI,OAAO,SAAS,CAAC,QAAQ,KAAK,WAAW;AACnD,QAAQ,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC;AACjC,MAAM,IAAI,OAAO,SAAS,CAAC,KAAK,KAAK,WAAW;AAChD,QAAQ,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5B,MAAM,IAAI,OAAO,SAAS,CAAC,kBAAkB,KAAK,WAAW;AAC7D,QAAQ,SAAS,CAAC,kBAAkB,GAAG,CAAC,CAAC;AACzC,MAAM,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;AACpC,MAAM,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACvC,KAAK,MAAM;AACX,MAAM,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnC,KAAK;AACL,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,WAAW,EAAE;AACnC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;AACvC,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAChD,IAAI,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AACtC,IAAI,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AACtC,IAAI,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AACtC,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAC5C,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;AACpF,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;AACpC,IAAI,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;AAClC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE;AACtC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;AAC/B,IAAI,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;AACpC,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAC5C,IAAI,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AACpC,IAAI,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AACtC,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAC5C,IAAI,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;AAChD,IAAI,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;AAC1C,IAAI,KAAK,CAAC,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;AAC9D,IAAI,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AACpC,IAAI,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AACpC,IAAI,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AACpC,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;AACpF,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAC5C,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE;AACvC,MAAM,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACxC,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACxC,MAAM,KAAK,CAAC,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC;AAC9D,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,KAAK,EAAE,WAAW,EAAE;AACzC,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAC5C,IAAI,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;AAC9C,IAAI,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AACxC,IAAI,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;AAClC,IAAI,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AACtC,IAAI,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;AACtC,IAAI,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACxC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;AACzC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;AACzC,IAAI,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACxC,IAAI,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACxC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC;AAC7C,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC;AAC7C,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;AACrC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC;AACrC,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;AAC/B,IAAI,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;AAC1C,GAAG;AACH,CAAC,CAAC;AACF,YAAY,CAAC,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,QAAQ,EAAE,CAAC,CAAC;AACd,CAAC,CAAC;AACF,YAAY,CAAC,oBAAoB,GAAG;AACpC;AACA,EAAE,IAAI,EAAE,IAAI;AACZ;AACA,EAAE,UAAU,EAAE,IAAI;AAClB;AACA,EAAE,KAAK,EAAE,IAAI;AACb;AACA,EAAE,KAAK,EAAE,IAAI;AACb,CAAC,CAAC;AACF,IAAI,WAAW,GAAG,YAAY,CAAC;AAC/B,MAAM,kBAAkB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,EAAE,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,EAAE,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,EAAE,IAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,EAAE,IAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,EAAE,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,EAAE,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,EAAE,IAAI;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,EAAE,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,EAAE,IAAI;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,EAAE,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,EAAE,IAAI;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,EAAE,IAAI;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,EAAE,IAAI;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,EAAE,IAAI;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,EAAE,IAAI;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,EAAE,IAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,EAAE,IAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,EAAE,IAAI;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,EAAE,IAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,EAAE,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,EAAE,IAAI;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,EAAE,IAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,EAAE,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,EAAE,IAAI;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,EAAE,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,EAAE,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,EAAE,IAAI;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,EAAE,IAAI;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,EAAE,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,EAAE,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,EAAE,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,EAAE,IAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,EAAE,IAAI;AACf;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC;AACvE,GAAG;AACH,EAAE,IAAI,WAAW,CAAC,KAAK,EAAE;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,QAAQ,GAAG,SAAS,CAAC;AAClD,GAAG;AACH;AACA;AACA;AACA,EAAE,kBAAkB,EAAE,KAAK,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,kBAAkB,IAAI,WAAW,CAAC,gBAAgB,CAAC;AACnE,GAAG;AACH,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE;AACvB,IAAI,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;AACvE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,EAAE,IAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,EAAE,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE;AAC5C,IAAI,MAAM,OAAO,GAAG,OAAO,OAAO,KAAK,SAAS,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC;AAC9G,IAAI,MAAM,MAAM,GAAG,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AACzE,IAAI,MAAM,IAAI,GAAG,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC;AAC7E,IAAI,MAAM,OAAO,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC;AACvE,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AAC7C,IAAI,MAAM,UAAU,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC;AACxF,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAC7C,QAAQ,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC/C,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE;AAC/C,IAAI,MAAM,OAAO,GAAG,OAAO,OAAO,KAAK,SAAS,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC;AAC9G,IAAI,MAAM,OAAO,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC;AACvE,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AAC7C,IAAI,QAAQ,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC;AAChF,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACtC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,EAAE,CAAC,YAAY,cAAc,CAAC,EAAE;AACxC,MAAM,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;AAC/F,KAAK;AACL,IAAI,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAC/B,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAClB,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,IAAI,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAI,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC;AAC/B,GAAG;AACH,CAAC,CAAC;AACF,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACpC,SAAS,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;AACrC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC5B,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC"}