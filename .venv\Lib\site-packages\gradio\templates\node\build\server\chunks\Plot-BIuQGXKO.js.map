{"version": 3, "file": "Plot-BIuQGXKO.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Plot.js"], "sourcesContent": ["import { create_ssr_component, validate_component, missing_component } from \"svelte/internal\";\nimport { k as Empty, P as Plot$1 } from \"./client.js\";\nimport { createEventDispatcher } from \"svelte\";\nconst Plot = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let _value;\n  let { colors = [] } = $$props;\n  let { show_label } = $$props;\n  let { theme_mode } = $$props;\n  let { caption } = $$props;\n  let { bokeh_version } = $$props;\n  let { show_actions_button } = $$props;\n  let { gradio } = $$props;\n  let { x_lim = null } = $$props;\n  let { _selectable } = $$props;\n  let PlotComponent = null;\n  let _type = value?.type;\n  let loaded_plotly_css = false;\n  const dispatch = createEventDispatcher();\n  const plotTypeMapping = {\n    plotly: () => import(\"./PlotlyPlot.js\"),\n    bokeh: () => import(\"./BokehPlot.js\"),\n    altair: () => import(\"./AltairPlot.js\"),\n    matplotlib: () => import(\"./MatplotlibPlot.js\")\n  };\n  let loadedPlotTypeMapping = {};\n  const is_browser = typeof window !== \"undefined\";\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.colors === void 0 && $$bindings.colors && colors !== void 0)\n    $$bindings.colors(colors);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.caption === void 0 && $$bindings.caption && caption !== void 0)\n    $$bindings.caption(caption);\n  if ($$props.bokeh_version === void 0 && $$bindings.bokeh_version && bokeh_version !== void 0)\n    $$bindings.bokeh_version(bokeh_version);\n  if ($$props.show_actions_button === void 0 && $$bindings.show_actions_button && show_actions_button !== void 0)\n    $$bindings.show_actions_button(show_actions_button);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.x_lim === void 0 && $$bindings.x_lim && x_lim !== void 0)\n    $$bindings.x_lim(x_lim);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      if (value !== _value) {\n        let type = value?.type;\n        if (type !== _type) {\n          PlotComponent = null;\n        }\n        if (type && type in plotTypeMapping && is_browser) {\n          if (loadedPlotTypeMapping[type]) {\n            PlotComponent = loadedPlotTypeMapping[type];\n          } else {\n            plotTypeMapping[type]().then((module) => {\n              PlotComponent = module.default;\n              loadedPlotTypeMapping[type] = PlotComponent;\n            });\n          }\n        }\n        _value = value;\n        _type = type;\n        dispatch(\"change\");\n      }\n    }\n    $$rendered = `${value && PlotComponent ? `${validate_component(PlotComponent || missing_component, \"svelte:component\").$$render(\n      $$result,\n      {\n        value,\n        colors,\n        theme_mode,\n        show_label,\n        caption,\n        bokeh_version,\n        show_actions_button,\n        gradio,\n        _selectable,\n        x_lim,\n        loaded_plotly_css\n      },\n      {\n        loaded_plotly_css: ($$value) => {\n          loaded_plotly_css = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}` : `${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true, size: \"large\" }, {}, {\n      default: () => {\n        return `${validate_component(Plot$1, \"PlotIcon\").$$render($$result, {}, {}, {})}`;\n      }\n    })}`}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  Plot as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC5E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC;AAC1B,EAAE,IAAI,iBAAiB,GAAG,KAAK,CAAC;AAChC,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,MAAM,eAAe,GAAG;AAC1B,IAAI,MAAM,EAAE,MAAM,OAAO,0BAAiB,CAAC;AAC3C,IAAI,KAAK,EAAE,MAAM,OAAO,yBAAgB,CAAC;AACzC,IAAI,MAAM,EAAE,MAAM,OAAO,0BAAiB,CAAC;AAC3C,IAAI,UAAU,EAAE,MAAM,OAAO,8BAAqB,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,IAAI,qBAAqB,GAAG,EAAE,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,mBAAmB,IAAI,mBAAmB,KAAK,KAAK,CAAC;AAChH,IAAI,UAAU,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AACxD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,IAAI,KAAK,KAAK,MAAM,EAAE;AAC5B,QAAQ,IAAI,IAAI,GAAG,KAAK,EAAE,IAAI,CAAC;AAC/B,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;AAC5B,UAAU,aAAa,GAAG,IAAI,CAAC;AAC/B,SAAS;AACT,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,eAAe,IAAI,UAAU,EAAE;AAC3D,UAAU,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;AAC3C,YAAY,aAAa,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;AACxD,WAAW,MAAM;AACjB,YAAY,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AACrD,cAAc,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC;AAC7C,cAAc,qBAAqB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;AAC1D,aAAa,CAAC,CAAC;AACf,WAAW;AACX,SAAS;AACT,QAAQ,MAAM,GAAG,KAAK,CAAC;AACvB,QAAQ,KAAK,GAAG,IAAI,CAAC;AACrB,QAAQ,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC3B,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,kBAAkB,CAAC,aAAa,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACnI,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,OAAO;AACf,QAAQ,aAAa;AACrB,QAAQ,mBAAmB;AAC3B,QAAQ,MAAM;AACd,QAAQ,WAAW;AACnB,QAAQ,KAAK;AACb,QAAQ,iBAAiB;AACzB,OAAO;AACP,MAAM;AACN,QAAQ,iBAAiB,EAAE,CAAC,OAAO,KAAK;AACxC,UAAU,iBAAiB,GAAG,OAAO,CAAC;AACtC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AAC9G,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1F,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACX,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}