import requests
import json

# 简单测试API
url = "http://localhost:8000/chat/completions"

data = {
    "model": "ui-tars-1.5-7b",
    "messages": [
        {
            "role": "user", 
            "content": "Hello"
        }
    ],
    "max_tokens": 50
}

print("🧪 测试API...")
try:
    response = requests.post(url, json=data, timeout=60)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"回复: {result['choices'][0]['message']['content']}")
    else:
        print(f"错误: {response.text}")
except Exception as e:
    print(f"异常: {e}")
